import React, { useState, useEffect } from 'react';
import { Link, useParams } from 'react-router-dom';
import axios from 'axios';
import StrategyDropdown from './StrategyDropdown';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend,
  elements,
} from 'chart.js';
import { Line } from 'react-chartjs-2';

ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend
);

const Strategy = () => {
  const { strategyName } = useParams();
  const [currentStrategy, setCurrentStrategy] = useState(strategyName || 'Alle (Durchschnitt)');
  const [strategyData, setStrategyData] = useState(null);
  const [loading, setLoading] = useState(true);

  // Chart state
  const [selectedAttribute, setSelectedAttribute] = useState('');
  const [startMonth, setStartMonth] = useState('');
  const [startYear, setStartYear] = useState('');
  const [endMonth, setEndMonth] = useState('');
  const [endYear, setEndYear] = useState('');

  // Settings state
  const [showSettings, setShowSettings] = useState(false);
  const [rescaleTo100, setRescaleTo100] = useState(false);

  useEffect(() => {
    fetchStrategyData(currentStrategy);
  }, [currentStrategy]);

  const fetchStrategyData = async (strategy) => {
    try {
      setLoading(true);
      const response = await axios.get(`/api/strategy/${strategy}/`);
      setStrategyData(response.data);

      // Initialize dropdowns with first available data
      if (response.data && Object.keys(response.data).length > 0) {
        const firstMonth = Object.keys(response.data)[0];
        const firstStrategy = Object.keys(response.data[firstMonth])[0];
        const attributes = Object.keys(response.data[firstMonth][firstStrategy]);

        if (attributes.length > 0 && !selectedAttribute) {
          setSelectedAttribute(attributes[0]);
        }

        // Set default date range
        const months = Object.keys(response.data).sort();
        if (months.length > 0) {
          const [startYearMonth, startMonthNum] = parseMonthYear(months[0]);
          const [endYearMonth, endMonthNum] = parseMonthYear(months[months.length - 1]);

          if (!startMonth) {
            setStartMonth(startMonthNum);
            setStartYear(startYearMonth);
          }
          if (!endMonth) {
            setEndMonth(endMonthNum);
            setEndYear(endYearMonth);
          }
        }
      }
    } catch (error) {
      console.error('Error fetching strategy data:', error);
    } finally {
      setLoading(false);
    }
  };

  // Helper function to parse month-year string like "2025 - 04"
  const parseMonthYear = (monthYearStr) => {
    const [year, month] = monthYearStr.split(' - ');
    return [year, month];
  };

  // Helper function to create month-year string
  // const createMonthYearStr = (year, month) => {
  //   return `${year} - ${month.padStart(2, '0')}`;
  // };

  // Get available attributes from data
  const getAvailableAttributes = () => {
    if (!strategyData || Object.keys(strategyData).length === 0) return [];

    const firstMonth = Object.keys(strategyData)[0];
    const firstStrategy = Object.keys(strategyData[firstMonth])[0];
    return Object.keys(strategyData[firstMonth][firstStrategy]);
  };

  // Get available months and years
  const getAvailableMonthsYears = () => {
    if (!strategyData) return { months: [], years: [] };

    const monthsYears = Object.keys(strategyData).map(parseMonthYear);
    const months = [...new Set(monthsYears.map(([year, month]) => month))].sort();
    const years = [...new Set(monthsYears.map(([year, month]) => year))].sort();

    return { months, years };
  };

  // Set end date to current month/year
  const setToNow = () => {
    const now = new Date();
    const currentMonth = (now.getMonth() + 1).toString().padStart(2, '0');
    const currentYear = now.getFullYear().toString();

    setEndMonth(currentMonth);
    setEndYear(currentYear);
  };

  // Set start date to first available month/year
  const setToStart = () => {
    if (!strategyData || Object.keys(strategyData).length === 0) return;
    
    const months = Object.keys(strategyData).sort();
    if (months.length > 0) {
      const [startYearMonth, startMonthNum] = parseMonthYear(months[0]);
      setStartMonth(startMonthNum);
      setStartYear(startYearMonth);
    }
  };

  // Get chart data filtered by selected strategy
  const getChartData = () => {
    if (!strategyData || !selectedAttribute || !startMonth || !startYear || !endMonth || !endYear) {
      return { labels: [], datasets: [] };
    }

    // Filter data by date range
    const filteredData = {};
    Object.keys(strategyData).forEach(monthYear => {
      const [year, month] = parseMonthYear(monthYear);
      const yearNum = parseInt(year);
      const monthNum = parseInt(month);
      const startYearNum = parseInt(startYear);
      const startMonthNum = parseInt(startMonth);
      const endYearNum = parseInt(endYear);
      const endMonthNum = parseInt(endMonth);

      const isInRange = (yearNum > startYearNum || (yearNum === startYearNum && monthNum >= startMonthNum)) &&
                       (yearNum < endYearNum || (yearNum === endYearNum && monthNum <= endMonthNum));

      if (isInRange) {
        // Filter by selected strategy
        const monthData = strategyData[monthYear];
        if (currentStrategy === "Alle (Durchschnitt)") {
          filteredData[monthYear] = monthData;
        } else if (monthData[currentStrategy]) {
          filteredData[monthYear] = { [currentStrategy]: monthData[currentStrategy] };
        }
      }
    });

    // Sort months chronologically
    const sortedMonths = Object.keys(filteredData).sort((a, b) => {
      const [yearA, monthA] = parseMonthYear(a);
      const [yearB, monthB] = parseMonthYear(b);
      return new Date(yearA, monthA - 1) - new Date(yearB, monthB - 1);
    });

    // Create labels (formatted month names)
    const labels = sortedMonths.map(monthYear => {
      const [year, month] = parseMonthYear(monthYear);
      const date = new Date(year, month - 1);
      return date.toLocaleDateString('en-US', { month: 'short', year: 'numeric' });
    });

    // Calculate coverage for each month
    const coverage = sortedMonths.map(monthYear => {
      let totalCoverage = 0;
      let strategyCount = 0;

      Object.values(filteredData[monthYear]).forEach(strategy => {
        if (strategy[selectedAttribute]) {
          const monthTotal = Object.values(strategy[selectedAttribute]).reduce((sum, value) => sum + value, 0);
          totalCoverage += monthTotal;
          strategyCount++;
        }
      });

      return strategyCount > 0 ? totalCoverage / strategyCount : 0;
    });

    // Get all unique currencies/subcategories for the selected attribute
    const allSubcategories = new Set();
    sortedMonths.forEach(monthYear => {
      Object.values(filteredData[monthYear]).forEach(strategy => {
        if (strategy[selectedAttribute]) {
          Object.keys(strategy[selectedAttribute]).forEach(subcat => {
            allSubcategories.add(subcat);
          });
        }
      });
    });

    // Generate colors for each line
    const colors = [
      'rgb(255, 99, 132)',
      'rgb(54, 162, 235)',
      'rgb(255, 205, 86)',
      'rgb(75, 192, 192)',
      'rgb(153, 102, 255)',
      'rgb(255, 159, 64)',
      'rgb(199, 199, 199)',
      'rgb(83, 102, 147)',
      'rgb(255, 87, 51)',
      'rgb(46, 204, 113)',
      'rgb(155, 89, 182)',
      'rgb(241, 196, 15)',
      'rgb(230, 126, 34)',
      'rgb(52, 152, 219)',
      'rgb(231, 76, 60)',
      'rgb(26, 188, 156)',
      'rgb(142, 68, 173)',
      'rgb(39, 174, 96)',
      'rgb(192, 57, 43)',
      'rgb(41, 128, 185)',
    ];

    // Create datasets for each subcategory
    const subcategoriesWithAverage = Array.from(allSubcategories).map(subcategory => {
      const data = sortedMonths.map(monthYear => {
        let total = 0;
        let count = 0;
        Object.values(filteredData[monthYear]).forEach(strategy => {
          if (strategy[selectedAttribute] && strategy[selectedAttribute][subcategory] !== undefined) {
            total += strategy[selectedAttribute][subcategory];
            count++;
          }
        });
        return count > 0 ? total / count : 0;
      });

      const average = data.reduce((sum, value) => sum + value, 0) / data.length;
      return { subcategory, data, average };
    });

    // Apply rescaling if enabled
    if (rescaleTo100) {
      // For each month, rescale all subcategories so they sum to 100%
      sortedMonths.forEach((monthYear, monthIndex) => {
        const monthTotal = subcategoriesWithAverage.reduce((sum, { data }) => sum + data[monthIndex], 0);
        if (monthTotal > 0) {
          subcategoriesWithAverage.forEach(({ data }) => {
            data[monthIndex] = (data[monthIndex] / monthTotal) * 100;
          });
        }
      });
    }

    // Sort by average value in descending order
    subcategoriesWithAverage.sort((a, b) => b.average - a.average);

    const datasets = subcategoriesWithAverage.map(({ subcategory, data }, index) => {
      return {
        label: subcategory,
        data: data,
        borderColor: colors[index % colors.length],
        backgroundColor: colors[index % colors.length], // + '20' to make them a dark shade
        tension: 0.1,
      };
    });

    return { labels, datasets, coverage };
  };

  // Custom plugin to display coverage at the top
  const coveragePlugin = {
    id: 'coverageDisplay',
    afterDraw: (chart) => {
      const { ctx, chartArea, scales } = chart;

      // Get coverage data from chart's custom property
      const coverage = chart.config.options.coverageData;

      if (!coverage || coverage.length === 0) return;

      ctx.save();
      ctx.font = '12px Arial';
      ctx.textAlign = 'center';

      coverage.forEach((coverageValue, index) => {
        // Determine color based on coverage percentage
        let color = 'rgba(0, 0, 0, 0.7)'; // Black for >= 90%
        if (coverageValue < 80) {
          color = 'rgba(220, 53, 69, 0.7)'; // Red for < 80% 
        } else if (coverageValue < 90) {
          color = 'rgba(255, 193, 7, 0.7)'; // Yellow for < 90%
        }

        ctx.fillStyle = color;

        // Calculate x position for each label
        const xScale = scales.x;
        const xPos = xScale.getPixelForTick(index);
        const yPos = chartArea.top - 20;

        // Draw coverage percentage
        ctx.fillText(`${coverageValue.toFixed(0)}%`, xPos, yPos);
      });

      ctx.restore();
    }
  };

  const chartOptions = {
    responsive: true,
    plugins: {
      legend: {
        position: 'bottom',
        labels: {
          usePointStyle: true,
          pointStyle: 'circle',
          boxWidth: 12,
          boxHeight: 8,
        }
      },
      title: {
        display: true,
        text: `${selectedAttribute} Distribution Over Time`,
        font: {
          size: 16
        },
        padding: {
          bottom: 50
        }
      },
      tooltip: {
        callbacks: {
          label: function(context) {
            return ` ${context.dataset.label}: ${context.parsed.y.toFixed(1)}%`;
          }
        }
      },
      coverageDisplay: true,
    },
    layout: {
      padding: {
        top: 0, // Add padding at top for coverage display
      }
    },
    scales: {
      y: {
        beginAtZero: true,
        ticks: {
          callback: function(value) {
            return value + '%';
          }
        },
        title: {
          display: true,
          text: 'Percentage (%)'
        }
      },
      x: {
        title: {
          display: true,
          text: 'Month'
        }
      }
    },
    elements: {
      point: {
        radius: 4
      }
    }
  };

  const availableAttributes = getAvailableAttributes();
  const { months, years } = getAvailableMonthsYears();
  const chartData = getChartData();

  // Update chart options with current coverage data
  const chartOptionsWithCoverage = {
    ...chartOptions,
    coverageData: chartData.coverage
  };

  return (
    <div>
      
      <div className="content-page">
        <div className='home-arrow-container'>
          <Link to="/" className="back-to-home-arrow">
            <i className='bi bi-arrow-left'></i> Home
          </Link>
        </div>
        <StrategyDropdown
          currentStrategy={currentStrategy}
          onStrategyChange={setCurrentStrategy}
        />
        <p className="section_title">Strategy Insights</p>
        <hr></hr>
        {loading ? (
          <div className="text-center">
            <div className="spinner-border" role="status">
              <span className="visually-hidden">Loading...</span>
            </div>
          </div>
        ) : (
          <div className="strategy-content">
            {strategyData && (
              <div className="chart-container">
                {/* Chart Controls */}
                <div className="chart-controls">
                  {/* Attribute Dropdown */}
                  <div className="control-group attribute-dropdown">
                    <label className="control-label">Attribute</label>
                    <select
                      className="form-select"
                      value={selectedAttribute}
                      onChange={(e) => setSelectedAttribute(e.target.value)}
                    >
                      <option value="">Select Attribute</option>
                      {availableAttributes.map(attr => (
                        <option key={attr} value={attr}>{attr}</option>
                      ))}
                    </select>
                  </div>

                  {/* Date Controls */}
                  <div className="date-controls">
                    {/* Start Date */}
                    <div className="date-group">
                      <label className="control-label">Start Date</label>
                      <div className="start-date-container">
                        <select
                          value={startMonth}
                          onChange={(e) => setStartMonth(e.target.value)}
                        >
                          <option value="">Month</option>
                          {months.map(month => (
                            <option key={month} value={month}>
                              {new Date(2000, month - 1).toLocaleDateString('en-US', { month: 'short' })}
                            </option>
                          ))}
                        </select>
                        <select
                          value={startYear}
                          onChange={(e) => setStartYear(e.target.value)}
                        >
                          <option value="">Year</option>
                          {years.map(year => (
                            <option key={year} value={year}>{year}</option>
                          ))}
                        </select>
                        <button
                          className="start-button"
                          onClick={setToStart}
                        >
                          Start
                        </button>
                      </div>
                    </div>

                    {/* End Date */}
                    <div className="date-group">
                      <label className="control-label">End Date</label>
                      <div className="end-date-container">
                        <select
                          value={endMonth}
                          onChange={(e) => setEndMonth(e.target.value)}
                        >
                          <option value="">Month</option>
                          {months.map(month => (
                            <option key={month} value={month}>
                              {new Date(2000, month - 1).toLocaleDateString('en-US', { month: 'short' })}
                            </option>
                          ))}
                        </select>
                        <select
                          value={endYear}
                          onChange={(e) => setEndYear(e.target.value)}
                        >
                          <option value="">Year</option>
                          {years.map(year => (
                            <option key={year} value={year}>{year}</option>
                          ))}
                        </select>
                        <button
                          className="now-button"
                          onClick={setToNow}
                        >
                          Now
                        </button>
                        <button
                          className="settings-button"
                          onClick={() => setShowSettings(true)}
                          title="Chart Settings"
                        >
                          <i className="bi bi-gear"></i>
                        </button>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Chart */}
                {selectedAttribute && chartData.datasets.length > 0 ? (
                  <div className="chart-wrapper" style={{ height: '400px' }}>
                    <Line data={chartData} options={chartOptionsWithCoverage} plugins={[coveragePlugin]} />
                  </div>
                ) : (
                  <div className="alert alert-info">
                    Please select an attribute and ensure data is available for the selected date range.
                  </div>
                )}
              </div>
            )}
          </div>
        )}

        {/* Settings Modal */}
        {showSettings && (
          <div className="modal show d-block" tabIndex="-1" style={{ backgroundColor: 'rgba(0,0,0,0.5)' }}>
            <div className="modal-dialog">
              <div className="modal-content">
                <div className="modal-header">
                  <h5 className="modal-title">Chart Settings</h5>
                  <button
                    type="button"
                    className="btn-close"
                    onClick={() => setShowSettings(false)}
                    aria-label="Close"
                  ></button>
                </div>
                <div className="modal-body">
                  <div className="form-check form-switch">
                    <input
                      className="form-check-input"
                      type="checkbox"
                      id="rescaleToggle"
                      checked={rescaleTo100}
                      onChange={(e) => setRescaleTo100(e.target.checked)}
                    />
                    <label className="form-check-label" htmlFor="rescaleToggle">
                      Rescale to 100%
                    </label>
                    <div className="form-text">
                      When enabled, percentages for each month will be rescaled so they sum to 100%.
                      Coverage display remains unchanged.
                    </div>
                  </div>
                </div>
                <div className="modal-footer">
                  <button
                    type="button"
                    className="btn btn-secondary"
                    onClick={() => setShowSettings(false)}
                  >
                    Close
                  </button>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default Strategy;
