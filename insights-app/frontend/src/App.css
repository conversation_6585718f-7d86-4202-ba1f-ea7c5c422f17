/* General stuff */
*{
    scroll-behavior: smooth;
}

:root {
  --corp-red: #a41551;
  --corp-red-lighted: #bb175c;
  --corp-red-darkened: #750f39;
  --corp-blue: #002940;
  --corp-blue-lighted: #003f63;
  --corp-blue-darkened: #001927;
}

html{
    overflow-x: hidden;
}

body {
    overflow-x: hidden;
    font-size: 18px;
    font-family: Arial, Helvetica, sans-serif;
    font-weight: 400;
    background-color: rgb(183, 193, 203);
    margin: 0;
}
a{
    text-decoration: none;
}

p::selection, b::selection, h1::selection, h2::selection, h3::selection, h4::selection, small::selection, a::selection, label::selection {
    background-color: var(--corp-red);
    color: white;
}

/* ### Home CSS ### */
.home-wrapper {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    height: 100vh;
    text-align: center;
}
.container {
    max-width: 600px;
}
.clickable-box {
    width: 250px;
    height: 250px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    border: 2px solid var(--corp-blue);
    border-radius: 10px;
    text-decoration: none;
    color: var(--corp-blue);
    font-size: 1.5rem;
    font-weight: bold;
    transition: background 0.3s, transform 0.2s;
}
.clickable-box:hover {
    background: rgba(255, 255, 255, 0.2);
    color: var(--corp-blue);
}
.clickable-box i {
    margin-bottom: 10px;
    transition: 0.3s;
}
.clickable-box:hover i {
    transform: scale(1.05);
}

.welcome {
    font-size: 44px;
    color: black;
    margin-bottom: 0px;
}
.welcome_sub {
    font-size: 19px;
    margin-bottom: 50px;
}
.section_title {
    color: var(--corp-red);
    text-transform: uppercase;
    margin-bottom: 25px;
    font-size: 15px;
}

/* ### CSS Used Across pages */
a.back-to-home-arrow {
  font-size: 16px;
  color: black;
  transition: 0.2s;
}

a.back-to-home-arrow:hover {
  margin-left: -5px;
  color: var(--corp-blue);
}

/* ### Content Page ### */
.content-page {
    margin: calc(25px + 1.5vh) calc(25px + 1.5vw);
}

/* Dropdown Styling - Match Bootstrap exactly */
.dropdown {
    position: relative;
    display: inline-block;
}

.dropdown-toggle {
    font-size: 2rem;
    font-weight: bold;
    cursor: pointer;
    margin-bottom: 0;
    background: none;
    border: none;
    padding: 0;
    color: black;
}

.dropdown-toggle::after {
    display: none; /* Hide default Bootstrap arrow, we use custom span */
}

.dropdown-menu {
    position: absolute;
    top: 100%;
    left: 0;
    z-index: 1000;
    display: block;
    min-width: 10rem;
    padding: 0;
    margin: 10px 0 0 0;
    font-size: 1rem;
    color: #212529;
    text-align: left;
    list-style: none;
    background-color: rgba(255, 255, 255, 0.6);
    background-clip: padding-box;
    border: none;
    border-radius: 0.375rem;
    backdrop-filter: blur(10px);
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

.dropdown-menu.show {
    display: block;
}

.dropdown-menu li {
    list-style: none;
}

.dropdown-item {
    display: block;
    width: 100%;
    padding: 10px 20px;
    clear: both;
    font-weight: bold;
    font-size: 2rem;
    color: black;
    text-align: inherit;
    text-decoration: none;
    white-space: nowrap;
    background-color: transparent;
    border: 0;
    cursor: pointer;
}

.dropdown-item:hover,
.dropdown-item:focus {
    color: black;
    background-color: rgba(200, 200, 200, 0.5);
}

.dropdown-item.active,
.dropdown-item:active {
    color: #fff;
    text-decoration: none;
    background-color: #0d6efd;
}

/* Style the heading */
h1 {
    font-size: 2rem;
    font-weight: bold;
    cursor: pointer;
}

/* Utility classes */
.d-flex {
    display: flex;
}

.gap-4 {
    gap: 2rem;
}

.text-center {
    text-align: center;
}

.fs-1 {
    font-size: 2.5rem;
}

.visually-hidden {
    position: absolute !important;
    width: 1px !important;
    height: 1px !important;
    padding: 0 !important;
    margin: -1px !important;
    overflow: hidden !important;
    clip: rect(0, 0, 0, 0) !important;
    white-space: nowrap !important;
    border: 0 !important;
}

.spinner-border {
    display: inline-block;
    width: 2rem;
    height: 2rem;
    vertical-align: text-bottom;
    border: 0.25em solid currentColor;
    border-right-color: transparent;
    border-radius: 50%;
    animation: spinner-border 0.75s linear infinite;
}

@keyframes spinner-border {
    to {
        transform: rotate(360deg);
    }
}

/* ### Strategy Chart CSS ### */
.chart-container {
    background: white;
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    margin-top: 20px;
}

.chart-controls {
    padding: 20px 0;
    display: flex;
    align-items: flex-end;
    gap: 20px;
    flex-wrap: wrap;
}

.control-group {
    display: flex;
    flex-direction: column;
}

.control-label {
    font-size: 11px;
    font-weight: 600;
    color: var(--corp-blue);
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin-bottom: 8px;
}

.attribute-dropdown {
    flex: 0 0 200px;
}

.date-controls {
    display: flex;
    gap: 20px;
    flex: 1;
    justify-content: flex-end;
}

.date-group {
    display: flex;
    flex-direction: column;
}

.date-container {
    display: flex;
    background: white;
    border: 1px solid #000;
    border-radius: 6px;
    overflow: hidden;
}

.date-container select {
    border: none;
    background: white;
    padding: 8px 12px;
    font-size: 14px;
    outline: none;
    border-right: 1px solid #ddd;
}

.date-container select:last-child {
    border-right: none;
}

.date-container select:focus {
    background: #f8f9fa;
}

.start-date-container {
    display: flex;
    background: white;
    border: 1px solid #000;
    border-radius: 6px;
    overflow: hidden;
}

.start-date-container select {
    border: none;
    background: white;
    padding: 8px 12px;
    font-size: 14px;
    outline: none;
    border-right: 1px solid #ddd;
}

.start-date-container .start-button {
    border: none;
    background: var(--corp-blue);
    color: white;
    padding: 8px 16px;
    font-size: 12px;
    font-weight: 600;
    cursor: pointer;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    transition: 0.2s;
}

.start-date-container .start-button:hover {
    background: var(--corp-blue-darkened);
}

.end-date-container {
    display: flex;
    background: white;
    border: 1px solid #000;
    border-radius: 6px;
    overflow: hidden;
}

.end-date-container select {
    border: none;
    background: white;
    padding: 8px 12px;
    font-size: 14px;
    outline: none;
    border-right: 1px solid #ddd;
}

.end-date-container .now-button {
    border: none;
    background: var(--corp-blue);
    color: white;
    padding: 8px 16px;
    font-size: 12px;
    font-weight: 600;
    cursor: pointer;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    transition: 0.2s;
}

.end-date-container .now-button:hover {
    background: var(--corp-blue-darkened);
}

.end-date-container .settings-button {
    border: none;
    background: #6c757d;
    color: white;
    padding: 8px 12px;
    font-size: 14px;
    cursor: pointer;
    transition: 0.2s;
    display: flex;
    align-items: center;
    justify-content: center;
}

.end-date-container .settings-button:hover {
    background: #5a6268;
}

/* Settings Modal Styles */
.settings-modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1050;
}

.settings-modal-container {
    background: white;
    border-radius: 8px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
    max-width: 500px;
    width: 90%;
    max-height: 90vh;
    overflow: hidden;
}

.settings-modal-header {
    background-color: var(--corp-blue);
    color: white;
    padding: 1rem 1.5rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.settings-modal-title {
    font-size: 1.25rem;
    font-weight: 600;
    margin: 0;
}

.settings-modal-close {
    background: none;
    border: none;
    color: white;
    font-size: 1.5rem;
    cursor: pointer;
    padding: 0;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 4px;
    transition: background-color 0.2s;
}

.settings-modal-close:hover {
    background-color: rgba(255, 255, 255, 0.1);
}

.settings-modal-body {
    padding: 1.5rem;
    background-color: #f8f9fa;
}

.settings-option {
    margin-bottom: 1rem;
}

.settings-switch-container {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.settings-option .form-check {
    margin-bottom: 0;
    padding-left: 2.5rem;
}

.settings-option .form-check-label {
    font-size: 1rem;
    color: var(--corp-blue);
    cursor: pointer;
    margin-left: 0.5rem;
}

.settings-option .form-check-input {
    cursor: pointer;
    width: 2rem;
    height: 1rem;
}

.settings-option .form-check-input:checked {
    background-color: var(--corp-blue);
    border-color: var(--corp-blue);
}

.settings-option .form-check-input:focus {
    border-color: var(--corp-blue-lighted);
    box-shadow: 0 0 0 0.25rem rgba(0, 41, 64, 0.25);
}

.settings-description {
    font-size: 0.875rem;
    color: #6c757d;
    line-height: 1.4;
    margin-left: 0;
    padding-left: 2.5rem;
}

.chart-controls .form-select {
    background: white;
    border: 1px solid #000;
    border-radius: 6px;
    padding: 8px 12px;
    font-size: 14px;
}

.chart-controls .form-select:focus {
    border-color: var(--corp-red);
    box-shadow: 0 0 0 0.2rem rgba(164, 21, 81, 0.25);
}

.chart-wrapper {
    position: relative;
    margin-top: 20px;
    padding: 10px;
    background: white;
    border-radius: 6px;
}

.chart-wrapper canvas {
    max-height: 400px !important;
}

@media (max-width: 768px) {
    .chart-controls {
        flex-direction: column;
        gap: 15px;
        align-items: stretch;
    }

    .attribute-dropdown {
        flex: none;
    }

    .date-controls {
        flex-direction: column;
        gap: 15px;
        justify-content: stretch;
    }

    .date-container,
    .end-date-container {
        width: 100%;
    }

    .date-container select,
    .end-date-container select {
        flex: 1;
    }
}
