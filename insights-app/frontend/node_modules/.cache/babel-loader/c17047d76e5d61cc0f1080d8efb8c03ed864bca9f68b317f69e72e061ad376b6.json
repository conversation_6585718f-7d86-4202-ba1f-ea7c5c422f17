{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Desktop/Coding/BVS/CrystalBall/insights-app/frontend/src/components/Strategy.js\",\n  _s2 = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { HashRouter, useParams } from 'react-router-dom';\nimport axios from 'axios';\nimport StrategyDropdown from './StrategyDropdown';\nimport { Chart as ChartJS, CategoryScale, LinearScale, PointElement, LineElement, Title, Tooltip, Legend } from 'chart.js';\nimport { Line } from 'react-chartjs-2';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nChartJS.register(CategoryScale, LinearScale, PointElement, LineElement, Title, Tooltip, Legend);\nconst Strategy = () => {\n  _s2();\n  var _s = $RefreshSig$();\n  const {\n    strategyName\n  } = useParams();\n  const [currentStrategy, setCurrentStrategy] = useState(strategyName || 'Alle');\n  const [strategyData, setStrategyData] = useState(null);\n  const [loading, setLoading] = useState(true);\n\n  // Chart state\n  const [selectedAttribute, setSelectedAttribute] = useState('');\n  const [startMonth, setStartMonth] = useState('');\n  const [startYear, setStartYear] = useState('');\n  const [endMonth, setEndMonth] = useState('');\n  const [endYear, setEndYear] = useState('');\n  useEffect(() => {\n    fetchStrategyData(currentStrategy);\n  }, [currentStrategy]);\n  const fetchStrategyData = async strategy => {\n    try {\n      setLoading(true);\n      const response = await axios.get(`/api/strategy/${strategy}/`);\n      setStrategyData(response.data);\n\n      // Initialize dropdowns with first available data\n      if (response.data && Object.keys(response.data).length > 0) {\n        const firstMonth = Object.keys(response.data)[0];\n        const firstStrategy = Object.keys(response.data[firstMonth])[0];\n        const attributes = Object.keys(response.data[firstMonth][firstStrategy]);\n        if (attributes.length > 0 && !selectedAttribute) {\n          setSelectedAttribute(attributes[0]);\n        }\n\n        // Set default date range\n        const months = Object.keys(response.data).sort();\n        if (months.length > 0) {\n          const [startYearMonth, startMonthNum] = parseMonthYear(months[0]);\n          const [endYearMonth, endMonthNum] = parseMonthYear(months[months.length - 1]);\n          if (!startMonth) {\n            setStartMonth(startMonthNum);\n            setStartYear(startYearMonth);\n          }\n          if (!endMonth) {\n            setEndMonth(endMonthNum);\n            setEndYear(endYearMonth);\n          }\n        }\n      }\n    } catch (error) {\n      console.error('Error fetching strategy data:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Helper function to parse month-year string like \"2025 - 04\"\n  const parseMonthYear = monthYearStr => {\n    const [year, month] = monthYearStr.split(' - ');\n    return [year, month];\n  };\n\n  // Helper function to create month-year string\n  const createMonthYearStr = (year, month) => {\n    return `${year} - ${month.padStart(2, '0')}`;\n  };\n\n  // Get available attributes from data\n  const getAvailableAttributes = () => {\n    if (!strategyData || Object.keys(strategyData).length === 0) return [];\n    const firstMonth = Object.keys(strategyData)[0];\n    const firstStrategy = Object.keys(strategyData[firstMonth])[0];\n    return Object.keys(strategyData[firstMonth][firstStrategy]);\n  };\n\n  // Get available months and years\n  const getAvailableMonthsYears = () => {\n    if (!strategyData) return {\n      months: [],\n      years: []\n    };\n    const monthsYears = Object.keys(strategyData).map(parseMonthYear);\n    const months = [...new Set(monthsYears.map(([year, month]) => month))].sort();\n    const years = [...new Set(monthsYears.map(([year, month]) => year))].sort();\n    return {\n      months,\n      years\n    };\n  };\n\n  // Set end date to current month/year\n  const setToNow = () => {\n    const now = new Date();\n    const currentMonth = (now.getMonth() + 1).toString().padStart(2, '0');\n    const currentYear = now.getFullYear().toString();\n    setEndMonth(currentMonth);\n    setEndYear(currentYear);\n  };\n\n  // Set start date to first available month/year\n  const setToStart = () => {\n    if (!strategyData || Object.keys(strategyData).length === 0) return;\n    const months = Object.keys(strategyData).sort();\n    if (months.length > 0) {\n      const [startYearMonth, startMonthNum] = parseMonthYear(months[0]);\n      setStartMonth(startMonthNum);\n      setStartYear(startYearMonth);\n    }\n  };\n\n  // Process data for chart\n  const getChartData = () => {\n    if (!strategyData || !selectedAttribute || !startMonth || !startYear || !endMonth || !endYear) {\n      return {\n        labels: [],\n        datasets: []\n      };\n    }\n\n    // Filter data by date range\n    const filteredData = {};\n    Object.keys(strategyData).forEach(monthYear => {\n      const [year, month] = parseMonthYear(monthYear);\n      const yearNum = parseInt(year);\n      const monthNum = parseInt(month);\n      const startYearNum = parseInt(startYear);\n      const startMonthNum = parseInt(startMonth);\n      const endYearNum = parseInt(endYear);\n      const endMonthNum = parseInt(endMonth);\n      const isInRange = (yearNum > startYearNum || yearNum === startYearNum && monthNum >= startMonthNum) && (yearNum < endYearNum || yearNum === endYearNum && monthNum <= endMonthNum);\n      if (isInRange) {\n        filteredData[monthYear] = strategyData[monthYear];\n      }\n    });\n\n    // Sort months chronologically\n    const sortedMonths = Object.keys(filteredData).sort((a, b) => {\n      const [yearA, monthA] = parseMonthYear(a);\n      const [yearB, monthB] = parseMonthYear(b);\n      return new Date(yearA, monthA - 1) - new Date(yearB, monthB - 1);\n    });\n\n    // Create labels (formatted month names)\n    const labels = sortedMonths.map(monthYear => {\n      const [year, month] = parseMonthYear(monthYear);\n      const date = new Date(year, month - 1);\n      return date.toLocaleDateString('en-US', {\n        month: 'short',\n        year: 'numeric'\n      });\n    });\n\n    // Get all unique currencies/subcategories for the selected attribute\n    const allSubcategories = new Set();\n    sortedMonths.forEach(monthYear => {\n      Object.values(filteredData[monthYear]).forEach(strategy => {\n        if (strategy[selectedAttribute]) {\n          Object.keys(strategy[selectedAttribute]).forEach(subcat => {\n            allSubcategories.add(subcat);\n          });\n        }\n      });\n    });\n\n    // Generate colors for each line\n    const colors = ['rgb(255, 99, 132)', 'rgb(54, 162, 235)', 'rgb(255, 205, 86)', 'rgb(75, 192, 192)', 'rgb(153, 102, 255)', 'rgb(255, 159, 64)', 'rgb(199, 199, 199)', 'rgb(83, 102, 147)'];\n\n    // Create datasets for each subcategory\n    const datasets = Array.from(allSubcategories).map((subcategory, index) => {\n      const data = sortedMonths.map(monthYear => {\n        // Aggregate data across all strategies for this month and subcategory\n        let total = 0;\n        let count = 0;\n        Object.values(filteredData[monthYear]).forEach(strategy => {\n          if (strategy[selectedAttribute] && strategy[selectedAttribute][subcategory] !== undefined) {\n            total += strategy[selectedAttribute][subcategory];\n            count++;\n          }\n        });\n        return count > 0 ? total / count : 0; // Average across strategies\n      });\n      return {\n        label: subcategory,\n        data: data,\n        borderColor: colors[index % colors.length],\n        backgroundColor: colors[index % colors.length] + '20',\n        tension: 0.1\n      };\n    });\n    return {\n      labels,\n      datasets\n    };\n  };\n  const chartOptions = {\n    responsive: true,\n    plugins: {\n      legend: {\n        position: 'bottom'\n      },\n      title: {\n        display: true,\n        text: `${selectedAttribute} Distribution Over Time`\n      }\n    },\n    scales: {\n      y: {\n        beginAtZero: true,\n        ticks: {\n          callback: function (value) {\n            return value + '%';\n          }\n        },\n        title: {\n          display: true,\n          text: 'Percentage (%)'\n        }\n      },\n      x: {\n        title: {\n          display: true,\n          text: 'Month'\n        }\n      }\n    }\n  };\n  const availableAttributes = getAvailableAttributes();\n  const {\n    months,\n    years\n  } = getAvailableMonthsYears();\n  const chartData = getChartData();\n\n  // Custom Select Component\n  const CustomSelect = ({\n    value,\n    onChange,\n    options,\n    placeholder,\n    className\n  }) => {\n    _s();\n    const [isOpen, setIsOpen] = useState(false);\n    const [selectedLabel, setSelectedLabel] = useState('');\n    useEffect(() => {\n      const selected = options.find(opt => opt.value === value);\n      setSelectedLabel(selected ? selected.label : '');\n    }, [value, options]);\n    const handleSelect = optionValue => {\n      onChange({\n        target: {\n          value: optionValue\n        }\n      });\n      setIsOpen(false);\n    };\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: `custom-select ${className || ''}`,\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"custom-select-trigger\",\n        onClick: () => setIsOpen(!isOpen),\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          children: selectedLabel || placeholder\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 284,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          className: `custom-select-arrow ${isOpen ? 'open' : ''}`,\n          children: \"\\u25BE\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 285,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 280,\n        columnNumber: 9\n      }, this), isOpen && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"custom-select-options\",\n        children: options.map(option => /*#__PURE__*/_jsxDEV(\"div\", {\n          className: `custom-select-option ${value === option.value ? 'selected' : ''}`,\n          onClick: () => handleSelect(option.value),\n          children: option.label\n        }, option.value, false, {\n          fileName: _jsxFileName,\n          lineNumber: 290,\n          columnNumber: 15\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 288,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 279,\n      columnNumber: 7\n    }, this);\n  };\n  _s(CustomSelect, \"Qix6Dba7Ye+UztNUDZf6vAetnG4=\");\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"content-page\",\n    children: [/*#__PURE__*/_jsxDEV(StrategyDropdown, {\n      currentStrategy: currentStrategy,\n      onStrategyChange: setCurrentStrategy\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 306,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n      className: \"section_title\",\n      children: \"Strategy Insights\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 310,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"hr\", {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 311,\n      columnNumber: 7\n    }, this), loading ? /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"text-center\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"spinner-border\",\n        role: \"status\",\n        children: /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"visually-hidden\",\n          children: \"Loading...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 315,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 314,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 313,\n      columnNumber: 9\n    }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"strategy-content\",\n      children: strategyData && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"chart-container\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"chart-controls\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"control-group attribute-dropdown\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"control-label\",\n              children: \"Attribute\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 326,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(CustomSelect, {\n              value: selectedAttribute,\n              onChange: e => setSelectedAttribute(e.target.value),\n              options: [{\n                value: '',\n                label: 'Select Attribute'\n              }, ...availableAttributes.map(attr => ({\n                value: attr,\n                label: attr\n              }))],\n              placeholder: \"Select Attribute\",\n              className: \"form-select\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 327,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 325,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"date-controls\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"date-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"control-label\",\n                children: \"Start Date\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 343,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"start-date-container\",\n                children: [/*#__PURE__*/_jsxDEV(CustomSelect, {\n                  value: startMonth,\n                  onChange: e => setStartMonth(e.target.value),\n                  options: [{\n                    value: '',\n                    label: 'Month'\n                  }, ...months.map(month => ({\n                    value: month,\n                    label: new Date(2000, month - 1).toLocaleDateString('en-US', {\n                      month: 'short'\n                    })\n                  }))],\n                  placeholder: \"Month\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 345,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(CustomSelect, {\n                  value: startYear,\n                  onChange: e => setStartYear(e.target.value),\n                  options: [{\n                    value: '',\n                    label: 'Year'\n                  }, ...years.map(year => ({\n                    value: year,\n                    label: year\n                  }))],\n                  placeholder: \"Year\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 357,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  className: \"start-button\",\n                  onClick: setToStart,\n                  children: \"Start\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 366,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 344,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 342,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"date-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"control-label\",\n                children: \"End Date\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 377,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"end-date-container\",\n                children: [/*#__PURE__*/_jsxDEV(CustomSelect, {\n                  value: endMonth,\n                  onChange: e => setEndMonth(e.target.value),\n                  options: [{\n                    value: '',\n                    label: 'Month'\n                  }, ...months.map(month => ({\n                    value: month,\n                    label: new Date(2000, month - 1).toLocaleDateString('en-US', {\n                      month: 'short'\n                    })\n                  }))],\n                  placeholder: \"Month\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 379,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(CustomSelect, {\n                  value: endYear,\n                  onChange: e => setEndYear(e.target.value),\n                  options: [{\n                    value: '',\n                    label: 'Year'\n                  }, ...years.map(year => ({\n                    value: year,\n                    label: year\n                  }))],\n                  placeholder: \"Year\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 391,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  className: \"now-button\",\n                  onClick: setToNow,\n                  children: \"Now\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 400,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 378,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 376,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 340,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 323,\n          columnNumber: 15\n        }, this), selectedAttribute && chartData.datasets.length > 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"chart-wrapper\",\n          style: {\n            height: '400px'\n          },\n          children: /*#__PURE__*/_jsxDEV(Line, {\n            data: chartData,\n            options: chartOptions\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 414,\n            columnNumber: 19\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 413,\n          columnNumber: 17\n        }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"alert alert-info\",\n          children: \"Please select an attribute and ensure data is available for the selected date range.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 417,\n          columnNumber: 17\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 321,\n        columnNumber: 13\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 319,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 305,\n    columnNumber: 5\n  }, this);\n};\n_s2(Strategy, \"FXPu1PVWL27qn/kjax75gpk9gsM=\", false, function () {\n  return [useParams];\n});\n_c = Strategy;\nexport default Strategy;\nvar _c;\n$RefreshReg$(_c, \"Strategy\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "useParams", "axios", "StrategyDropdown", "Chart", "ChartJS", "CategoryScale", "LinearScale", "PointElement", "LineElement", "Title", "<PERSON><PERSON><PERSON>", "Legend", "Line", "jsxDEV", "_jsxDEV", "register", "Strategy", "_s2", "_s", "$RefreshSig$", "strategyName", "currentStrategy", "setCurrentStrategy", "strategyData", "setStrategyData", "loading", "setLoading", "selectedAttribute", "setSelectedAttribute", "startMonth", "setStartMonth", "startYear", "setStartYear", "endMonth", "setEndMonth", "endYear", "setEndYear", "fetchStrategyData", "strategy", "response", "get", "data", "Object", "keys", "length", "firstMonth", "firstStrategy", "attributes", "months", "sort", "startYearMonth", "startMonthNum", "parseMonthYear", "endYearMonth", "endMonthNum", "error", "console", "monthYearStr", "year", "month", "split", "createMonthYearStr", "padStart", "getAvailableAttributes", "getAvailableMonthsYears", "years", "monthsYears", "map", "Set", "setToNow", "now", "Date", "currentMonth", "getMonth", "toString", "currentYear", "getFullYear", "setToStart", "getChartData", "labels", "datasets", "filteredData", "for<PERSON>ach", "monthYear", "yearNum", "parseInt", "monthNum", "startYearNum", "endYearNum", "isInRange", "sortedMonths", "a", "b", "yearA", "monthA", "yearB", "monthB", "date", "toLocaleDateString", "allSubcategories", "values", "subcat", "add", "colors", "Array", "from", "subcategory", "index", "total", "count", "undefined", "label", "borderColor", "backgroundColor", "tension", "chartOptions", "responsive", "plugins", "legend", "position", "title", "display", "text", "scales", "y", "beginAtZero", "ticks", "callback", "value", "x", "availableAttributes", "chartData", "CustomSelect", "onChange", "options", "placeholder", "className", "isOpen", "setIsOpen", "<PERSON><PERSON><PERSON><PERSON>", "setSelectedLabel", "selected", "find", "opt", "handleSelect", "optionValue", "target", "children", "onClick", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "option", "onStrategyChange", "role", "e", "attr", "style", "height", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Desktop/Coding/BVS/CrystalBall/insights-app/frontend/src/components/Strategy.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { <PERSON>hRouter, useParams } from 'react-router-dom';\nimport axios from 'axios';\nimport StrategyDropdown from './StrategyDropdown';\nimport {\n  Chart as ChartJS,\n  CategoryScale,\n  LinearScale,\n  PointElement,\n  LineElement,\n  Title,\n  Tooltip,\n  Legend,\n} from 'chart.js';\nimport { Line } from 'react-chartjs-2';\n\nChartJS.register(\n  CategoryScale,\n  LinearScale,\n  PointElement,\n  LineElement,\n  Title,\n  Tooltip,\n  Legend\n);\n\nconst Strategy = () => {\n  const { strategyName } = useParams();\n  const [currentStrategy, setCurrentStrategy] = useState(strategyName || 'Alle');\n  const [strategyData, setStrategyData] = useState(null);\n  const [loading, setLoading] = useState(true);\n\n  // Chart state\n  const [selectedAttribute, setSelectedAttribute] = useState('');\n  const [startMonth, setStartMonth] = useState('');\n  const [startYear, setStartYear] = useState('');\n  const [endMonth, setEndMonth] = useState('');\n  const [endYear, setEndYear] = useState('');\n\n  useEffect(() => {\n    fetchStrategyData(currentStrategy);\n  }, [currentStrategy]);\n\n  const fetchStrategyData = async (strategy) => {\n    try {\n      setLoading(true);\n      const response = await axios.get(`/api/strategy/${strategy}/`);\n      setStrategyData(response.data);\n\n      // Initialize dropdowns with first available data\n      if (response.data && Object.keys(response.data).length > 0) {\n        const firstMonth = Object.keys(response.data)[0];\n        const firstStrategy = Object.keys(response.data[firstMonth])[0];\n        const attributes = Object.keys(response.data[firstMonth][firstStrategy]);\n\n        if (attributes.length > 0 && !selectedAttribute) {\n          setSelectedAttribute(attributes[0]);\n        }\n\n        // Set default date range\n        const months = Object.keys(response.data).sort();\n        if (months.length > 0) {\n          const [startYearMonth, startMonthNum] = parseMonthYear(months[0]);\n          const [endYearMonth, endMonthNum] = parseMonthYear(months[months.length - 1]);\n\n          if (!startMonth) {\n            setStartMonth(startMonthNum);\n            setStartYear(startYearMonth);\n          }\n          if (!endMonth) {\n            setEndMonth(endMonthNum);\n            setEndYear(endYearMonth);\n          }\n        }\n      }\n    } catch (error) {\n      console.error('Error fetching strategy data:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Helper function to parse month-year string like \"2025 - 04\"\n  const parseMonthYear = (monthYearStr) => {\n    const [year, month] = monthYearStr.split(' - ');\n    return [year, month];\n  };\n\n  // Helper function to create month-year string\n  const createMonthYearStr = (year, month) => {\n    return `${year} - ${month.padStart(2, '0')}`;\n  };\n\n  // Get available attributes from data\n  const getAvailableAttributes = () => {\n    if (!strategyData || Object.keys(strategyData).length === 0) return [];\n\n    const firstMonth = Object.keys(strategyData)[0];\n    const firstStrategy = Object.keys(strategyData[firstMonth])[0];\n    return Object.keys(strategyData[firstMonth][firstStrategy]);\n  };\n\n  // Get available months and years\n  const getAvailableMonthsYears = () => {\n    if (!strategyData) return { months: [], years: [] };\n\n    const monthsYears = Object.keys(strategyData).map(parseMonthYear);\n    const months = [...new Set(monthsYears.map(([year, month]) => month))].sort();\n    const years = [...new Set(monthsYears.map(([year, month]) => year))].sort();\n\n    return { months, years };\n  };\n\n  // Set end date to current month/year\n  const setToNow = () => {\n    const now = new Date();\n    const currentMonth = (now.getMonth() + 1).toString().padStart(2, '0');\n    const currentYear = now.getFullYear().toString();\n\n    setEndMonth(currentMonth);\n    setEndYear(currentYear);\n  };\n\n  // Set start date to first available month/year\n  const setToStart = () => {\n    if (!strategyData || Object.keys(strategyData).length === 0) return;\n    \n    const months = Object.keys(strategyData).sort();\n    if (months.length > 0) {\n      const [startYearMonth, startMonthNum] = parseMonthYear(months[0]);\n      setStartMonth(startMonthNum);\n      setStartYear(startYearMonth);\n    }\n  };\n\n  // Process data for chart\n  const getChartData = () => {\n    if (!strategyData || !selectedAttribute || !startMonth || !startYear || !endMonth || !endYear) {\n      return { labels: [], datasets: [] };\n    }\n\n    // Filter data by date range\n    const filteredData = {};\n    Object.keys(strategyData).forEach(monthYear => {\n      const [year, month] = parseMonthYear(monthYear);\n      const yearNum = parseInt(year);\n      const monthNum = parseInt(month);\n      const startYearNum = parseInt(startYear);\n      const startMonthNum = parseInt(startMonth);\n      const endYearNum = parseInt(endYear);\n      const endMonthNum = parseInt(endMonth);\n\n      const isInRange = (yearNum > startYearNum || (yearNum === startYearNum && monthNum >= startMonthNum)) &&\n                       (yearNum < endYearNum || (yearNum === endYearNum && monthNum <= endMonthNum));\n\n      if (isInRange) {\n        filteredData[monthYear] = strategyData[monthYear];\n      }\n    });\n\n    // Sort months chronologically\n    const sortedMonths = Object.keys(filteredData).sort((a, b) => {\n      const [yearA, monthA] = parseMonthYear(a);\n      const [yearB, monthB] = parseMonthYear(b);\n      return new Date(yearA, monthA - 1) - new Date(yearB, monthB - 1);\n    });\n\n    // Create labels (formatted month names)\n    const labels = sortedMonths.map(monthYear => {\n      const [year, month] = parseMonthYear(monthYear);\n      const date = new Date(year, month - 1);\n      return date.toLocaleDateString('en-US', { month: 'short', year: 'numeric' });\n    });\n\n    // Get all unique currencies/subcategories for the selected attribute\n    const allSubcategories = new Set();\n    sortedMonths.forEach(monthYear => {\n      Object.values(filteredData[monthYear]).forEach(strategy => {\n        if (strategy[selectedAttribute]) {\n          Object.keys(strategy[selectedAttribute]).forEach(subcat => {\n            allSubcategories.add(subcat);\n          });\n        }\n      });\n    });\n\n    // Generate colors for each line\n    const colors = [\n      'rgb(255, 99, 132)',\n      'rgb(54, 162, 235)',\n      'rgb(255, 205, 86)',\n      'rgb(75, 192, 192)',\n      'rgb(153, 102, 255)',\n      'rgb(255, 159, 64)',\n      'rgb(199, 199, 199)',\n      'rgb(83, 102, 147)',\n    ];\n\n    // Create datasets for each subcategory\n    const datasets = Array.from(allSubcategories).map((subcategory, index) => {\n      const data = sortedMonths.map(monthYear => {\n        // Aggregate data across all strategies for this month and subcategory\n        let total = 0;\n        let count = 0;\n        Object.values(filteredData[monthYear]).forEach(strategy => {\n          if (strategy[selectedAttribute] && strategy[selectedAttribute][subcategory] !== undefined) {\n            total += strategy[selectedAttribute][subcategory];\n            count++;\n          }\n        });\n        return count > 0 ? total / count : 0; // Average across strategies\n      });\n\n      return {\n        label: subcategory,\n        data: data,\n        borderColor: colors[index % colors.length],\n        backgroundColor: colors[index % colors.length] + '20',\n        tension: 0.1,\n      };\n    });\n\n    return { labels, datasets };\n  };\n\n  const chartOptions = {\n    responsive: true,\n    plugins: {\n      legend: {\n        position: 'bottom',\n      },\n      title: {\n        display: true,\n        text: `${selectedAttribute} Distribution Over Time`,\n      },\n    },\n    scales: {\n      y: {\n        beginAtZero: true,\n        ticks: {\n          callback: function(value) {\n            return value + '%';\n          }\n        },\n        title: {\n          display: true,\n          text: 'Percentage (%)'\n        }\n      },\n      x: {\n        title: {\n          display: true,\n          text: 'Month'\n        }\n      }\n    },\n  };\n\n  const availableAttributes = getAvailableAttributes();\n  const { months, years } = getAvailableMonthsYears();\n  const chartData = getChartData();\n\n  // Custom Select Component\n  const CustomSelect = ({ value, onChange, options, placeholder, className }) => {\n    const [isOpen, setIsOpen] = useState(false);\n    const [selectedLabel, setSelectedLabel] = useState('');\n\n    useEffect(() => {\n      const selected = options.find(opt => opt.value === value);\n      setSelectedLabel(selected ? selected.label : '');\n    }, [value, options]);\n\n    const handleSelect = (optionValue) => {\n      onChange({ target: { value: optionValue } });\n      setIsOpen(false);\n    };\n\n    return (\n      <div className={`custom-select ${className || ''}`}>\n        <div \n          className=\"custom-select-trigger\"\n          onClick={() => setIsOpen(!isOpen)}\n        >\n          <span>{selectedLabel || placeholder}</span>\n          <span className={`custom-select-arrow ${isOpen ? 'open' : ''}`}>&#9662;</span>\n        </div>\n        {isOpen && (\n          <div className=\"custom-select-options\">\n            {options.map(option => (\n              <div\n                key={option.value}\n                className={`custom-select-option ${value === option.value ? 'selected' : ''}`}\n                onClick={() => handleSelect(option.value)}\n              >\n                {option.label}\n              </div>\n            ))}\n          </div>\n        )}\n      </div>\n    );\n  };\n\n  return (\n    <div className=\"content-page\">\n      <StrategyDropdown\n        currentStrategy={currentStrategy}\n        onStrategyChange={setCurrentStrategy}\n      />\n      <p className=\"section_title\">Strategy Insights</p>\n      <hr></hr>\n      {loading ? (\n        <div className=\"text-center\">\n          <div className=\"spinner-border\" role=\"status\">\n            <span className=\"visually-hidden\">Loading...</span>\n          </div>\n        </div>\n      ) : (\n        <div className=\"strategy-content\">\n          {strategyData && (\n            <div className=\"chart-container\">\n              {/* Chart Controls */}\n              <div className=\"chart-controls\">\n                {/* Attribute Dropdown */}\n                <div className=\"control-group attribute-dropdown\">\n                  <label className=\"control-label\">Attribute</label>\n                  <CustomSelect\n                    value={selectedAttribute}\n                    onChange={(e) => setSelectedAttribute(e.target.value)}\n                    options={[\n                      { value: '', label: 'Select Attribute' },\n                      ...availableAttributes.map(attr => ({ value: attr, label: attr }))\n                    ]}\n                    placeholder=\"Select Attribute\"\n                    className=\"form-select\"\n                  />\n                </div>\n\n                {/* Date Controls */}\n                <div className=\"date-controls\">\n                  {/* Start Date */}\n                  <div className=\"date-group\">\n                    <label className=\"control-label\">Start Date</label>\n                    <div className=\"start-date-container\">\n                      <CustomSelect\n                        value={startMonth}\n                        onChange={(e) => setStartMonth(e.target.value)}\n                        options={[\n                          { value: '', label: 'Month' },\n                          ...months.map(month => ({\n                            value: month,\n                            label: new Date(2000, month - 1).toLocaleDateString('en-US', { month: 'short' })\n                          }))\n                        ]}\n                        placeholder=\"Month\"\n                      />\n                      <CustomSelect\n                        value={startYear}\n                        onChange={(e) => setStartYear(e.target.value)}\n                        options={[\n                          { value: '', label: 'Year' },\n                          ...years.map(year => ({ value: year, label: year }))\n                        ]}\n                        placeholder=\"Year\"\n                      />\n                      <button\n                        className=\"start-button\"\n                        onClick={setToStart}\n                      >\n                        Start\n                      </button>\n                    </div>\n                  </div>\n\n                  {/* End Date */}\n                  <div className=\"date-group\">\n                    <label className=\"control-label\">End Date</label>\n                    <div className=\"end-date-container\">\n                      <CustomSelect\n                        value={endMonth}\n                        onChange={(e) => setEndMonth(e.target.value)}\n                        options={[\n                          { value: '', label: 'Month' },\n                          ...months.map(month => ({\n                            value: month,\n                            label: new Date(2000, month - 1).toLocaleDateString('en-US', { month: 'short' })\n                          }))\n                        ]}\n                        placeholder=\"Month\"\n                      />\n                      <CustomSelect\n                        value={endYear}\n                        onChange={(e) => setEndYear(e.target.value)}\n                        options={[\n                          { value: '', label: 'Year' },\n                          ...years.map(year => ({ value: year, label: year }))\n                        ]}\n                        placeholder=\"Year\"\n                      />\n                      <button\n                        className=\"now-button\"\n                        onClick={setToNow}\n                      >\n                        Now\n                      </button>\n                    </div>\n                  </div>\n                </div>\n              </div>\n\n              {/* Chart */}\n              {selectedAttribute && chartData.datasets.length > 0 ? (\n                <div className=\"chart-wrapper\" style={{ height: '400px' }}>\n                  <Line data={chartData} options={chartOptions} />\n                </div>\n              ) : (\n                <div className=\"alert alert-info\">\n                  Please select an attribute and ensure data is available for the selected date range.\n                </div>\n              )}\n            </div>\n          )}\n        </div>\n      )}\n    </div>\n  );\n};\n\nexport default Strategy;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,UAAU,EAAEC,SAAS,QAAQ,kBAAkB;AACxD,OAAOC,KAAK,MAAM,OAAO;AACzB,OAAOC,gBAAgB,MAAM,oBAAoB;AACjD,SACEC,KAAK,IAAIC,OAAO,EAChBC,aAAa,EACbC,WAAW,EACXC,YAAY,EACZC,WAAW,EACXC,KAAK,EACLC,OAAO,EACPC,MAAM,QACD,UAAU;AACjB,SAASC,IAAI,QAAQ,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEvCV,OAAO,CAACW,QAAQ,CACdV,aAAa,EACbC,WAAW,EACXC,YAAY,EACZC,WAAW,EACXC,KAAK,EACLC,OAAO,EACPC,MACF,CAAC;AAED,MAAMK,QAAQ,GAAGA,CAAA,KAAM;EAAAC,GAAA;EAAA,IAAAC,EAAA,GAAAC,YAAA;EACrB,MAAM;IAAEC;EAAa,CAAC,GAAGpB,SAAS,CAAC,CAAC;EACpC,MAAM,CAACqB,eAAe,EAAEC,kBAAkB,CAAC,GAAGzB,QAAQ,CAACuB,YAAY,IAAI,MAAM,CAAC;EAC9E,MAAM,CAACG,YAAY,EAAEC,eAAe,CAAC,GAAG3B,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAAC4B,OAAO,EAAEC,UAAU,CAAC,GAAG7B,QAAQ,CAAC,IAAI,CAAC;;EAE5C;EACA,MAAM,CAAC8B,iBAAiB,EAAEC,oBAAoB,CAAC,GAAG/B,QAAQ,CAAC,EAAE,CAAC;EAC9D,MAAM,CAACgC,UAAU,EAAEC,aAAa,CAAC,GAAGjC,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACkC,SAAS,EAAEC,YAAY,CAAC,GAAGnC,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAACoC,QAAQ,EAAEC,WAAW,CAAC,GAAGrC,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACsC,OAAO,EAAEC,UAAU,CAAC,GAAGvC,QAAQ,CAAC,EAAE,CAAC;EAE1CC,SAAS,CAAC,MAAM;IACduC,iBAAiB,CAAChB,eAAe,CAAC;EACpC,CAAC,EAAE,CAACA,eAAe,CAAC,CAAC;EAErB,MAAMgB,iBAAiB,GAAG,MAAOC,QAAQ,IAAK;IAC5C,IAAI;MACFZ,UAAU,CAAC,IAAI,CAAC;MAChB,MAAMa,QAAQ,GAAG,MAAMtC,KAAK,CAACuC,GAAG,CAAC,iBAAiBF,QAAQ,GAAG,CAAC;MAC9Dd,eAAe,CAACe,QAAQ,CAACE,IAAI,CAAC;;MAE9B;MACA,IAAIF,QAAQ,CAACE,IAAI,IAAIC,MAAM,CAACC,IAAI,CAACJ,QAAQ,CAACE,IAAI,CAAC,CAACG,MAAM,GAAG,CAAC,EAAE;QAC1D,MAAMC,UAAU,GAAGH,MAAM,CAACC,IAAI,CAACJ,QAAQ,CAACE,IAAI,CAAC,CAAC,CAAC,CAAC;QAChD,MAAMK,aAAa,GAAGJ,MAAM,CAACC,IAAI,CAACJ,QAAQ,CAACE,IAAI,CAACI,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC;QAC/D,MAAME,UAAU,GAAGL,MAAM,CAACC,IAAI,CAACJ,QAAQ,CAACE,IAAI,CAACI,UAAU,CAAC,CAACC,aAAa,CAAC,CAAC;QAExE,IAAIC,UAAU,CAACH,MAAM,GAAG,CAAC,IAAI,CAACjB,iBAAiB,EAAE;UAC/CC,oBAAoB,CAACmB,UAAU,CAAC,CAAC,CAAC,CAAC;QACrC;;QAEA;QACA,MAAMC,MAAM,GAAGN,MAAM,CAACC,IAAI,CAACJ,QAAQ,CAACE,IAAI,CAAC,CAACQ,IAAI,CAAC,CAAC;QAChD,IAAID,MAAM,CAACJ,MAAM,GAAG,CAAC,EAAE;UACrB,MAAM,CAACM,cAAc,EAAEC,aAAa,CAAC,GAAGC,cAAc,CAACJ,MAAM,CAAC,CAAC,CAAC,CAAC;UACjE,MAAM,CAACK,YAAY,EAAEC,WAAW,CAAC,GAAGF,cAAc,CAACJ,MAAM,CAACA,MAAM,CAACJ,MAAM,GAAG,CAAC,CAAC,CAAC;UAE7E,IAAI,CAACf,UAAU,EAAE;YACfC,aAAa,CAACqB,aAAa,CAAC;YAC5BnB,YAAY,CAACkB,cAAc,CAAC;UAC9B;UACA,IAAI,CAACjB,QAAQ,EAAE;YACbC,WAAW,CAACoB,WAAW,CAAC;YACxBlB,UAAU,CAACiB,YAAY,CAAC;UAC1B;QACF;MACF;IACF,CAAC,CAAC,OAAOE,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;IACvD,CAAC,SAAS;MACR7B,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAM0B,cAAc,GAAIK,YAAY,IAAK;IACvC,MAAM,CAACC,IAAI,EAAEC,KAAK,CAAC,GAAGF,YAAY,CAACG,KAAK,CAAC,KAAK,CAAC;IAC/C,OAAO,CAACF,IAAI,EAAEC,KAAK,CAAC;EACtB,CAAC;;EAED;EACA,MAAME,kBAAkB,GAAGA,CAACH,IAAI,EAAEC,KAAK,KAAK;IAC1C,OAAO,GAAGD,IAAI,MAAMC,KAAK,CAACG,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE;EAC9C,CAAC;;EAED;EACA,MAAMC,sBAAsB,GAAGA,CAAA,KAAM;IACnC,IAAI,CAACxC,YAAY,IAAImB,MAAM,CAACC,IAAI,CAACpB,YAAY,CAAC,CAACqB,MAAM,KAAK,CAAC,EAAE,OAAO,EAAE;IAEtE,MAAMC,UAAU,GAAGH,MAAM,CAACC,IAAI,CAACpB,YAAY,CAAC,CAAC,CAAC,CAAC;IAC/C,MAAMuB,aAAa,GAAGJ,MAAM,CAACC,IAAI,CAACpB,YAAY,CAACsB,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC;IAC9D,OAAOH,MAAM,CAACC,IAAI,CAACpB,YAAY,CAACsB,UAAU,CAAC,CAACC,aAAa,CAAC,CAAC;EAC7D,CAAC;;EAED;EACA,MAAMkB,uBAAuB,GAAGA,CAAA,KAAM;IACpC,IAAI,CAACzC,YAAY,EAAE,OAAO;MAAEyB,MAAM,EAAE,EAAE;MAAEiB,KAAK,EAAE;IAAG,CAAC;IAEnD,MAAMC,WAAW,GAAGxB,MAAM,CAACC,IAAI,CAACpB,YAAY,CAAC,CAAC4C,GAAG,CAACf,cAAc,CAAC;IACjE,MAAMJ,MAAM,GAAG,CAAC,GAAG,IAAIoB,GAAG,CAACF,WAAW,CAACC,GAAG,CAAC,CAAC,CAACT,IAAI,EAAEC,KAAK,CAAC,KAAKA,KAAK,CAAC,CAAC,CAAC,CAACV,IAAI,CAAC,CAAC;IAC7E,MAAMgB,KAAK,GAAG,CAAC,GAAG,IAAIG,GAAG,CAACF,WAAW,CAACC,GAAG,CAAC,CAAC,CAACT,IAAI,EAAEC,KAAK,CAAC,KAAKD,IAAI,CAAC,CAAC,CAAC,CAACT,IAAI,CAAC,CAAC;IAE3E,OAAO;MAAED,MAAM;MAAEiB;IAAM,CAAC;EAC1B,CAAC;;EAED;EACA,MAAMI,QAAQ,GAAGA,CAAA,KAAM;IACrB,MAAMC,GAAG,GAAG,IAAIC,IAAI,CAAC,CAAC;IACtB,MAAMC,YAAY,GAAG,CAACF,GAAG,CAACG,QAAQ,CAAC,CAAC,GAAG,CAAC,EAAEC,QAAQ,CAAC,CAAC,CAACZ,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;IACrE,MAAMa,WAAW,GAAGL,GAAG,CAACM,WAAW,CAAC,CAAC,CAACF,QAAQ,CAAC,CAAC;IAEhDxC,WAAW,CAACsC,YAAY,CAAC;IACzBpC,UAAU,CAACuC,WAAW,CAAC;EACzB,CAAC;;EAED;EACA,MAAME,UAAU,GAAGA,CAAA,KAAM;IACvB,IAAI,CAACtD,YAAY,IAAImB,MAAM,CAACC,IAAI,CAACpB,YAAY,CAAC,CAACqB,MAAM,KAAK,CAAC,EAAE;IAE7D,MAAMI,MAAM,GAAGN,MAAM,CAACC,IAAI,CAACpB,YAAY,CAAC,CAAC0B,IAAI,CAAC,CAAC;IAC/C,IAAID,MAAM,CAACJ,MAAM,GAAG,CAAC,EAAE;MACrB,MAAM,CAACM,cAAc,EAAEC,aAAa,CAAC,GAAGC,cAAc,CAACJ,MAAM,CAAC,CAAC,CAAC,CAAC;MACjElB,aAAa,CAACqB,aAAa,CAAC;MAC5BnB,YAAY,CAACkB,cAAc,CAAC;IAC9B;EACF,CAAC;;EAED;EACA,MAAM4B,YAAY,GAAGA,CAAA,KAAM;IACzB,IAAI,CAACvD,YAAY,IAAI,CAACI,iBAAiB,IAAI,CAACE,UAAU,IAAI,CAACE,SAAS,IAAI,CAACE,QAAQ,IAAI,CAACE,OAAO,EAAE;MAC7F,OAAO;QAAE4C,MAAM,EAAE,EAAE;QAAEC,QAAQ,EAAE;MAAG,CAAC;IACrC;;IAEA;IACA,MAAMC,YAAY,GAAG,CAAC,CAAC;IACvBvC,MAAM,CAACC,IAAI,CAACpB,YAAY,CAAC,CAAC2D,OAAO,CAACC,SAAS,IAAI;MAC7C,MAAM,CAACzB,IAAI,EAAEC,KAAK,CAAC,GAAGP,cAAc,CAAC+B,SAAS,CAAC;MAC/C,MAAMC,OAAO,GAAGC,QAAQ,CAAC3B,IAAI,CAAC;MAC9B,MAAM4B,QAAQ,GAAGD,QAAQ,CAAC1B,KAAK,CAAC;MAChC,MAAM4B,YAAY,GAAGF,QAAQ,CAACtD,SAAS,CAAC;MACxC,MAAMoB,aAAa,GAAGkC,QAAQ,CAACxD,UAAU,CAAC;MAC1C,MAAM2D,UAAU,GAAGH,QAAQ,CAAClD,OAAO,CAAC;MACpC,MAAMmB,WAAW,GAAG+B,QAAQ,CAACpD,QAAQ,CAAC;MAEtC,MAAMwD,SAAS,GAAG,CAACL,OAAO,GAAGG,YAAY,IAAKH,OAAO,KAAKG,YAAY,IAAID,QAAQ,IAAInC,aAAc,MAClFiC,OAAO,GAAGI,UAAU,IAAKJ,OAAO,KAAKI,UAAU,IAAIF,QAAQ,IAAIhC,WAAY,CAAC;MAE9F,IAAImC,SAAS,EAAE;QACbR,YAAY,CAACE,SAAS,CAAC,GAAG5D,YAAY,CAAC4D,SAAS,CAAC;MACnD;IACF,CAAC,CAAC;;IAEF;IACA,MAAMO,YAAY,GAAGhD,MAAM,CAACC,IAAI,CAACsC,YAAY,CAAC,CAAChC,IAAI,CAAC,CAAC0C,CAAC,EAAEC,CAAC,KAAK;MAC5D,MAAM,CAACC,KAAK,EAAEC,MAAM,CAAC,GAAG1C,cAAc,CAACuC,CAAC,CAAC;MACzC,MAAM,CAACI,KAAK,EAAEC,MAAM,CAAC,GAAG5C,cAAc,CAACwC,CAAC,CAAC;MACzC,OAAO,IAAIrB,IAAI,CAACsB,KAAK,EAAEC,MAAM,GAAG,CAAC,CAAC,GAAG,IAAIvB,IAAI,CAACwB,KAAK,EAAEC,MAAM,GAAG,CAAC,CAAC;IAClE,CAAC,CAAC;;IAEF;IACA,MAAMjB,MAAM,GAAGW,YAAY,CAACvB,GAAG,CAACgB,SAAS,IAAI;MAC3C,MAAM,CAACzB,IAAI,EAAEC,KAAK,CAAC,GAAGP,cAAc,CAAC+B,SAAS,CAAC;MAC/C,MAAMc,IAAI,GAAG,IAAI1B,IAAI,CAACb,IAAI,EAAEC,KAAK,GAAG,CAAC,CAAC;MACtC,OAAOsC,IAAI,CAACC,kBAAkB,CAAC,OAAO,EAAE;QAAEvC,KAAK,EAAE,OAAO;QAAED,IAAI,EAAE;MAAU,CAAC,CAAC;IAC9E,CAAC,CAAC;;IAEF;IACA,MAAMyC,gBAAgB,GAAG,IAAI/B,GAAG,CAAC,CAAC;IAClCsB,YAAY,CAACR,OAAO,CAACC,SAAS,IAAI;MAChCzC,MAAM,CAAC0D,MAAM,CAACnB,YAAY,CAACE,SAAS,CAAC,CAAC,CAACD,OAAO,CAAC5C,QAAQ,IAAI;QACzD,IAAIA,QAAQ,CAACX,iBAAiB,CAAC,EAAE;UAC/Be,MAAM,CAACC,IAAI,CAACL,QAAQ,CAACX,iBAAiB,CAAC,CAAC,CAACuD,OAAO,CAACmB,MAAM,IAAI;YACzDF,gBAAgB,CAACG,GAAG,CAACD,MAAM,CAAC;UAC9B,CAAC,CAAC;QACJ;MACF,CAAC,CAAC;IACJ,CAAC,CAAC;;IAEF;IACA,MAAME,MAAM,GAAG,CACb,mBAAmB,EACnB,mBAAmB,EACnB,mBAAmB,EACnB,mBAAmB,EACnB,oBAAoB,EACpB,mBAAmB,EACnB,oBAAoB,EACpB,mBAAmB,CACpB;;IAED;IACA,MAAMvB,QAAQ,GAAGwB,KAAK,CAACC,IAAI,CAACN,gBAAgB,CAAC,CAAChC,GAAG,CAAC,CAACuC,WAAW,EAAEC,KAAK,KAAK;MACxE,MAAMlE,IAAI,GAAGiD,YAAY,CAACvB,GAAG,CAACgB,SAAS,IAAI;QACzC;QACA,IAAIyB,KAAK,GAAG,CAAC;QACb,IAAIC,KAAK,GAAG,CAAC;QACbnE,MAAM,CAAC0D,MAAM,CAACnB,YAAY,CAACE,SAAS,CAAC,CAAC,CAACD,OAAO,CAAC5C,QAAQ,IAAI;UACzD,IAAIA,QAAQ,CAACX,iBAAiB,CAAC,IAAIW,QAAQ,CAACX,iBAAiB,CAAC,CAAC+E,WAAW,CAAC,KAAKI,SAAS,EAAE;YACzFF,KAAK,IAAItE,QAAQ,CAACX,iBAAiB,CAAC,CAAC+E,WAAW,CAAC;YACjDG,KAAK,EAAE;UACT;QACF,CAAC,CAAC;QACF,OAAOA,KAAK,GAAG,CAAC,GAAGD,KAAK,GAAGC,KAAK,GAAG,CAAC,CAAC,CAAC;MACxC,CAAC,CAAC;MAEF,OAAO;QACLE,KAAK,EAAEL,WAAW;QAClBjE,IAAI,EAAEA,IAAI;QACVuE,WAAW,EAAET,MAAM,CAACI,KAAK,GAAGJ,MAAM,CAAC3D,MAAM,CAAC;QAC1CqE,eAAe,EAAEV,MAAM,CAACI,KAAK,GAAGJ,MAAM,CAAC3D,MAAM,CAAC,GAAG,IAAI;QACrDsE,OAAO,EAAE;MACX,CAAC;IACH,CAAC,CAAC;IAEF,OAAO;MAAEnC,MAAM;MAAEC;IAAS,CAAC;EAC7B,CAAC;EAED,MAAMmC,YAAY,GAAG;IACnBC,UAAU,EAAE,IAAI;IAChBC,OAAO,EAAE;MACPC,MAAM,EAAE;QACNC,QAAQ,EAAE;MACZ,CAAC;MACDC,KAAK,EAAE;QACLC,OAAO,EAAE,IAAI;QACbC,IAAI,EAAE,GAAG/F,iBAAiB;MAC5B;IACF,CAAC;IACDgG,MAAM,EAAE;MACNC,CAAC,EAAE;QACDC,WAAW,EAAE,IAAI;QACjBC,KAAK,EAAE;UACLC,QAAQ,EAAE,SAAAA,CAASC,KAAK,EAAE;YACxB,OAAOA,KAAK,GAAG,GAAG;UACpB;QACF,CAAC;QACDR,KAAK,EAAE;UACLC,OAAO,EAAE,IAAI;UACbC,IAAI,EAAE;QACR;MACF,CAAC;MACDO,CAAC,EAAE;QACDT,KAAK,EAAE;UACLC,OAAO,EAAE,IAAI;UACbC,IAAI,EAAE;QACR;MACF;IACF;EACF,CAAC;EAED,MAAMQ,mBAAmB,GAAGnE,sBAAsB,CAAC,CAAC;EACpD,MAAM;IAAEf,MAAM;IAAEiB;EAAM,CAAC,GAAGD,uBAAuB,CAAC,CAAC;EACnD,MAAMmE,SAAS,GAAGrD,YAAY,CAAC,CAAC;;EAEhC;EACA,MAAMsD,YAAY,GAAGA,CAAC;IAAEJ,KAAK;IAAEK,QAAQ;IAAEC,OAAO;IAAEC,WAAW;IAAEC;EAAU,CAAC,KAAK;IAAAtH,EAAA;IAC7E,MAAM,CAACuH,MAAM,EAAEC,SAAS,CAAC,GAAG7I,QAAQ,CAAC,KAAK,CAAC;IAC3C,MAAM,CAAC8I,aAAa,EAAEC,gBAAgB,CAAC,GAAG/I,QAAQ,CAAC,EAAE,CAAC;IAEtDC,SAAS,CAAC,MAAM;MACd,MAAM+I,QAAQ,GAAGP,OAAO,CAACQ,IAAI,CAACC,GAAG,IAAIA,GAAG,CAACf,KAAK,KAAKA,KAAK,CAAC;MACzDY,gBAAgB,CAACC,QAAQ,GAAGA,QAAQ,CAAC9B,KAAK,GAAG,EAAE,CAAC;IAClD,CAAC,EAAE,CAACiB,KAAK,EAAEM,OAAO,CAAC,CAAC;IAEpB,MAAMU,YAAY,GAAIC,WAAW,IAAK;MACpCZ,QAAQ,CAAC;QAAEa,MAAM,EAAE;UAAElB,KAAK,EAAEiB;QAAY;MAAE,CAAC,CAAC;MAC5CP,SAAS,CAAC,KAAK,CAAC;IAClB,CAAC;IAED,oBACE5H,OAAA;MAAK0H,SAAS,EAAE,iBAAiBA,SAAS,IAAI,EAAE,EAAG;MAAAW,QAAA,gBACjDrI,OAAA;QACE0H,SAAS,EAAC,uBAAuB;QACjCY,OAAO,EAAEA,CAAA,KAAMV,SAAS,CAAC,CAACD,MAAM,CAAE;QAAAU,QAAA,gBAElCrI,OAAA;UAAAqI,QAAA,EAAOR,aAAa,IAAIJ;QAAW;UAAAc,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eAC3C1I,OAAA;UAAM0H,SAAS,EAAE,uBAAuBC,MAAM,GAAG,MAAM,GAAG,EAAE,EAAG;UAAAU,QAAA,EAAC;QAAO;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC3E,CAAC,EACLf,MAAM,iBACL3H,OAAA;QAAK0H,SAAS,EAAC,uBAAuB;QAAAW,QAAA,EACnCb,OAAO,CAACnE,GAAG,CAACsF,MAAM,iBACjB3I,OAAA;UAEE0H,SAAS,EAAE,wBAAwBR,KAAK,KAAKyB,MAAM,CAACzB,KAAK,GAAG,UAAU,GAAG,EAAE,EAAG;UAC9EoB,OAAO,EAAEA,CAAA,KAAMJ,YAAY,CAACS,MAAM,CAACzB,KAAK,CAAE;UAAAmB,QAAA,EAEzCM,MAAM,CAAC1C;QAAK,GAJR0C,MAAM,CAACzB,KAAK;UAAAqB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAKd,CACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC;EAEV,CAAC;EAACtI,EAAA,CAtCIkH,YAAY;EAwClB,oBACEtH,OAAA;IAAK0H,SAAS,EAAC,cAAc;IAAAW,QAAA,gBAC3BrI,OAAA,CAACZ,gBAAgB;MACfmB,eAAe,EAAEA,eAAgB;MACjCqI,gBAAgB,EAAEpI;IAAmB;MAAA+H,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACtC,CAAC,eACF1I,OAAA;MAAG0H,SAAS,EAAC,eAAe;MAAAW,QAAA,EAAC;IAAiB;MAAAE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAG,CAAC,eAClD1I,OAAA;MAAAuI,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAQ,CAAC,EACR/H,OAAO,gBACNX,OAAA;MAAK0H,SAAS,EAAC,aAAa;MAAAW,QAAA,eAC1BrI,OAAA;QAAK0H,SAAS,EAAC,gBAAgB;QAACmB,IAAI,EAAC,QAAQ;QAAAR,QAAA,eAC3CrI,OAAA;UAAM0H,SAAS,EAAC,iBAAiB;UAAAW,QAAA,EAAC;QAAU;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChD;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,gBAEN1I,OAAA;MAAK0H,SAAS,EAAC,kBAAkB;MAAAW,QAAA,EAC9B5H,YAAY,iBACXT,OAAA;QAAK0H,SAAS,EAAC,iBAAiB;QAAAW,QAAA,gBAE9BrI,OAAA;UAAK0H,SAAS,EAAC,gBAAgB;UAAAW,QAAA,gBAE7BrI,OAAA;YAAK0H,SAAS,EAAC,kCAAkC;YAAAW,QAAA,gBAC/CrI,OAAA;cAAO0H,SAAS,EAAC,eAAe;cAAAW,QAAA,EAAC;YAAS;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAClD1I,OAAA,CAACsH,YAAY;cACXJ,KAAK,EAAErG,iBAAkB;cACzB0G,QAAQ,EAAGuB,CAAC,IAAKhI,oBAAoB,CAACgI,CAAC,CAACV,MAAM,CAAClB,KAAK,CAAE;cACtDM,OAAO,EAAE,CACP;gBAAEN,KAAK,EAAE,EAAE;gBAAEjB,KAAK,EAAE;cAAmB,CAAC,EACxC,GAAGmB,mBAAmB,CAAC/D,GAAG,CAAC0F,IAAI,KAAK;gBAAE7B,KAAK,EAAE6B,IAAI;gBAAE9C,KAAK,EAAE8C;cAAK,CAAC,CAAC,CAAC,CAClE;cACFtB,WAAW,EAAC,kBAAkB;cAC9BC,SAAS,EAAC;YAAa;cAAAa,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAGN1I,OAAA;YAAK0H,SAAS,EAAC,eAAe;YAAAW,QAAA,gBAE5BrI,OAAA;cAAK0H,SAAS,EAAC,YAAY;cAAAW,QAAA,gBACzBrI,OAAA;gBAAO0H,SAAS,EAAC,eAAe;gBAAAW,QAAA,EAAC;cAAU;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACnD1I,OAAA;gBAAK0H,SAAS,EAAC,sBAAsB;gBAAAW,QAAA,gBACnCrI,OAAA,CAACsH,YAAY;kBACXJ,KAAK,EAAEnG,UAAW;kBAClBwG,QAAQ,EAAGuB,CAAC,IAAK9H,aAAa,CAAC8H,CAAC,CAACV,MAAM,CAAClB,KAAK,CAAE;kBAC/CM,OAAO,EAAE,CACP;oBAAEN,KAAK,EAAE,EAAE;oBAAEjB,KAAK,EAAE;kBAAQ,CAAC,EAC7B,GAAG/D,MAAM,CAACmB,GAAG,CAACR,KAAK,KAAK;oBACtBqE,KAAK,EAAErE,KAAK;oBACZoD,KAAK,EAAE,IAAIxC,IAAI,CAAC,IAAI,EAAEZ,KAAK,GAAG,CAAC,CAAC,CAACuC,kBAAkB,CAAC,OAAO,EAAE;sBAAEvC,KAAK,EAAE;oBAAQ,CAAC;kBACjF,CAAC,CAAC,CAAC,CACH;kBACF4E,WAAW,EAAC;gBAAO;kBAAAc,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACpB,CAAC,eACF1I,OAAA,CAACsH,YAAY;kBACXJ,KAAK,EAAEjG,SAAU;kBACjBsG,QAAQ,EAAGuB,CAAC,IAAK5H,YAAY,CAAC4H,CAAC,CAACV,MAAM,CAAClB,KAAK,CAAE;kBAC9CM,OAAO,EAAE,CACP;oBAAEN,KAAK,EAAE,EAAE;oBAAEjB,KAAK,EAAE;kBAAO,CAAC,EAC5B,GAAG9C,KAAK,CAACE,GAAG,CAACT,IAAI,KAAK;oBAAEsE,KAAK,EAAEtE,IAAI;oBAAEqD,KAAK,EAAErD;kBAAK,CAAC,CAAC,CAAC,CACpD;kBACF6E,WAAW,EAAC;gBAAM;kBAAAc,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnB,CAAC,eACF1I,OAAA;kBACE0H,SAAS,EAAC,cAAc;kBACxBY,OAAO,EAAEvE,UAAW;kBAAAsE,QAAA,EACrB;gBAED;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAGN1I,OAAA;cAAK0H,SAAS,EAAC,YAAY;cAAAW,QAAA,gBACzBrI,OAAA;gBAAO0H,SAAS,EAAC,eAAe;gBAAAW,QAAA,EAAC;cAAQ;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACjD1I,OAAA;gBAAK0H,SAAS,EAAC,oBAAoB;gBAAAW,QAAA,gBACjCrI,OAAA,CAACsH,YAAY;kBACXJ,KAAK,EAAE/F,QAAS;kBAChBoG,QAAQ,EAAGuB,CAAC,IAAK1H,WAAW,CAAC0H,CAAC,CAACV,MAAM,CAAClB,KAAK,CAAE;kBAC7CM,OAAO,EAAE,CACP;oBAAEN,KAAK,EAAE,EAAE;oBAAEjB,KAAK,EAAE;kBAAQ,CAAC,EAC7B,GAAG/D,MAAM,CAACmB,GAAG,CAACR,KAAK,KAAK;oBACtBqE,KAAK,EAAErE,KAAK;oBACZoD,KAAK,EAAE,IAAIxC,IAAI,CAAC,IAAI,EAAEZ,KAAK,GAAG,CAAC,CAAC,CAACuC,kBAAkB,CAAC,OAAO,EAAE;sBAAEvC,KAAK,EAAE;oBAAQ,CAAC;kBACjF,CAAC,CAAC,CAAC,CACH;kBACF4E,WAAW,EAAC;gBAAO;kBAAAc,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACpB,CAAC,eACF1I,OAAA,CAACsH,YAAY;kBACXJ,KAAK,EAAE7F,OAAQ;kBACfkG,QAAQ,EAAGuB,CAAC,IAAKxH,UAAU,CAACwH,CAAC,CAACV,MAAM,CAAClB,KAAK,CAAE;kBAC5CM,OAAO,EAAE,CACP;oBAAEN,KAAK,EAAE,EAAE;oBAAEjB,KAAK,EAAE;kBAAO,CAAC,EAC5B,GAAG9C,KAAK,CAACE,GAAG,CAACT,IAAI,KAAK;oBAAEsE,KAAK,EAAEtE,IAAI;oBAAEqD,KAAK,EAAErD;kBAAK,CAAC,CAAC,CAAC,CACpD;kBACF6E,WAAW,EAAC;gBAAM;kBAAAc,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnB,CAAC,eACF1I,OAAA;kBACE0H,SAAS,EAAC,YAAY;kBACtBY,OAAO,EAAE/E,QAAS;kBAAA8E,QAAA,EACnB;gBAED;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,EAGL7H,iBAAiB,IAAIwG,SAAS,CAACnD,QAAQ,CAACpC,MAAM,GAAG,CAAC,gBACjD9B,OAAA;UAAK0H,SAAS,EAAC,eAAe;UAACsB,KAAK,EAAE;YAAEC,MAAM,EAAE;UAAQ,CAAE;UAAAZ,QAAA,eACxDrI,OAAA,CAACF,IAAI;YAAC6B,IAAI,EAAE0F,SAAU;YAACG,OAAO,EAAEnB;UAAa;YAAAkC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7C,CAAC,gBAEN1I,OAAA;UAAK0H,SAAS,EAAC,kBAAkB;UAAAW,QAAA,EAAC;QAElC;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE;IACN;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAACvI,GAAA,CAhZID,QAAQ;EAAA,QACahB,SAAS;AAAA;AAAAgK,EAAA,GAD9BhJ,QAAQ;AAkZd,eAAeA,QAAQ;AAAC,IAAAgJ,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}