{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Desktop/Coding/BVS/CrystalBall/insights-app/frontend/src/components/Strategy.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useParams } from 'react-router-dom';\nimport axios from 'axios';\nimport StrategyDropdown from './StrategyDropdown';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Strategy = () => {\n  _s();\n  const {\n    strategyName\n  } = useParams();\n  const [currentStrategy, setCurrentStrategy] = useState(strategyName || 'Alle');\n  const [strategyData, setStrategyData] = useState(null);\n  const [loading, setLoading] = useState(true);\n  useEffect(() => {\n    fetchStrategyData(currentStrategy);\n  }, [currentStrategy]);\n  const fetchStrategyData = async strategy => {\n    try {\n      setLoading(true);\n      const response = await axios.get(`/api/strategy/${strategy}/`);\n      setStrategyData(response.data);\n    } catch (error) {\n      console.error('Error fetching strategy data:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"content-page\",\n    children: [/*#__PURE__*/_jsxDEV(StrategyDropdown, {\n      currentStrategy: currentStrategy,\n      onStrategyChange: setCurrentStrategy\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 30,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n      className: \"section_title\",\n      children: \"Strategy Insights\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 34,\n      columnNumber: 7\n    }, this), loading ? /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"text-center\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"spinner-border\",\n        role: \"status\",\n        children: /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"visually-hidden\",\n          children: \"Loading...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 39,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 38,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 37,\n      columnNumber: 9\n    }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"strategy-content\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 43,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 29,\n    columnNumber: 5\n  }, this);\n};\n_s(Strategy, \"gFXaExzt7p+qtizUbqaDaph1Th8=\", false, function () {\n  return [useParams];\n});\n_c = Strategy;\nexport default Strategy;\nvar _c;\n$RefreshReg$(_c, \"Strategy\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useParams", "axios", "StrategyDropdown", "jsxDEV", "_jsxDEV", "Strategy", "_s", "strategyName", "currentStrategy", "setCurrentStrategy", "strategyData", "setStrategyData", "loading", "setLoading", "fetchStrategyData", "strategy", "response", "get", "data", "error", "console", "className", "children", "onStrategyChange", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "role", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Desktop/Coding/BVS/CrystalBall/insights-app/frontend/src/components/Strategy.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useParams } from 'react-router-dom';\nimport axios from 'axios';\nimport StrategyDropdown from './StrategyDropdown';\n\nconst Strategy = () => {\n  const { strategyName } = useParams();\n  const [currentStrategy, setCurrentStrategy] = useState(strategyName || 'Alle');\n  const [strategyData, setStrategyData] = useState(null);\n  const [loading, setLoading] = useState(true);\n\n  useEffect(() => {\n    fetchStrategyData(currentStrategy);\n  }, [currentStrategy]);\n\n  const fetchStrategyData = async (strategy) => {\n    try {\n      setLoading(true);\n      const response = await axios.get(`/api/strategy/${strategy}/`);\n      setStrategyData(response.data);\n    } catch (error) {\n      console.error('Error fetching strategy data:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  return (\n    <div className=\"content-page\">\n      <StrategyDropdown \n        currentStrategy={currentStrategy}\n        onStrategyChange={setCurrentStrategy}\n      />\n      <p className=\"section_title\">Strategy Insights</p>\n      \n      {loading ? (\n        <div className=\"text-center\">\n          <div className=\"spinner-border\" role=\"status\">\n            <span className=\"visually-hidden\">Loading...</span>\n          </div>\n        </div>\n      ) : (\n        <div className=\"strategy-content\">\n          \n        </div>\n      )}\n    </div>\n  );\n};\n\nexport default Strategy;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,SAAS,QAAQ,kBAAkB;AAC5C,OAAOC,KAAK,MAAM,OAAO;AACzB,OAAOC,gBAAgB,MAAM,oBAAoB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAElD,MAAMC,QAAQ,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACrB,MAAM;IAAEC;EAAa,CAAC,GAAGP,SAAS,CAAC,CAAC;EACpC,MAAM,CAACQ,eAAe,EAAEC,kBAAkB,CAAC,GAAGX,QAAQ,CAACS,YAAY,IAAI,MAAM,CAAC;EAC9E,MAAM,CAACG,YAAY,EAAEC,eAAe,CAAC,GAAGb,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAACc,OAAO,EAAEC,UAAU,CAAC,GAAGf,QAAQ,CAAC,IAAI,CAAC;EAE5CC,SAAS,CAAC,MAAM;IACde,iBAAiB,CAACN,eAAe,CAAC;EACpC,CAAC,EAAE,CAACA,eAAe,CAAC,CAAC;EAErB,MAAMM,iBAAiB,GAAG,MAAOC,QAAQ,IAAK;IAC5C,IAAI;MACFF,UAAU,CAAC,IAAI,CAAC;MAChB,MAAMG,QAAQ,GAAG,MAAMf,KAAK,CAACgB,GAAG,CAAC,iBAAiBF,QAAQ,GAAG,CAAC;MAC9DJ,eAAe,CAACK,QAAQ,CAACE,IAAI,CAAC;IAChC,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;IACvD,CAAC,SAAS;MACRN,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,oBACET,OAAA;IAAKiB,SAAS,EAAC,cAAc;IAAAC,QAAA,gBAC3BlB,OAAA,CAACF,gBAAgB;MACfM,eAAe,EAAEA,eAAgB;MACjCe,gBAAgB,EAAEd;IAAmB;MAAAe,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACtC,CAAC,eACFvB,OAAA;MAAGiB,SAAS,EAAC,eAAe;MAAAC,QAAA,EAAC;IAAiB;MAAAE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAG,CAAC,EAEjDf,OAAO,gBACNR,OAAA;MAAKiB,SAAS,EAAC,aAAa;MAAAC,QAAA,eAC1BlB,OAAA;QAAKiB,SAAS,EAAC,gBAAgB;QAACO,IAAI,EAAC,QAAQ;QAAAN,QAAA,eAC3ClB,OAAA;UAAMiB,SAAS,EAAC,iBAAiB;UAAAC,QAAA,EAAC;QAAU;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChD;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,gBAENvB,OAAA;MAAKiB,SAAS,EAAC;IAAkB;MAAAG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAE5B,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAACrB,EAAA,CA3CID,QAAQ;EAAA,QACaL,SAAS;AAAA;AAAA6B,EAAA,GAD9BxB,QAAQ;AA6Cd,eAAeA,QAAQ;AAAC,IAAAwB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}