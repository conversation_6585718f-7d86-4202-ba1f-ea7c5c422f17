{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Desktop/Coding/BVS/CrystalBall/insights-app/frontend/src/components/Strategy.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Link, useParams } from 'react-router-dom';\nimport axios from 'axios';\nimport StrategyDropdown from './StrategyDropdown';\nimport { Chart as ChartJS, CategoryScale, LinearScale, PointElement, LineElement, Title, Tooltip, Legend, elements } from 'chart.js';\nimport { Line } from 'react-chartjs-2';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nChartJS.register(CategoryScale, LinearScale, PointElement, LineElement, Title, Tooltip, Legend);\nconst Strategy = () => {\n  _s();\n  const {\n    strategyName\n  } = useParams();\n  const [currentStrategy, setCurrentStrategy] = useState(strategyName || 'Alle (Durchschnitt)');\n  const [strategyData, setStrategyData] = useState(null);\n  const [loading, setLoading] = useState(true);\n\n  // Chart state\n  const [selectedAttribute, setSelectedAttribute] = useState('');\n  const [startMonth, setStartMonth] = useState('');\n  const [startYear, setStartYear] = useState('');\n  const [endMonth, setEndMonth] = useState('');\n  const [endYear, setEndYear] = useState('');\n\n  // Settings state\n  const [showSettings, setShowSettings] = useState(false);\n  const [rescaleTo100, setRescaleTo100] = useState(false);\n  useEffect(() => {\n    fetchStrategyData(currentStrategy);\n  }, [currentStrategy]);\n  const fetchStrategyData = async strategy => {\n    try {\n      setLoading(true);\n      const response = await axios.get(`/api/strategy/${strategy}/`);\n      setStrategyData(response.data);\n\n      // Initialize dropdowns with first available data\n      if (response.data && Object.keys(response.data).length > 0) {\n        const firstMonth = Object.keys(response.data)[0];\n        const firstStrategy = Object.keys(response.data[firstMonth])[0];\n        const attributes = Object.keys(response.data[firstMonth][firstStrategy]);\n        if (attributes.length > 0 && !selectedAttribute) {\n          setSelectedAttribute(attributes[0]);\n        }\n\n        // Set default date range\n        const months = Object.keys(response.data).sort();\n        if (months.length > 0) {\n          const [startYearMonth, startMonthNum] = parseMonthYear(months[0]);\n          const [endYearMonth, endMonthNum] = parseMonthYear(months[months.length - 1]);\n          if (!startMonth) {\n            setStartMonth(startMonthNum);\n            setStartYear(startYearMonth);\n          }\n          if (!endMonth) {\n            setEndMonth(endMonthNum);\n            setEndYear(endYearMonth);\n          }\n        }\n      }\n    } catch (error) {\n      console.error('Error fetching strategy data:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Helper function to parse month-year string like \"2025 - 04\"\n  const parseMonthYear = monthYearStr => {\n    const [year, month] = monthYearStr.split(' - ');\n    return [year, month];\n  };\n\n  // Helper function to create month-year string\n  // const createMonthYearStr = (year, month) => {\n  //   return `${year} - ${month.padStart(2, '0')}`;\n  // };\n\n  // Get available attributes from data\n  const getAvailableAttributes = () => {\n    if (!strategyData || Object.keys(strategyData).length === 0) return [];\n    const firstMonth = Object.keys(strategyData)[0];\n    const firstStrategy = Object.keys(strategyData[firstMonth])[0];\n    return Object.keys(strategyData[firstMonth][firstStrategy]);\n  };\n\n  // Get available months and years\n  const getAvailableMonthsYears = () => {\n    if (!strategyData) return {\n      months: [],\n      years: []\n    };\n    const monthsYears = Object.keys(strategyData).map(parseMonthYear);\n    const months = [...new Set(monthsYears.map(([year, month]) => month))].sort();\n    const years = [...new Set(monthsYears.map(([year, month]) => year))].sort();\n    return {\n      months,\n      years\n    };\n  };\n\n  // Set end date to current month/year\n  const setToNow = () => {\n    const now = new Date();\n    const currentMonth = (now.getMonth() + 1).toString().padStart(2, '0');\n    const currentYear = now.getFullYear().toString();\n    setEndMonth(currentMonth);\n    setEndYear(currentYear);\n  };\n\n  // Set start date to first available month/year\n  const setToStart = () => {\n    if (!strategyData || Object.keys(strategyData).length === 0) return;\n    const months = Object.keys(strategyData).sort();\n    if (months.length > 0) {\n      const [startYearMonth, startMonthNum] = parseMonthYear(months[0]);\n      setStartMonth(startMonthNum);\n      setStartYear(startYearMonth);\n    }\n  };\n\n  // Get chart data filtered by selected strategy\n  const getChartData = () => {\n    if (!strategyData || !selectedAttribute || !startMonth || !startYear || !endMonth || !endYear) {\n      return {\n        labels: [],\n        datasets: []\n      };\n    }\n\n    // Filter data by date range\n    const filteredData = {};\n    Object.keys(strategyData).forEach(monthYear => {\n      const [year, month] = parseMonthYear(monthYear);\n      const yearNum = parseInt(year);\n      const monthNum = parseInt(month);\n      const startYearNum = parseInt(startYear);\n      const startMonthNum = parseInt(startMonth);\n      const endYearNum = parseInt(endYear);\n      const endMonthNum = parseInt(endMonth);\n      const isInRange = (yearNum > startYearNum || yearNum === startYearNum && monthNum >= startMonthNum) && (yearNum < endYearNum || yearNum === endYearNum && monthNum <= endMonthNum);\n      if (isInRange) {\n        // Filter by selected strategy\n        const monthData = strategyData[monthYear];\n        if (currentStrategy === \"Alle (Durchschnitt)\") {\n          filteredData[monthYear] = monthData;\n        } else if (monthData[currentStrategy]) {\n          filteredData[monthYear] = {\n            [currentStrategy]: monthData[currentStrategy]\n          };\n        }\n      }\n    });\n\n    // Sort months chronologically\n    const sortedMonths = Object.keys(filteredData).sort((a, b) => {\n      const [yearA, monthA] = parseMonthYear(a);\n      const [yearB, monthB] = parseMonthYear(b);\n      return new Date(yearA, monthA - 1) - new Date(yearB, monthB - 1);\n    });\n\n    // Create labels (formatted month names)\n    const labels = sortedMonths.map(monthYear => {\n      const [year, month] = parseMonthYear(monthYear);\n      const date = new Date(year, month - 1);\n      return date.toLocaleDateString('en-US', {\n        month: 'short',\n        year: 'numeric'\n      });\n    });\n\n    // Calculate coverage for each month\n    const coverage = sortedMonths.map(monthYear => {\n      let totalCoverage = 0;\n      let strategyCount = 0;\n      Object.values(filteredData[monthYear]).forEach(strategy => {\n        if (strategy[selectedAttribute]) {\n          const monthTotal = Object.values(strategy[selectedAttribute]).reduce((sum, value) => sum + value, 0);\n          totalCoverage += monthTotal;\n          strategyCount++;\n        }\n      });\n      return strategyCount > 0 ? totalCoverage / strategyCount : 0;\n    });\n\n    // Get all unique currencies/subcategories for the selected attribute\n    const allSubcategories = new Set();\n    sortedMonths.forEach(monthYear => {\n      Object.values(filteredData[monthYear]).forEach(strategy => {\n        if (strategy[selectedAttribute]) {\n          Object.keys(strategy[selectedAttribute]).forEach(subcat => {\n            allSubcategories.add(subcat);\n          });\n        }\n      });\n    });\n\n    // Generate colors for each line\n    const colors = ['rgb(255, 99, 132)', 'rgb(54, 162, 235)', 'rgb(255, 205, 86)', 'rgb(75, 192, 192)', 'rgb(153, 102, 255)', 'rgb(255, 159, 64)', 'rgb(199, 199, 199)', 'rgb(83, 102, 147)', 'rgb(255, 87, 51)', 'rgb(46, 204, 113)', 'rgb(155, 89, 182)', 'rgb(241, 196, 15)', 'rgb(230, 126, 34)', 'rgb(52, 152, 219)', 'rgb(231, 76, 60)', 'rgb(26, 188, 156)', 'rgb(142, 68, 173)', 'rgb(39, 174, 96)', 'rgb(192, 57, 43)', 'rgb(41, 128, 185)'];\n\n    // Create datasets for each subcategory\n    const subcategoriesWithAverage = Array.from(allSubcategories).map(subcategory => {\n      const data = sortedMonths.map(monthYear => {\n        let total = 0;\n        let count = 0;\n        Object.values(filteredData[monthYear]).forEach(strategy => {\n          if (strategy[selectedAttribute] && strategy[selectedAttribute][subcategory] !== undefined) {\n            total += strategy[selectedAttribute][subcategory];\n            count++;\n          }\n        });\n        return count > 0 ? total / count : 0;\n      });\n      const average = data.reduce((sum, value) => sum + value, 0) / data.length;\n      return {\n        subcategory,\n        data,\n        average\n      };\n    });\n\n    // Apply rescaling if enabled\n    if (rescaleTo100) {\n      // For each month, rescale all subcategories so they sum to 100%\n      sortedMonths.forEach((monthYear, monthIndex) => {\n        const monthTotal = subcategoriesWithAverage.reduce((sum, {\n          data\n        }) => sum + data[monthIndex], 0);\n        if (monthTotal > 0) {\n          subcategoriesWithAverage.forEach(({\n            data\n          }) => {\n            data[monthIndex] = data[monthIndex] / monthTotal * 100;\n          });\n        }\n      });\n    }\n\n    // Sort by average value in descending order\n    subcategoriesWithAverage.sort((a, b) => b.average - a.average);\n    const datasets = subcategoriesWithAverage.map(({\n      subcategory,\n      data\n    }, index) => {\n      return {\n        label: subcategory,\n        data: data,\n        borderColor: colors[index % colors.length],\n        backgroundColor: colors[index % colors.length],\n        // + '20' to make them a dark shade\n        tension: 0.1\n      };\n    });\n    return {\n      labels,\n      datasets,\n      coverage\n    };\n  };\n\n  // Custom plugin to display coverage at the top\n  const coveragePlugin = {\n    id: 'coverageDisplay',\n    afterDraw: chart => {\n      const {\n        ctx,\n        chartArea,\n        scales\n      } = chart;\n\n      // Get coverage data from chart's custom property\n      const coverage = chart.config.options.coverageData;\n      if (!coverage || coverage.length === 0) return;\n      ctx.save();\n      ctx.font = '12px Arial';\n      ctx.textAlign = 'center';\n      coverage.forEach((coverageValue, index) => {\n        // Determine color based on coverage percentage\n        let color = 'rgba(0, 0, 0, 0.7)'; // Black for >= 90%\n        if (coverageValue < 80) {\n          color = 'rgba(220, 53, 69, 0.7)'; // Red for < 80% \n        } else if (coverageValue < 90) {\n          color = 'rgba(255, 193, 7, 0.7)'; // Yellow for < 90%\n        }\n        ctx.fillStyle = color;\n\n        // Calculate x position for each label\n        const xScale = scales.x;\n        const xPos = xScale.getPixelForTick(index);\n        const yPos = chartArea.top - 20;\n\n        // Draw coverage percentage\n        ctx.fillText(`${coverageValue.toFixed(0)}%`, xPos, yPos);\n      });\n      ctx.restore();\n    }\n  };\n  const chartOptions = {\n    responsive: true,\n    maintainAspectRatio: false,\n    plugins: {\n      legend: {\n        position: 'bottom',\n        labels: {\n          usePointStyle: true,\n          pointStyle: 'circle',\n          boxWidth: 12,\n          boxHeight: 8\n        }\n      },\n      title: {\n        display: true,\n        text: `${selectedAttribute} Distribution Over Time`,\n        font: {\n          size: 16\n        },\n        padding: {\n          bottom: 50\n        }\n      },\n      tooltip: {\n        callbacks: {\n          label: function (context) {\n            return ` ${context.dataset.label}: ${context.parsed.y.toFixed(1)}%`;\n          }\n        }\n      },\n      coverageDisplay: true\n    },\n    layout: {\n      padding: {\n        top: 0 // Add padding at top for coverage display\n      }\n    },\n    scales: {\n      y: {\n        beginAtZero: true,\n        ticks: {\n          callback: function (value) {\n            return value + '%';\n          }\n        },\n        title: {\n          display: true,\n          text: 'Percentage (%)'\n        }\n      },\n      x: {\n        title: {\n          display: true,\n          text: 'Month'\n        }\n      }\n    },\n    elements: {\n      point: {\n        radius: 4\n      }\n    }\n  };\n  const availableAttributes = getAvailableAttributes();\n  const {\n    months,\n    years\n  } = getAvailableMonthsYears();\n  const chartData = getChartData();\n\n  // Update chart options with current coverage data\n  const chartOptionsWithCoverage = {\n    ...chartOptions,\n    coverageData: chartData.coverage\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"content-page\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"home-arrow-container\",\n        children: /*#__PURE__*/_jsxDEV(Link, {\n          to: \"/\",\n          className: \"back-to-home-arrow\",\n          children: [/*#__PURE__*/_jsxDEV(\"i\", {\n            className: \"bi bi-arrow-left\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 404,\n            columnNumber: 13\n          }, this), \" Home\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 403,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 402,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(StrategyDropdown, {\n        currentStrategy: currentStrategy,\n        onStrategyChange: setCurrentStrategy\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 407,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"section_title\",\n        children: \"Strategy Insights\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 411,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"hr\", {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 412,\n        columnNumber: 9\n      }, this), loading ? /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"spinner-border\",\n          role: \"status\",\n          children: /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"visually-hidden\",\n            children: \"Loading...\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 416,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 415,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 414,\n        columnNumber: 11\n      }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"strategy-content\",\n        children: strategyData && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"chart-container\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"chart-controls\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"control-group attribute-dropdown\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"control-label\",\n                children: \"Attribute\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 427,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                className: \"form-select\",\n                value: selectedAttribute,\n                onChange: e => setSelectedAttribute(e.target.value),\n                children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"\",\n                  children: \"Select Attribute\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 433,\n                  columnNumber: 23\n                }, this), availableAttributes.map(attr => /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: attr,\n                  children: attr\n                }, attr, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 435,\n                  columnNumber: 25\n                }, this))]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 428,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 426,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"date-controls\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"date-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"control-label\",\n                  children: \"Start Date\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 444,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"start-date-container\",\n                  children: [/*#__PURE__*/_jsxDEV(\"select\", {\n                    value: startMonth,\n                    onChange: e => setStartMonth(e.target.value),\n                    children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                      value: \"\",\n                      children: \"Month\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 450,\n                      columnNumber: 27\n                    }, this), months.map(month => /*#__PURE__*/_jsxDEV(\"option\", {\n                      value: month,\n                      children: new Date(2000, month - 1).toLocaleDateString('en-US', {\n                        month: 'short'\n                      })\n                    }, month, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 452,\n                      columnNumber: 29\n                    }, this))]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 446,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                    value: startYear,\n                    onChange: e => setStartYear(e.target.value),\n                    children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                      value: \"\",\n                      children: \"Year\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 461,\n                      columnNumber: 27\n                    }, this), years.map(year => /*#__PURE__*/_jsxDEV(\"option\", {\n                      value: year,\n                      children: year\n                    }, year, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 463,\n                      columnNumber: 29\n                    }, this))]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 457,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    className: \"start-button\",\n                    onClick: setToStart,\n                    children: \"Start\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 466,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 445,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 443,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"date-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"control-label\",\n                  children: \"End Date\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 477,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"end-date-container\",\n                  children: [/*#__PURE__*/_jsxDEV(\"select\", {\n                    value: endMonth,\n                    onChange: e => setEndMonth(e.target.value),\n                    children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                      value: \"\",\n                      children: \"Month\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 483,\n                      columnNumber: 27\n                    }, this), months.map(month => /*#__PURE__*/_jsxDEV(\"option\", {\n                      value: month,\n                      children: new Date(2000, month - 1).toLocaleDateString('en-US', {\n                        month: 'short'\n                      })\n                    }, month, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 485,\n                      columnNumber: 29\n                    }, this))]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 479,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                    value: endYear,\n                    onChange: e => setEndYear(e.target.value),\n                    children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                      value: \"\",\n                      children: \"Year\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 494,\n                      columnNumber: 27\n                    }, this), years.map(year => /*#__PURE__*/_jsxDEV(\"option\", {\n                      value: year,\n                      children: year\n                    }, year, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 496,\n                      columnNumber: 29\n                    }, this))]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 490,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    className: \"now-button\",\n                    onClick: setToNow,\n                    children: \"Now\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 499,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 478,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 476,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 441,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"control-group settings-group\",\n              children: /*#__PURE__*/_jsxDEV(\"button\", {\n                className: \"settings-button btn\",\n                type: \"button\",\n                onClick: () => setShowSettings(true),\n                title: \"Chart Settings\",\n                children: /*#__PURE__*/_jsxDEV(\"i\", {\n                  className: \"bi bi-gear\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 516,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 510,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 509,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 424,\n            columnNumber: 17\n          }, this), selectedAttribute && chartData.datasets.length > 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"chart-wrapper\",\n            style: {\n              height: '400px'\n            },\n            children: /*#__PURE__*/_jsxDEV(Line, {\n              data: chartData,\n              options: chartOptionsWithCoverage,\n              plugins: [coveragePlugin]\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 524,\n              columnNumber: 21\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 523,\n            columnNumber: 19\n          }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"alert alert-info\",\n            children: \"Please select an attribute and ensure data is available for the selected date range.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 527,\n            columnNumber: 19\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 422,\n          columnNumber: 15\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 420,\n        columnNumber: 11\n      }, this), showSettings && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"settings-modal-overlay\",\n        onClick: () => setShowSettings(false),\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"settings-modal-container\",\n          onClick: e => e.stopPropagation(),\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"settings-modal-header\",\n            children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n              className: \"settings-modal-title\",\n              children: \"Chart Settings\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 541,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              type: \"button\",\n              className: \"settings-modal-close\",\n              onClick: () => setShowSettings(false),\n              \"aria-label\": \"Close\",\n              children: \"\\xD7\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 542,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 540,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"settings-modal-body\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"settings-option\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"settings-switch-container\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"form-check form-switch\",\n                  children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                    className: \"form-check-input\",\n                    type: \"checkbox\",\n                    role: \"switch\",\n                    id: \"rescaleToggle\",\n                    checked: rescaleTo100,\n                    onChange: e => setRescaleTo100(e.target.checked)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 555,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n                    className: \"form-check-label\",\n                    htmlFor: \"rescaleToggle\",\n                    children: /*#__PURE__*/_jsxDEV(\"strong\", {\n                      children: \"Rescale to 100%\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 564,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 563,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 554,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"settings-description\",\n                  children: \"When enabled, percentages for each month will be rescaled so they sum to 100%. Coverage display remains unchanged.\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 567,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 553,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 552,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 551,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 539,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 538,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 401,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 399,\n    columnNumber: 5\n  }, this);\n};\n_s(Strategy, \"TRiLjXWJDuFCK5Kg5wIUI5OewoU=\", false, function () {\n  return [useParams];\n});\n_c = Strategy;\nexport default Strategy;\nvar _c;\n$RefreshReg$(_c, \"Strategy\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Link", "useParams", "axios", "StrategyDropdown", "Chart", "ChartJS", "CategoryScale", "LinearScale", "PointElement", "LineElement", "Title", "<PERSON><PERSON><PERSON>", "Legend", "elements", "Line", "jsxDEV", "_jsxDEV", "register", "Strategy", "_s", "strategyName", "currentStrategy", "setCurrentStrategy", "strategyData", "setStrategyData", "loading", "setLoading", "selectedAttribute", "setSelectedAttribute", "startMonth", "setStartMonth", "startYear", "setStartYear", "endMonth", "setEndMonth", "endYear", "setEndYear", "showSettings", "setShowSettings", "rescaleTo100", "setRescaleTo100", "fetchStrategyData", "strategy", "response", "get", "data", "Object", "keys", "length", "firstMonth", "firstStrategy", "attributes", "months", "sort", "startYearMonth", "startMonthNum", "parseMonthYear", "endYearMonth", "endMonthNum", "error", "console", "monthYearStr", "year", "month", "split", "getAvailableAttributes", "getAvailableMonthsYears", "years", "monthsYears", "map", "Set", "setToNow", "now", "Date", "currentMonth", "getMonth", "toString", "padStart", "currentYear", "getFullYear", "setToStart", "getChartData", "labels", "datasets", "filteredData", "for<PERSON>ach", "monthYear", "yearNum", "parseInt", "monthNum", "startYearNum", "endYearNum", "isInRange", "monthData", "sortedMonths", "a", "b", "yearA", "monthA", "yearB", "monthB", "date", "toLocaleDateString", "coverage", "totalCoverage", "strategyCount", "values", "monthTotal", "reduce", "sum", "value", "allSubcategories", "subcat", "add", "colors", "subcategoriesWithAverage", "Array", "from", "subcategory", "total", "count", "undefined", "average", "monthIndex", "index", "label", "borderColor", "backgroundColor", "tension", "coveragePlugin", "id", "afterDraw", "chart", "ctx", "chartArea", "scales", "config", "options", "coverageData", "save", "font", "textAlign", "coverageValue", "color", "fillStyle", "xScale", "x", "xPos", "getPixelForTick", "yPos", "top", "fillText", "toFixed", "restore", "chartOptions", "responsive", "maintainAspectRatio", "plugins", "legend", "position", "usePointStyle", "pointStyle", "boxWidth", "boxHeight", "title", "display", "text", "size", "padding", "bottom", "tooltip", "callbacks", "context", "dataset", "parsed", "y", "coverageDisplay", "layout", "beginAtZero", "ticks", "callback", "point", "radius", "availableAttributes", "chartData", "chartOptionsWithCoverage", "children", "className", "to", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onStrategyChange", "role", "onChange", "e", "target", "attr", "onClick", "type", "style", "height", "stopPropagation", "checked", "htmlFor", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Desktop/Coding/BVS/CrystalBall/insights-app/frontend/src/components/Strategy.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { Link, useParams } from 'react-router-dom';\nimport axios from 'axios';\nimport StrategyDropdown from './StrategyDropdown';\nimport {\n  Chart as ChartJS,\n  CategoryScale,\n  LinearScale,\n  PointElement,\n  LineElement,\n  Title,\n  Tooltip,\n  Legend,\n  elements,\n} from 'chart.js';\nimport { Line } from 'react-chartjs-2';\n\nChartJS.register(\n  CategoryScale,\n  LinearScale,\n  PointElement,\n  LineElement,\n  Title,\n  Tooltip,\n  Legend\n);\n\nconst Strategy = () => {\n  const { strategyName } = useParams();\n  const [currentStrategy, setCurrentStrategy] = useState(strategyName || 'Alle (Durchschnitt)');\n  const [strategyData, setStrategyData] = useState(null);\n  const [loading, setLoading] = useState(true);\n\n  // Chart state\n  const [selectedAttribute, setSelectedAttribute] = useState('');\n  const [startMonth, setStartMonth] = useState('');\n  const [startYear, setStartYear] = useState('');\n  const [endMonth, setEndMonth] = useState('');\n  const [endYear, setEndYear] = useState('');\n\n  // Settings state\n  const [showSettings, setShowSettings] = useState(false);\n  const [rescaleTo100, setRescaleTo100] = useState(false);\n\n  useEffect(() => {\n    fetchStrategyData(currentStrategy);\n  }, [currentStrategy]);\n\n  const fetchStrategyData = async (strategy) => {\n    try {\n      setLoading(true);\n      const response = await axios.get(`/api/strategy/${strategy}/`);\n      setStrategyData(response.data);\n\n      // Initialize dropdowns with first available data\n      if (response.data && Object.keys(response.data).length > 0) {\n        const firstMonth = Object.keys(response.data)[0];\n        const firstStrategy = Object.keys(response.data[firstMonth])[0];\n        const attributes = Object.keys(response.data[firstMonth][firstStrategy]);\n\n        if (attributes.length > 0 && !selectedAttribute) {\n          setSelectedAttribute(attributes[0]);\n        }\n\n        // Set default date range\n        const months = Object.keys(response.data).sort();\n        if (months.length > 0) {\n          const [startYearMonth, startMonthNum] = parseMonthYear(months[0]);\n          const [endYearMonth, endMonthNum] = parseMonthYear(months[months.length - 1]);\n\n          if (!startMonth) {\n            setStartMonth(startMonthNum);\n            setStartYear(startYearMonth);\n          }\n          if (!endMonth) {\n            setEndMonth(endMonthNum);\n            setEndYear(endYearMonth);\n          }\n        }\n      }\n    } catch (error) {\n      console.error('Error fetching strategy data:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Helper function to parse month-year string like \"2025 - 04\"\n  const parseMonthYear = (monthYearStr) => {\n    const [year, month] = monthYearStr.split(' - ');\n    return [year, month];\n  };\n\n  // Helper function to create month-year string\n  // const createMonthYearStr = (year, month) => {\n  //   return `${year} - ${month.padStart(2, '0')}`;\n  // };\n\n  // Get available attributes from data\n  const getAvailableAttributes = () => {\n    if (!strategyData || Object.keys(strategyData).length === 0) return [];\n\n    const firstMonth = Object.keys(strategyData)[0];\n    const firstStrategy = Object.keys(strategyData[firstMonth])[0];\n    return Object.keys(strategyData[firstMonth][firstStrategy]);\n  };\n\n  // Get available months and years\n  const getAvailableMonthsYears = () => {\n    if (!strategyData) return { months: [], years: [] };\n\n    const monthsYears = Object.keys(strategyData).map(parseMonthYear);\n    const months = [...new Set(monthsYears.map(([year, month]) => month))].sort();\n    const years = [...new Set(monthsYears.map(([year, month]) => year))].sort();\n\n    return { months, years };\n  };\n\n  // Set end date to current month/year\n  const setToNow = () => {\n    const now = new Date();\n    const currentMonth = (now.getMonth() + 1).toString().padStart(2, '0');\n    const currentYear = now.getFullYear().toString();\n\n    setEndMonth(currentMonth);\n    setEndYear(currentYear);\n  };\n\n  // Set start date to first available month/year\n  const setToStart = () => {\n    if (!strategyData || Object.keys(strategyData).length === 0) return;\n    \n    const months = Object.keys(strategyData).sort();\n    if (months.length > 0) {\n      const [startYearMonth, startMonthNum] = parseMonthYear(months[0]);\n      setStartMonth(startMonthNum);\n      setStartYear(startYearMonth);\n    }\n  };\n\n  // Get chart data filtered by selected strategy\n  const getChartData = () => {\n    if (!strategyData || !selectedAttribute || !startMonth || !startYear || !endMonth || !endYear) {\n      return { labels: [], datasets: [] };\n    }\n\n    // Filter data by date range\n    const filteredData = {};\n    Object.keys(strategyData).forEach(monthYear => {\n      const [year, month] = parseMonthYear(monthYear);\n      const yearNum = parseInt(year);\n      const monthNum = parseInt(month);\n      const startYearNum = parseInt(startYear);\n      const startMonthNum = parseInt(startMonth);\n      const endYearNum = parseInt(endYear);\n      const endMonthNum = parseInt(endMonth);\n\n      const isInRange = (yearNum > startYearNum || (yearNum === startYearNum && monthNum >= startMonthNum)) &&\n                       (yearNum < endYearNum || (yearNum === endYearNum && monthNum <= endMonthNum));\n\n      if (isInRange) {\n        // Filter by selected strategy\n        const monthData = strategyData[monthYear];\n        if (currentStrategy === \"Alle (Durchschnitt)\") {\n          filteredData[monthYear] = monthData;\n        } else if (monthData[currentStrategy]) {\n          filteredData[monthYear] = { [currentStrategy]: monthData[currentStrategy] };\n        }\n      }\n    });\n\n    // Sort months chronologically\n    const sortedMonths = Object.keys(filteredData).sort((a, b) => {\n      const [yearA, monthA] = parseMonthYear(a);\n      const [yearB, monthB] = parseMonthYear(b);\n      return new Date(yearA, monthA - 1) - new Date(yearB, monthB - 1);\n    });\n\n    // Create labels (formatted month names)\n    const labels = sortedMonths.map(monthYear => {\n      const [year, month] = parseMonthYear(monthYear);\n      const date = new Date(year, month - 1);\n      return date.toLocaleDateString('en-US', { month: 'short', year: 'numeric' });\n    });\n\n    // Calculate coverage for each month\n    const coverage = sortedMonths.map(monthYear => {\n      let totalCoverage = 0;\n      let strategyCount = 0;\n\n      Object.values(filteredData[monthYear]).forEach(strategy => {\n        if (strategy[selectedAttribute]) {\n          const monthTotal = Object.values(strategy[selectedAttribute]).reduce((sum, value) => sum + value, 0);\n          totalCoverage += monthTotal;\n          strategyCount++;\n        }\n      });\n\n      return strategyCount > 0 ? totalCoverage / strategyCount : 0;\n    });\n\n    // Get all unique currencies/subcategories for the selected attribute\n    const allSubcategories = new Set();\n    sortedMonths.forEach(monthYear => {\n      Object.values(filteredData[monthYear]).forEach(strategy => {\n        if (strategy[selectedAttribute]) {\n          Object.keys(strategy[selectedAttribute]).forEach(subcat => {\n            allSubcategories.add(subcat);\n          });\n        }\n      });\n    });\n\n    // Generate colors for each line\n    const colors = [\n      'rgb(255, 99, 132)',\n      'rgb(54, 162, 235)',\n      'rgb(255, 205, 86)',\n      'rgb(75, 192, 192)',\n      'rgb(153, 102, 255)',\n      'rgb(255, 159, 64)',\n      'rgb(199, 199, 199)',\n      'rgb(83, 102, 147)',\n      'rgb(255, 87, 51)',\n      'rgb(46, 204, 113)',\n      'rgb(155, 89, 182)',\n      'rgb(241, 196, 15)',\n      'rgb(230, 126, 34)',\n      'rgb(52, 152, 219)',\n      'rgb(231, 76, 60)',\n      'rgb(26, 188, 156)',\n      'rgb(142, 68, 173)',\n      'rgb(39, 174, 96)',\n      'rgb(192, 57, 43)',\n      'rgb(41, 128, 185)',\n    ];\n\n    // Create datasets for each subcategory\n    const subcategoriesWithAverage = Array.from(allSubcategories).map(subcategory => {\n      const data = sortedMonths.map(monthYear => {\n        let total = 0;\n        let count = 0;\n        Object.values(filteredData[monthYear]).forEach(strategy => {\n          if (strategy[selectedAttribute] && strategy[selectedAttribute][subcategory] !== undefined) {\n            total += strategy[selectedAttribute][subcategory];\n            count++;\n          }\n        });\n        return count > 0 ? total / count : 0;\n      });\n\n      const average = data.reduce((sum, value) => sum + value, 0) / data.length;\n      return { subcategory, data, average };\n    });\n\n    // Apply rescaling if enabled\n    if (rescaleTo100) {\n      // For each month, rescale all subcategories so they sum to 100%\n      sortedMonths.forEach((monthYear, monthIndex) => {\n        const monthTotal = subcategoriesWithAverage.reduce((sum, { data }) => sum + data[monthIndex], 0);\n        if (monthTotal > 0) {\n          subcategoriesWithAverage.forEach(({ data }) => {\n            data[monthIndex] = (data[monthIndex] / monthTotal) * 100;\n          });\n        }\n      });\n    }\n\n    // Sort by average value in descending order\n    subcategoriesWithAverage.sort((a, b) => b.average - a.average);\n\n    const datasets = subcategoriesWithAverage.map(({ subcategory, data }, index) => {\n      return {\n        label: subcategory,\n        data: data,\n        borderColor: colors[index % colors.length],\n        backgroundColor: colors[index % colors.length], // + '20' to make them a dark shade\n        tension: 0.1,\n      };\n    });\n\n    return { labels, datasets, coverage };\n  };\n\n  // Custom plugin to display coverage at the top\n  const coveragePlugin = {\n    id: 'coverageDisplay',\n    afterDraw: (chart) => {\n      const { ctx, chartArea, scales } = chart;\n\n      // Get coverage data from chart's custom property\n      const coverage = chart.config.options.coverageData;\n\n      if (!coverage || coverage.length === 0) return;\n\n      ctx.save();\n      ctx.font = '12px Arial';\n      ctx.textAlign = 'center';\n\n      coverage.forEach((coverageValue, index) => {\n        // Determine color based on coverage percentage\n        let color = 'rgba(0, 0, 0, 0.7)'; // Black for >= 90%\n        if (coverageValue < 80) {\n          color = 'rgba(220, 53, 69, 0.7)'; // Red for < 80% \n        } else if (coverageValue < 90) {\n          color = 'rgba(255, 193, 7, 0.7)'; // Yellow for < 90%\n        }\n\n        ctx.fillStyle = color;\n\n        // Calculate x position for each label\n        const xScale = scales.x;\n        const xPos = xScale.getPixelForTick(index);\n        const yPos = chartArea.top - 20;\n\n        // Draw coverage percentage\n        ctx.fillText(`${coverageValue.toFixed(0)}%`, xPos, yPos);\n      });\n\n      ctx.restore();\n    }\n  };\n\n  const chartOptions = {\n    responsive: true,\n    maintainAspectRatio: false,\n    plugins: {\n      legend: {\n        position: 'bottom',\n        labels: {\n          usePointStyle: true,\n          pointStyle: 'circle',\n          boxWidth: 12,\n          boxHeight: 8,\n        }\n      },\n      title: {\n        display: true,\n        text: `${selectedAttribute} Distribution Over Time`,\n        font: {\n          size: 16\n        },\n        padding: {\n          bottom: 50\n        }\n      },\n      tooltip: {\n        callbacks: {\n          label: function(context) {\n            return ` ${context.dataset.label}: ${context.parsed.y.toFixed(1)}%`;\n          }\n        }\n      },\n      coverageDisplay: true,\n    },\n    layout: {\n      padding: {\n        top: 0, // Add padding at top for coverage display\n      }\n    },\n    scales: {\n      y: {\n        beginAtZero: true,\n        ticks: {\n          callback: function(value) {\n            return value + '%';\n          }\n        },\n        title: {\n          display: true,\n          text: 'Percentage (%)'\n        }\n      },\n      x: {\n        title: {\n          display: true,\n          text: 'Month'\n        }\n      }\n    },\n    elements: {\n      point: {\n        radius: 4\n      }\n    }\n  };\n\n  const availableAttributes = getAvailableAttributes();\n  const { months, years } = getAvailableMonthsYears();\n  const chartData = getChartData();\n\n  // Update chart options with current coverage data\n  const chartOptionsWithCoverage = {\n    ...chartOptions,\n    coverageData: chartData.coverage\n  };\n\n  return (\n    <div>\n      \n      <div className=\"content-page\">\n        <div className='home-arrow-container'>\n          <Link to=\"/\" className=\"back-to-home-arrow\">\n            <i className='bi bi-arrow-left'></i> Home\n          </Link>\n        </div>\n        <StrategyDropdown\n          currentStrategy={currentStrategy}\n          onStrategyChange={setCurrentStrategy}\n        />\n        <p className=\"section_title\">Strategy Insights</p>\n        <hr></hr>\n        {loading ? (\n          <div className=\"text-center\">\n            <div className=\"spinner-border\" role=\"status\">\n              <span className=\"visually-hidden\">Loading...</span>\n            </div>\n          </div>\n        ) : (\n          <div className=\"strategy-content\">\n            {strategyData && (\n              <div className=\"chart-container\">\n                {/* Chart Controls */}\n                <div className=\"chart-controls\">\n                  {/* Attribute Dropdown */}\n                  <div className=\"control-group attribute-dropdown\">\n                    <label className=\"control-label\">Attribute</label>\n                    <select\n                      className=\"form-select\"\n                      value={selectedAttribute}\n                      onChange={(e) => setSelectedAttribute(e.target.value)}\n                    >\n                      <option value=\"\">Select Attribute</option>\n                      {availableAttributes.map(attr => (\n                        <option key={attr} value={attr}>{attr}</option>\n                      ))}\n                    </select>\n                  </div>\n\n                  {/* Date Controls */}\n                  <div className=\"date-controls\">\n                    {/* Start Date */}\n                    <div className=\"date-group\">\n                      <label className=\"control-label\">Start Date</label>\n                      <div className=\"start-date-container\">\n                        <select\n                          value={startMonth}\n                          onChange={(e) => setStartMonth(e.target.value)}\n                        >\n                          <option value=\"\">Month</option>\n                          {months.map(month => (\n                            <option key={month} value={month}>\n                              {new Date(2000, month - 1).toLocaleDateString('en-US', { month: 'short' })}\n                            </option>\n                          ))}\n                        </select>\n                        <select\n                          value={startYear}\n                          onChange={(e) => setStartYear(e.target.value)}\n                        >\n                          <option value=\"\">Year</option>\n                          {years.map(year => (\n                            <option key={year} value={year}>{year}</option>\n                          ))}\n                        </select>\n                        <button\n                          className=\"start-button\"\n                          onClick={setToStart}\n                        >\n                          Start\n                        </button>\n                      </div>\n                    </div>\n\n                    {/* End Date */}\n                    <div className=\"date-group\">\n                      <label className=\"control-label\">End Date</label>\n                      <div className=\"end-date-container\">\n                        <select\n                          value={endMonth}\n                          onChange={(e) => setEndMonth(e.target.value)}\n                        >\n                          <option value=\"\">Month</option>\n                          {months.map(month => (\n                            <option key={month} value={month}>\n                              {new Date(2000, month - 1).toLocaleDateString('en-US', { month: 'short' })}\n                            </option>\n                          ))}\n                        </select>\n                        <select\n                          value={endYear}\n                          onChange={(e) => setEndYear(e.target.value)}\n                        >\n                          <option value=\"\">Year</option>\n                          {years.map(year => (\n                            <option key={year} value={year}>{year}</option>\n                          ))}\n                        </select>\n                        <button\n                          className=\"now-button\"\n                          onClick={setToNow}\n                        >\n                          Now\n                        </button>\n                      </div>\n                    </div>\n                  </div>\n                  {/* Attribute Dropdown */}\n                  <div className=\"control-group settings-group\">\n                    <button\n                      className=\"settings-button btn\"\n                      type=\"button\"\n                      onClick={() => setShowSettings(true)}\n                      title=\"Chart Settings\"\n                    >\n                      <i className=\"bi bi-gear\"></i>\n                    </button>\n                  </div>\n                </div>\n\n                {/* Chart */}\n                {selectedAttribute && chartData.datasets.length > 0 ? (\n                  <div className=\"chart-wrapper\" style={{ height: '400px' }}>\n                    <Line data={chartData} options={chartOptionsWithCoverage} plugins={[coveragePlugin]} />\n                  </div>\n                ) : (\n                  <div className=\"alert alert-info\">\n                    Please select an attribute and ensure data is available for the selected date range.\n                  </div>\n                )}\n              </div>\n            )}\n          </div>\n        )}\n\n        {/* Settings Modal */}\n        {showSettings && (\n          <div className=\"settings-modal-overlay\" onClick={() => setShowSettings(false)}>\n            <div className=\"settings-modal-container\" onClick={(e) => e.stopPropagation()}>\n              <div className=\"settings-modal-header\">\n                <h5 className=\"settings-modal-title\">Chart Settings</h5>\n                <button\n                  type=\"button\"\n                  className=\"settings-modal-close\"\n                  onClick={() => setShowSettings(false)}\n                  aria-label=\"Close\"\n                >\n                  ×\n                </button>\n              </div>\n              <div className=\"settings-modal-body\">\n                <div className=\"settings-option\">\n                  <div className=\"settings-switch-container\">\n                    <div className=\"form-check form-switch\">\n                      <input\n                        className=\"form-check-input\"\n                        type=\"checkbox\"\n                        role=\"switch\"\n                        id=\"rescaleToggle\"\n                        checked={rescaleTo100}\n                        onChange={(e) => setRescaleTo100(e.target.checked)}\n                      />\n                      <label className=\"form-check-label\" htmlFor=\"rescaleToggle\">\n                        <strong>Rescale to 100%</strong>\n                      </label>\n                    </div>\n                    <div className=\"settings-description\">\n                      When enabled, percentages for each month will be rescaled so they sum to 100%.\n                      Coverage display remains unchanged.\n                    </div>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n        )}\n      </div>\n    </div>\n  );\n};\n\nexport default Strategy;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,IAAI,EAAEC,SAAS,QAAQ,kBAAkB;AAClD,OAAOC,KAAK,MAAM,OAAO;AACzB,OAAOC,gBAAgB,MAAM,oBAAoB;AACjD,SACEC,KAAK,IAAIC,OAAO,EAChBC,aAAa,EACbC,WAAW,EACXC,YAAY,EACZC,WAAW,EACXC,KAAK,EACLC,OAAO,EACPC,MAAM,EACNC,QAAQ,QACH,UAAU;AACjB,SAASC,IAAI,QAAQ,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEvCX,OAAO,CAACY,QAAQ,CACdX,aAAa,EACbC,WAAW,EACXC,YAAY,EACZC,WAAW,EACXC,KAAK,EACLC,OAAO,EACPC,MACF,CAAC;AAED,MAAMM,QAAQ,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACrB,MAAM;IAAEC;EAAa,CAAC,GAAGnB,SAAS,CAAC,CAAC;EACpC,MAAM,CAACoB,eAAe,EAAEC,kBAAkB,CAAC,GAAGxB,QAAQ,CAACsB,YAAY,IAAI,qBAAqB,CAAC;EAC7F,MAAM,CAACG,YAAY,EAAEC,eAAe,CAAC,GAAG1B,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAAC2B,OAAO,EAAEC,UAAU,CAAC,GAAG5B,QAAQ,CAAC,IAAI,CAAC;;EAE5C;EACA,MAAM,CAAC6B,iBAAiB,EAAEC,oBAAoB,CAAC,GAAG9B,QAAQ,CAAC,EAAE,CAAC;EAC9D,MAAM,CAAC+B,UAAU,EAAEC,aAAa,CAAC,GAAGhC,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACiC,SAAS,EAAEC,YAAY,CAAC,GAAGlC,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAACmC,QAAQ,EAAEC,WAAW,CAAC,GAAGpC,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACqC,OAAO,EAAEC,UAAU,CAAC,GAAGtC,QAAQ,CAAC,EAAE,CAAC;;EAE1C;EACA,MAAM,CAACuC,YAAY,EAAEC,eAAe,CAAC,GAAGxC,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAACyC,YAAY,EAAEC,eAAe,CAAC,GAAG1C,QAAQ,CAAC,KAAK,CAAC;EAEvDC,SAAS,CAAC,MAAM;IACd0C,iBAAiB,CAACpB,eAAe,CAAC;EACpC,CAAC,EAAE,CAACA,eAAe,CAAC,CAAC;EAErB,MAAMoB,iBAAiB,GAAG,MAAOC,QAAQ,IAAK;IAC5C,IAAI;MACFhB,UAAU,CAAC,IAAI,CAAC;MAChB,MAAMiB,QAAQ,GAAG,MAAMzC,KAAK,CAAC0C,GAAG,CAAC,iBAAiBF,QAAQ,GAAG,CAAC;MAC9DlB,eAAe,CAACmB,QAAQ,CAACE,IAAI,CAAC;;MAE9B;MACA,IAAIF,QAAQ,CAACE,IAAI,IAAIC,MAAM,CAACC,IAAI,CAACJ,QAAQ,CAACE,IAAI,CAAC,CAACG,MAAM,GAAG,CAAC,EAAE;QAC1D,MAAMC,UAAU,GAAGH,MAAM,CAACC,IAAI,CAACJ,QAAQ,CAACE,IAAI,CAAC,CAAC,CAAC,CAAC;QAChD,MAAMK,aAAa,GAAGJ,MAAM,CAACC,IAAI,CAACJ,QAAQ,CAACE,IAAI,CAACI,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC;QAC/D,MAAME,UAAU,GAAGL,MAAM,CAACC,IAAI,CAACJ,QAAQ,CAACE,IAAI,CAACI,UAAU,CAAC,CAACC,aAAa,CAAC,CAAC;QAExE,IAAIC,UAAU,CAACH,MAAM,GAAG,CAAC,IAAI,CAACrB,iBAAiB,EAAE;UAC/CC,oBAAoB,CAACuB,UAAU,CAAC,CAAC,CAAC,CAAC;QACrC;;QAEA;QACA,MAAMC,MAAM,GAAGN,MAAM,CAACC,IAAI,CAACJ,QAAQ,CAACE,IAAI,CAAC,CAACQ,IAAI,CAAC,CAAC;QAChD,IAAID,MAAM,CAACJ,MAAM,GAAG,CAAC,EAAE;UACrB,MAAM,CAACM,cAAc,EAAEC,aAAa,CAAC,GAAGC,cAAc,CAACJ,MAAM,CAAC,CAAC,CAAC,CAAC;UACjE,MAAM,CAACK,YAAY,EAAEC,WAAW,CAAC,GAAGF,cAAc,CAACJ,MAAM,CAACA,MAAM,CAACJ,MAAM,GAAG,CAAC,CAAC,CAAC;UAE7E,IAAI,CAACnB,UAAU,EAAE;YACfC,aAAa,CAACyB,aAAa,CAAC;YAC5BvB,YAAY,CAACsB,cAAc,CAAC;UAC9B;UACA,IAAI,CAACrB,QAAQ,EAAE;YACbC,WAAW,CAACwB,WAAW,CAAC;YACxBtB,UAAU,CAACqB,YAAY,CAAC;UAC1B;QACF;MACF;IACF,CAAC,CAAC,OAAOE,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;IACvD,CAAC,SAAS;MACRjC,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAM8B,cAAc,GAAIK,YAAY,IAAK;IACvC,MAAM,CAACC,IAAI,EAAEC,KAAK,CAAC,GAAGF,YAAY,CAACG,KAAK,CAAC,KAAK,CAAC;IAC/C,OAAO,CAACF,IAAI,EAAEC,KAAK,CAAC;EACtB,CAAC;;EAED;EACA;EACA;EACA;;EAEA;EACA,MAAME,sBAAsB,GAAGA,CAAA,KAAM;IACnC,IAAI,CAAC1C,YAAY,IAAIuB,MAAM,CAACC,IAAI,CAACxB,YAAY,CAAC,CAACyB,MAAM,KAAK,CAAC,EAAE,OAAO,EAAE;IAEtE,MAAMC,UAAU,GAAGH,MAAM,CAACC,IAAI,CAACxB,YAAY,CAAC,CAAC,CAAC,CAAC;IAC/C,MAAM2B,aAAa,GAAGJ,MAAM,CAACC,IAAI,CAACxB,YAAY,CAAC0B,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC;IAC9D,OAAOH,MAAM,CAACC,IAAI,CAACxB,YAAY,CAAC0B,UAAU,CAAC,CAACC,aAAa,CAAC,CAAC;EAC7D,CAAC;;EAED;EACA,MAAMgB,uBAAuB,GAAGA,CAAA,KAAM;IACpC,IAAI,CAAC3C,YAAY,EAAE,OAAO;MAAE6B,MAAM,EAAE,EAAE;MAAEe,KAAK,EAAE;IAAG,CAAC;IAEnD,MAAMC,WAAW,GAAGtB,MAAM,CAACC,IAAI,CAACxB,YAAY,CAAC,CAAC8C,GAAG,CAACb,cAAc,CAAC;IACjE,MAAMJ,MAAM,GAAG,CAAC,GAAG,IAAIkB,GAAG,CAACF,WAAW,CAACC,GAAG,CAAC,CAAC,CAACP,IAAI,EAAEC,KAAK,CAAC,KAAKA,KAAK,CAAC,CAAC,CAAC,CAACV,IAAI,CAAC,CAAC;IAC7E,MAAMc,KAAK,GAAG,CAAC,GAAG,IAAIG,GAAG,CAACF,WAAW,CAACC,GAAG,CAAC,CAAC,CAACP,IAAI,EAAEC,KAAK,CAAC,KAAKD,IAAI,CAAC,CAAC,CAAC,CAACT,IAAI,CAAC,CAAC;IAE3E,OAAO;MAAED,MAAM;MAAEe;IAAM,CAAC;EAC1B,CAAC;;EAED;EACA,MAAMI,QAAQ,GAAGA,CAAA,KAAM;IACrB,MAAMC,GAAG,GAAG,IAAIC,IAAI,CAAC,CAAC;IACtB,MAAMC,YAAY,GAAG,CAACF,GAAG,CAACG,QAAQ,CAAC,CAAC,GAAG,CAAC,EAAEC,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;IACrE,MAAMC,WAAW,GAAGN,GAAG,CAACO,WAAW,CAAC,CAAC,CAACH,QAAQ,CAAC,CAAC;IAEhD1C,WAAW,CAACwC,YAAY,CAAC;IACzBtC,UAAU,CAAC0C,WAAW,CAAC;EACzB,CAAC;;EAED;EACA,MAAME,UAAU,GAAGA,CAAA,KAAM;IACvB,IAAI,CAACzD,YAAY,IAAIuB,MAAM,CAACC,IAAI,CAACxB,YAAY,CAAC,CAACyB,MAAM,KAAK,CAAC,EAAE;IAE7D,MAAMI,MAAM,GAAGN,MAAM,CAACC,IAAI,CAACxB,YAAY,CAAC,CAAC8B,IAAI,CAAC,CAAC;IAC/C,IAAID,MAAM,CAACJ,MAAM,GAAG,CAAC,EAAE;MACrB,MAAM,CAACM,cAAc,EAAEC,aAAa,CAAC,GAAGC,cAAc,CAACJ,MAAM,CAAC,CAAC,CAAC,CAAC;MACjEtB,aAAa,CAACyB,aAAa,CAAC;MAC5BvB,YAAY,CAACsB,cAAc,CAAC;IAC9B;EACF,CAAC;;EAED;EACA,MAAM2B,YAAY,GAAGA,CAAA,KAAM;IACzB,IAAI,CAAC1D,YAAY,IAAI,CAACI,iBAAiB,IAAI,CAACE,UAAU,IAAI,CAACE,SAAS,IAAI,CAACE,QAAQ,IAAI,CAACE,OAAO,EAAE;MAC7F,OAAO;QAAE+C,MAAM,EAAE,EAAE;QAAEC,QAAQ,EAAE;MAAG,CAAC;IACrC;;IAEA;IACA,MAAMC,YAAY,GAAG,CAAC,CAAC;IACvBtC,MAAM,CAACC,IAAI,CAACxB,YAAY,CAAC,CAAC8D,OAAO,CAACC,SAAS,IAAI;MAC7C,MAAM,CAACxB,IAAI,EAAEC,KAAK,CAAC,GAAGP,cAAc,CAAC8B,SAAS,CAAC;MAC/C,MAAMC,OAAO,GAAGC,QAAQ,CAAC1B,IAAI,CAAC;MAC9B,MAAM2B,QAAQ,GAAGD,QAAQ,CAACzB,KAAK,CAAC;MAChC,MAAM2B,YAAY,GAAGF,QAAQ,CAACzD,SAAS,CAAC;MACxC,MAAMwB,aAAa,GAAGiC,QAAQ,CAAC3D,UAAU,CAAC;MAC1C,MAAM8D,UAAU,GAAGH,QAAQ,CAACrD,OAAO,CAAC;MACpC,MAAMuB,WAAW,GAAG8B,QAAQ,CAACvD,QAAQ,CAAC;MAEtC,MAAM2D,SAAS,GAAG,CAACL,OAAO,GAAGG,YAAY,IAAKH,OAAO,KAAKG,YAAY,IAAID,QAAQ,IAAIlC,aAAc,MAClFgC,OAAO,GAAGI,UAAU,IAAKJ,OAAO,KAAKI,UAAU,IAAIF,QAAQ,IAAI/B,WAAY,CAAC;MAE9F,IAAIkC,SAAS,EAAE;QACb;QACA,MAAMC,SAAS,GAAGtE,YAAY,CAAC+D,SAAS,CAAC;QACzC,IAAIjE,eAAe,KAAK,qBAAqB,EAAE;UAC7C+D,YAAY,CAACE,SAAS,CAAC,GAAGO,SAAS;QACrC,CAAC,MAAM,IAAIA,SAAS,CAACxE,eAAe,CAAC,EAAE;UACrC+D,YAAY,CAACE,SAAS,CAAC,GAAG;YAAE,CAACjE,eAAe,GAAGwE,SAAS,CAACxE,eAAe;UAAE,CAAC;QAC7E;MACF;IACF,CAAC,CAAC;;IAEF;IACA,MAAMyE,YAAY,GAAGhD,MAAM,CAACC,IAAI,CAACqC,YAAY,CAAC,CAAC/B,IAAI,CAAC,CAAC0C,CAAC,EAAEC,CAAC,KAAK;MAC5D,MAAM,CAACC,KAAK,EAAEC,MAAM,CAAC,GAAG1C,cAAc,CAACuC,CAAC,CAAC;MACzC,MAAM,CAACI,KAAK,EAAEC,MAAM,CAAC,GAAG5C,cAAc,CAACwC,CAAC,CAAC;MACzC,OAAO,IAAIvB,IAAI,CAACwB,KAAK,EAAEC,MAAM,GAAG,CAAC,CAAC,GAAG,IAAIzB,IAAI,CAAC0B,KAAK,EAAEC,MAAM,GAAG,CAAC,CAAC;IAClE,CAAC,CAAC;;IAEF;IACA,MAAMlB,MAAM,GAAGY,YAAY,CAACzB,GAAG,CAACiB,SAAS,IAAI;MAC3C,MAAM,CAACxB,IAAI,EAAEC,KAAK,CAAC,GAAGP,cAAc,CAAC8B,SAAS,CAAC;MAC/C,MAAMe,IAAI,GAAG,IAAI5B,IAAI,CAACX,IAAI,EAAEC,KAAK,GAAG,CAAC,CAAC;MACtC,OAAOsC,IAAI,CAACC,kBAAkB,CAAC,OAAO,EAAE;QAAEvC,KAAK,EAAE,OAAO;QAAED,IAAI,EAAE;MAAU,CAAC,CAAC;IAC9E,CAAC,CAAC;;IAEF;IACA,MAAMyC,QAAQ,GAAGT,YAAY,CAACzB,GAAG,CAACiB,SAAS,IAAI;MAC7C,IAAIkB,aAAa,GAAG,CAAC;MACrB,IAAIC,aAAa,GAAG,CAAC;MAErB3D,MAAM,CAAC4D,MAAM,CAACtB,YAAY,CAACE,SAAS,CAAC,CAAC,CAACD,OAAO,CAAC3C,QAAQ,IAAI;QACzD,IAAIA,QAAQ,CAACf,iBAAiB,CAAC,EAAE;UAC/B,MAAMgF,UAAU,GAAG7D,MAAM,CAAC4D,MAAM,CAAChE,QAAQ,CAACf,iBAAiB,CAAC,CAAC,CAACiF,MAAM,CAAC,CAACC,GAAG,EAAEC,KAAK,KAAKD,GAAG,GAAGC,KAAK,EAAE,CAAC,CAAC;UACpGN,aAAa,IAAIG,UAAU;UAC3BF,aAAa,EAAE;QACjB;MACF,CAAC,CAAC;MAEF,OAAOA,aAAa,GAAG,CAAC,GAAGD,aAAa,GAAGC,aAAa,GAAG,CAAC;IAC9D,CAAC,CAAC;;IAEF;IACA,MAAMM,gBAAgB,GAAG,IAAIzC,GAAG,CAAC,CAAC;IAClCwB,YAAY,CAACT,OAAO,CAACC,SAAS,IAAI;MAChCxC,MAAM,CAAC4D,MAAM,CAACtB,YAAY,CAACE,SAAS,CAAC,CAAC,CAACD,OAAO,CAAC3C,QAAQ,IAAI;QACzD,IAAIA,QAAQ,CAACf,iBAAiB,CAAC,EAAE;UAC/BmB,MAAM,CAACC,IAAI,CAACL,QAAQ,CAACf,iBAAiB,CAAC,CAAC,CAAC0D,OAAO,CAAC2B,MAAM,IAAI;YACzDD,gBAAgB,CAACE,GAAG,CAACD,MAAM,CAAC;UAC9B,CAAC,CAAC;QACJ;MACF,CAAC,CAAC;IACJ,CAAC,CAAC;;IAEF;IACA,MAAME,MAAM,GAAG,CACb,mBAAmB,EACnB,mBAAmB,EACnB,mBAAmB,EACnB,mBAAmB,EACnB,oBAAoB,EACpB,mBAAmB,EACnB,oBAAoB,EACpB,mBAAmB,EACnB,kBAAkB,EAClB,mBAAmB,EACnB,mBAAmB,EACnB,mBAAmB,EACnB,mBAAmB,EACnB,mBAAmB,EACnB,kBAAkB,EAClB,mBAAmB,EACnB,mBAAmB,EACnB,kBAAkB,EAClB,kBAAkB,EAClB,mBAAmB,CACpB;;IAED;IACA,MAAMC,wBAAwB,GAAGC,KAAK,CAACC,IAAI,CAACN,gBAAgB,CAAC,CAAC1C,GAAG,CAACiD,WAAW,IAAI;MAC/E,MAAMzE,IAAI,GAAGiD,YAAY,CAACzB,GAAG,CAACiB,SAAS,IAAI;QACzC,IAAIiC,KAAK,GAAG,CAAC;QACb,IAAIC,KAAK,GAAG,CAAC;QACb1E,MAAM,CAAC4D,MAAM,CAACtB,YAAY,CAACE,SAAS,CAAC,CAAC,CAACD,OAAO,CAAC3C,QAAQ,IAAI;UACzD,IAAIA,QAAQ,CAACf,iBAAiB,CAAC,IAAIe,QAAQ,CAACf,iBAAiB,CAAC,CAAC2F,WAAW,CAAC,KAAKG,SAAS,EAAE;YACzFF,KAAK,IAAI7E,QAAQ,CAACf,iBAAiB,CAAC,CAAC2F,WAAW,CAAC;YACjDE,KAAK,EAAE;UACT;QACF,CAAC,CAAC;QACF,OAAOA,KAAK,GAAG,CAAC,GAAGD,KAAK,GAAGC,KAAK,GAAG,CAAC;MACtC,CAAC,CAAC;MAEF,MAAME,OAAO,GAAG7E,IAAI,CAAC+D,MAAM,CAAC,CAACC,GAAG,EAAEC,KAAK,KAAKD,GAAG,GAAGC,KAAK,EAAE,CAAC,CAAC,GAAGjE,IAAI,CAACG,MAAM;MACzE,OAAO;QAAEsE,WAAW;QAAEzE,IAAI;QAAE6E;MAAQ,CAAC;IACvC,CAAC,CAAC;;IAEF;IACA,IAAInF,YAAY,EAAE;MAChB;MACAuD,YAAY,CAACT,OAAO,CAAC,CAACC,SAAS,EAAEqC,UAAU,KAAK;QAC9C,MAAMhB,UAAU,GAAGQ,wBAAwB,CAACP,MAAM,CAAC,CAACC,GAAG,EAAE;UAAEhE;QAAK,CAAC,KAAKgE,GAAG,GAAGhE,IAAI,CAAC8E,UAAU,CAAC,EAAE,CAAC,CAAC;QAChG,IAAIhB,UAAU,GAAG,CAAC,EAAE;UAClBQ,wBAAwB,CAAC9B,OAAO,CAAC,CAAC;YAAExC;UAAK,CAAC,KAAK;YAC7CA,IAAI,CAAC8E,UAAU,CAAC,GAAI9E,IAAI,CAAC8E,UAAU,CAAC,GAAGhB,UAAU,GAAI,GAAG;UAC1D,CAAC,CAAC;QACJ;MACF,CAAC,CAAC;IACJ;;IAEA;IACAQ,wBAAwB,CAAC9D,IAAI,CAAC,CAAC0C,CAAC,EAAEC,CAAC,KAAKA,CAAC,CAAC0B,OAAO,GAAG3B,CAAC,CAAC2B,OAAO,CAAC;IAE9D,MAAMvC,QAAQ,GAAGgC,wBAAwB,CAAC9C,GAAG,CAAC,CAAC;MAAEiD,WAAW;MAAEzE;IAAK,CAAC,EAAE+E,KAAK,KAAK;MAC9E,OAAO;QACLC,KAAK,EAAEP,WAAW;QAClBzE,IAAI,EAAEA,IAAI;QACViF,WAAW,EAAEZ,MAAM,CAACU,KAAK,GAAGV,MAAM,CAAClE,MAAM,CAAC;QAC1C+E,eAAe,EAAEb,MAAM,CAACU,KAAK,GAAGV,MAAM,CAAClE,MAAM,CAAC;QAAE;QAChDgF,OAAO,EAAE;MACX,CAAC;IACH,CAAC,CAAC;IAEF,OAAO;MAAE9C,MAAM;MAAEC,QAAQ;MAAEoB;IAAS,CAAC;EACvC,CAAC;;EAED;EACA,MAAM0B,cAAc,GAAG;IACrBC,EAAE,EAAE,iBAAiB;IACrBC,SAAS,EAAGC,KAAK,IAAK;MACpB,MAAM;QAAEC,GAAG;QAAEC,SAAS;QAAEC;MAAO,CAAC,GAAGH,KAAK;;MAExC;MACA,MAAM7B,QAAQ,GAAG6B,KAAK,CAACI,MAAM,CAACC,OAAO,CAACC,YAAY;MAElD,IAAI,CAACnC,QAAQ,IAAIA,QAAQ,CAACvD,MAAM,KAAK,CAAC,EAAE;MAExCqF,GAAG,CAACM,IAAI,CAAC,CAAC;MACVN,GAAG,CAACO,IAAI,GAAG,YAAY;MACvBP,GAAG,CAACQ,SAAS,GAAG,QAAQ;MAExBtC,QAAQ,CAAClB,OAAO,CAAC,CAACyD,aAAa,EAAElB,KAAK,KAAK;QACzC;QACA,IAAImB,KAAK,GAAG,oBAAoB,CAAC,CAAC;QAClC,IAAID,aAAa,GAAG,EAAE,EAAE;UACtBC,KAAK,GAAG,wBAAwB,CAAC,CAAC;QACpC,CAAC,MAAM,IAAID,aAAa,GAAG,EAAE,EAAE;UAC7BC,KAAK,GAAG,wBAAwB,CAAC,CAAC;QACpC;QAEAV,GAAG,CAACW,SAAS,GAAGD,KAAK;;QAErB;QACA,MAAME,MAAM,GAAGV,MAAM,CAACW,CAAC;QACvB,MAAMC,IAAI,GAAGF,MAAM,CAACG,eAAe,CAACxB,KAAK,CAAC;QAC1C,MAAMyB,IAAI,GAAGf,SAAS,CAACgB,GAAG,GAAG,EAAE;;QAE/B;QACAjB,GAAG,CAACkB,QAAQ,CAAC,GAAGT,aAAa,CAACU,OAAO,CAAC,CAAC,CAAC,GAAG,EAAEL,IAAI,EAAEE,IAAI,CAAC;MAC1D,CAAC,CAAC;MAEFhB,GAAG,CAACoB,OAAO,CAAC,CAAC;IACf;EACF,CAAC;EAED,MAAMC,YAAY,GAAG;IACnBC,UAAU,EAAE,IAAI;IAChBC,mBAAmB,EAAE,KAAK;IAC1BC,OAAO,EAAE;MACPC,MAAM,EAAE;QACNC,QAAQ,EAAE,QAAQ;QAClB7E,MAAM,EAAE;UACN8E,aAAa,EAAE,IAAI;UACnBC,UAAU,EAAE,QAAQ;UACpBC,QAAQ,EAAE,EAAE;UACZC,SAAS,EAAE;QACb;MACF,CAAC;MACDC,KAAK,EAAE;QACLC,OAAO,EAAE,IAAI;QACbC,IAAI,EAAE,GAAG3I,iBAAiB,yBAAyB;QACnDiH,IAAI,EAAE;UACJ2B,IAAI,EAAE;QACR,CAAC;QACDC,OAAO,EAAE;UACPC,MAAM,EAAE;QACV;MACF,CAAC;MACDC,OAAO,EAAE;QACPC,SAAS,EAAE;UACT9C,KAAK,EAAE,SAAAA,CAAS+C,OAAO,EAAE;YACvB,OAAO,IAAIA,OAAO,CAACC,OAAO,CAAChD,KAAK,KAAK+C,OAAO,CAACE,MAAM,CAACC,CAAC,CAACvB,OAAO,CAAC,CAAC,CAAC,GAAG;UACrE;QACF;MACF,CAAC;MACDwB,eAAe,EAAE;IACnB,CAAC;IACDC,MAAM,EAAE;MACNT,OAAO,EAAE;QACPlB,GAAG,EAAE,CAAC,CAAE;MACV;IACF,CAAC;IACDf,MAAM,EAAE;MACNwC,CAAC,EAAE;QACDG,WAAW,EAAE,IAAI;QACjBC,KAAK,EAAE;UACLC,QAAQ,EAAE,SAAAA,CAAStE,KAAK,EAAE;YACxB,OAAOA,KAAK,GAAG,GAAG;UACpB;QACF,CAAC;QACDsD,KAAK,EAAE;UACLC,OAAO,EAAE,IAAI;UACbC,IAAI,EAAE;QACR;MACF,CAAC;MACDpB,CAAC,EAAE;QACDkB,KAAK,EAAE;UACLC,OAAO,EAAE,IAAI;UACbC,IAAI,EAAE;QACR;MACF;IACF,CAAC;IACDzJ,QAAQ,EAAE;MACRwK,KAAK,EAAE;QACLC,MAAM,EAAE;MACV;IACF;EACF,CAAC;EAED,MAAMC,mBAAmB,GAAGtH,sBAAsB,CAAC,CAAC;EACpD,MAAM;IAAEb,MAAM;IAAEe;EAAM,CAAC,GAAGD,uBAAuB,CAAC,CAAC;EACnD,MAAMsH,SAAS,GAAGvG,YAAY,CAAC,CAAC;;EAEhC;EACA,MAAMwG,wBAAwB,GAAG;IAC/B,GAAG/B,YAAY;IACfhB,YAAY,EAAE8C,SAAS,CAACjF;EAC1B,CAAC;EAED,oBACEvF,OAAA;IAAA0K,QAAA,eAEE1K,OAAA;MAAK2K,SAAS,EAAC,cAAc;MAAAD,QAAA,gBAC3B1K,OAAA;QAAK2K,SAAS,EAAC,sBAAsB;QAAAD,QAAA,eACnC1K,OAAA,CAAChB,IAAI;UAAC4L,EAAE,EAAC,GAAG;UAACD,SAAS,EAAC,oBAAoB;UAAAD,QAAA,gBACzC1K,OAAA;YAAG2K,SAAS,EAAC;UAAkB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,SACtC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACNhL,OAAA,CAACb,gBAAgB;QACfkB,eAAe,EAAEA,eAAgB;QACjC4K,gBAAgB,EAAE3K;MAAmB;QAAAuK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACtC,CAAC,eACFhL,OAAA;QAAG2K,SAAS,EAAC,eAAe;QAAAD,QAAA,EAAC;MAAiB;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC,eAClDhL,OAAA;QAAA6K,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,EACRvK,OAAO,gBACNT,OAAA;QAAK2K,SAAS,EAAC,aAAa;QAAAD,QAAA,eAC1B1K,OAAA;UAAK2K,SAAS,EAAC,gBAAgB;UAACO,IAAI,EAAC,QAAQ;UAAAR,QAAA,eAC3C1K,OAAA;YAAM2K,SAAS,EAAC,iBAAiB;YAAAD,QAAA,EAAC;UAAU;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChD;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,gBAENhL,OAAA;QAAK2K,SAAS,EAAC,kBAAkB;QAAAD,QAAA,EAC9BnK,YAAY,iBACXP,OAAA;UAAK2K,SAAS,EAAC,iBAAiB;UAAAD,QAAA,gBAE9B1K,OAAA;YAAK2K,SAAS,EAAC,gBAAgB;YAAAD,QAAA,gBAE7B1K,OAAA;cAAK2K,SAAS,EAAC,kCAAkC;cAAAD,QAAA,gBAC/C1K,OAAA;gBAAO2K,SAAS,EAAC,eAAe;gBAAAD,QAAA,EAAC;cAAS;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAClDhL,OAAA;gBACE2K,SAAS,EAAC,aAAa;gBACvB7E,KAAK,EAAEnF,iBAAkB;gBACzBwK,QAAQ,EAAGC,CAAC,IAAKxK,oBAAoB,CAACwK,CAAC,CAACC,MAAM,CAACvF,KAAK,CAAE;gBAAA4E,QAAA,gBAEtD1K,OAAA;kBAAQ8F,KAAK,EAAC,EAAE;kBAAA4E,QAAA,EAAC;gBAAgB;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,EACzCT,mBAAmB,CAAClH,GAAG,CAACiI,IAAI,iBAC3BtL,OAAA;kBAAmB8F,KAAK,EAAEwF,IAAK;kBAAAZ,QAAA,EAAEY;gBAAI,GAAxBA,IAAI;kBAAAT,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAA6B,CAC/C,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,eAGNhL,OAAA;cAAK2K,SAAS,EAAC,eAAe;cAAAD,QAAA,gBAE5B1K,OAAA;gBAAK2K,SAAS,EAAC,YAAY;gBAAAD,QAAA,gBACzB1K,OAAA;kBAAO2K,SAAS,EAAC,eAAe;kBAAAD,QAAA,EAAC;gBAAU;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACnDhL,OAAA;kBAAK2K,SAAS,EAAC,sBAAsB;kBAAAD,QAAA,gBACnC1K,OAAA;oBACE8F,KAAK,EAAEjF,UAAW;oBAClBsK,QAAQ,EAAGC,CAAC,IAAKtK,aAAa,CAACsK,CAAC,CAACC,MAAM,CAACvF,KAAK,CAAE;oBAAA4E,QAAA,gBAE/C1K,OAAA;sBAAQ8F,KAAK,EAAC,EAAE;sBAAA4E,QAAA,EAAC;oBAAK;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,EAC9B5I,MAAM,CAACiB,GAAG,CAACN,KAAK,iBACf/C,OAAA;sBAAoB8F,KAAK,EAAE/C,KAAM;sBAAA2H,QAAA,EAC9B,IAAIjH,IAAI,CAAC,IAAI,EAAEV,KAAK,GAAG,CAAC,CAAC,CAACuC,kBAAkB,CAAC,OAAO,EAAE;wBAAEvC,KAAK,EAAE;sBAAQ,CAAC;oBAAC,GAD/DA,KAAK;sBAAA8H,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAEV,CACT,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACI,CAAC,eACThL,OAAA;oBACE8F,KAAK,EAAE/E,SAAU;oBACjBoK,QAAQ,EAAGC,CAAC,IAAKpK,YAAY,CAACoK,CAAC,CAACC,MAAM,CAACvF,KAAK,CAAE;oBAAA4E,QAAA,gBAE9C1K,OAAA;sBAAQ8F,KAAK,EAAC,EAAE;sBAAA4E,QAAA,EAAC;oBAAI;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,EAC7B7H,KAAK,CAACE,GAAG,CAACP,IAAI,iBACb9C,OAAA;sBAAmB8F,KAAK,EAAEhD,IAAK;sBAAA4H,QAAA,EAAE5H;oBAAI,GAAxBA,IAAI;sBAAA+H,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAA6B,CAC/C,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACI,CAAC,eACThL,OAAA;oBACE2K,SAAS,EAAC,cAAc;oBACxBY,OAAO,EAAEvH,UAAW;oBAAA0G,QAAA,EACrB;kBAED;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAGNhL,OAAA;gBAAK2K,SAAS,EAAC,YAAY;gBAAAD,QAAA,gBACzB1K,OAAA;kBAAO2K,SAAS,EAAC,eAAe;kBAAAD,QAAA,EAAC;gBAAQ;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACjDhL,OAAA;kBAAK2K,SAAS,EAAC,oBAAoB;kBAAAD,QAAA,gBACjC1K,OAAA;oBACE8F,KAAK,EAAE7E,QAAS;oBAChBkK,QAAQ,EAAGC,CAAC,IAAKlK,WAAW,CAACkK,CAAC,CAACC,MAAM,CAACvF,KAAK,CAAE;oBAAA4E,QAAA,gBAE7C1K,OAAA;sBAAQ8F,KAAK,EAAC,EAAE;sBAAA4E,QAAA,EAAC;oBAAK;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,EAC9B5I,MAAM,CAACiB,GAAG,CAACN,KAAK,iBACf/C,OAAA;sBAAoB8F,KAAK,EAAE/C,KAAM;sBAAA2H,QAAA,EAC9B,IAAIjH,IAAI,CAAC,IAAI,EAAEV,KAAK,GAAG,CAAC,CAAC,CAACuC,kBAAkB,CAAC,OAAO,EAAE;wBAAEvC,KAAK,EAAE;sBAAQ,CAAC;oBAAC,GAD/DA,KAAK;sBAAA8H,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAEV,CACT,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACI,CAAC,eACThL,OAAA;oBACE8F,KAAK,EAAE3E,OAAQ;oBACfgK,QAAQ,EAAGC,CAAC,IAAKhK,UAAU,CAACgK,CAAC,CAACC,MAAM,CAACvF,KAAK,CAAE;oBAAA4E,QAAA,gBAE5C1K,OAAA;sBAAQ8F,KAAK,EAAC,EAAE;sBAAA4E,QAAA,EAAC;oBAAI;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,EAC7B7H,KAAK,CAACE,GAAG,CAACP,IAAI,iBACb9C,OAAA;sBAAmB8F,KAAK,EAAEhD,IAAK;sBAAA4H,QAAA,EAAE5H;oBAAI,GAAxBA,IAAI;sBAAA+H,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAA6B,CAC/C,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACI,CAAC,eACThL,OAAA;oBACE2K,SAAS,EAAC,YAAY;oBACtBY,OAAO,EAAEhI,QAAS;oBAAAmH,QAAA,EACnB;kBAED;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAENhL,OAAA;cAAK2K,SAAS,EAAC,8BAA8B;cAAAD,QAAA,eAC3C1K,OAAA;gBACE2K,SAAS,EAAC,qBAAqB;gBAC/Ba,IAAI,EAAC,QAAQ;gBACbD,OAAO,EAAEA,CAAA,KAAMjK,eAAe,CAAC,IAAI,CAAE;gBACrC8H,KAAK,EAAC,gBAAgB;gBAAAsB,QAAA,eAEtB1K,OAAA;kBAAG2K,SAAS,EAAC;gBAAY;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxB;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,EAGLrK,iBAAiB,IAAI6J,SAAS,CAACrG,QAAQ,CAACnC,MAAM,GAAG,CAAC,gBACjDhC,OAAA;YAAK2K,SAAS,EAAC,eAAe;YAACc,KAAK,EAAE;cAAEC,MAAM,EAAE;YAAQ,CAAE;YAAAhB,QAAA,eACxD1K,OAAA,CAACF,IAAI;cAAC+B,IAAI,EAAE2I,SAAU;cAAC/C,OAAO,EAAEgD,wBAAyB;cAAC5B,OAAO,EAAE,CAAC5B,cAAc;YAAE;cAAA4D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpF,CAAC,gBAENhL,OAAA;YAAK2K,SAAS,EAAC,kBAAkB;YAAAD,QAAA,EAAC;UAElC;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MACN;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CACN,EAGA3J,YAAY,iBACXrB,OAAA;QAAK2K,SAAS,EAAC,wBAAwB;QAACY,OAAO,EAAEA,CAAA,KAAMjK,eAAe,CAAC,KAAK,CAAE;QAAAoJ,QAAA,eAC5E1K,OAAA;UAAK2K,SAAS,EAAC,0BAA0B;UAACY,OAAO,EAAGH,CAAC,IAAKA,CAAC,CAACO,eAAe,CAAC,CAAE;UAAAjB,QAAA,gBAC5E1K,OAAA;YAAK2K,SAAS,EAAC,uBAAuB;YAAAD,QAAA,gBACpC1K,OAAA;cAAI2K,SAAS,EAAC,sBAAsB;cAAAD,QAAA,EAAC;YAAc;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACxDhL,OAAA;cACEwL,IAAI,EAAC,QAAQ;cACbb,SAAS,EAAC,sBAAsB;cAChCY,OAAO,EAAEA,CAAA,KAAMjK,eAAe,CAAC,KAAK,CAAE;cACtC,cAAW,OAAO;cAAAoJ,QAAA,EACnB;YAED;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eACNhL,OAAA;YAAK2K,SAAS,EAAC,qBAAqB;YAAAD,QAAA,eAClC1K,OAAA;cAAK2K,SAAS,EAAC,iBAAiB;cAAAD,QAAA,eAC9B1K,OAAA;gBAAK2K,SAAS,EAAC,2BAA2B;gBAAAD,QAAA,gBACxC1K,OAAA;kBAAK2K,SAAS,EAAC,wBAAwB;kBAAAD,QAAA,gBACrC1K,OAAA;oBACE2K,SAAS,EAAC,kBAAkB;oBAC5Ba,IAAI,EAAC,UAAU;oBACfN,IAAI,EAAC,QAAQ;oBACbhE,EAAE,EAAC,eAAe;oBAClB0E,OAAO,EAAErK,YAAa;oBACtB4J,QAAQ,EAAGC,CAAC,IAAK5J,eAAe,CAAC4J,CAAC,CAACC,MAAM,CAACO,OAAO;kBAAE;oBAAAf,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACpD,CAAC,eACFhL,OAAA;oBAAO2K,SAAS,EAAC,kBAAkB;oBAACkB,OAAO,EAAC,eAAe;oBAAAnB,QAAA,eACzD1K,OAAA;sBAAA0K,QAAA,EAAQ;oBAAe;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC3B,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACL,CAAC,eACNhL,OAAA;kBAAK2K,SAAS,EAAC,sBAAsB;kBAAAD,QAAA,EAAC;gBAGtC;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAC7K,EAAA,CAxiBID,QAAQ;EAAA,QACajB,SAAS;AAAA;AAAA6M,EAAA,GAD9B5L,QAAQ;AA0iBd,eAAeA,QAAQ;AAAC,IAAA4L,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}