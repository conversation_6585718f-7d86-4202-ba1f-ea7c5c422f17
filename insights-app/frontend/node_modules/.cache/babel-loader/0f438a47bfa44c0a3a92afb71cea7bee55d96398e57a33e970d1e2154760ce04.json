{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Desktop/Coding/BVS/CrystalBall/insights-app/frontend/src/components/Strategy.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Link, useParams } from 'react-router-dom';\nimport axios from 'axios';\nimport StrategyDropdown from './StrategyDropdown';\nimport { Chart as ChartJS, CategoryScale, LinearScale, PointElement, LineElement, Title, Tooltip, Legend } from 'chart.js';\nimport { Line } from 'react-chartjs-2';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nChartJS.register(CategoryScale, LinearScale, PointElement, LineElement, Title, Tooltip, Legend);\nconst Strategy = () => {\n  _s();\n  const {\n    strategyName\n  } = useParams();\n  const [currentStrategy, setCurrentStrategy] = useState(strategyName || 'Alle (Durchschnitt)');\n  const [strategyData, setStrategyData] = useState(null);\n  const [loading, setLoading] = useState(true);\n\n  // Chart state\n  const [selectedAttribute, setSelectedAttribute] = useState('');\n  const [startMonth, setStartMonth] = useState('');\n  const [startYear, setStartYear] = useState('');\n  const [endMonth, setEndMonth] = useState('');\n  const [endYear, setEndYear] = useState('');\n  useEffect(() => {\n    fetchStrategyData(currentStrategy);\n  }, [currentStrategy]);\n  const fetchStrategyData = async strategy => {\n    try {\n      setLoading(true);\n      const response = await axios.get(`/api/strategy/${strategy}/`);\n      setStrategyData(response.data);\n\n      // Initialize dropdowns with first available data\n      if (response.data && Object.keys(response.data).length > 0) {\n        const firstMonth = Object.keys(response.data)[0];\n        const firstStrategy = Object.keys(response.data[firstMonth])[0];\n        const attributes = Object.keys(response.data[firstMonth][firstStrategy]);\n        if (attributes.length > 0 && !selectedAttribute) {\n          setSelectedAttribute(attributes[0]);\n        }\n\n        // Set default date range\n        const months = Object.keys(response.data).sort();\n        if (months.length > 0) {\n          const [startYearMonth, startMonthNum] = parseMonthYear(months[0]);\n          const [endYearMonth, endMonthNum] = parseMonthYear(months[months.length - 1]);\n          if (!startMonth) {\n            setStartMonth(startMonthNum);\n            setStartYear(startYearMonth);\n          }\n          if (!endMonth) {\n            setEndMonth(endMonthNum);\n            setEndYear(endYearMonth);\n          }\n        }\n      }\n    } catch (error) {\n      console.error('Error fetching strategy data:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Helper function to parse month-year string like \"2025 - 04\"\n  const parseMonthYear = monthYearStr => {\n    const [year, month] = monthYearStr.split(' - ');\n    return [year, month];\n  };\n\n  // Helper function to create month-year string\n  // const createMonthYearStr = (year, month) => {\n  //   return `${year} - ${month.padStart(2, '0')}`;\n  // };\n\n  // Get available attributes from data\n  const getAvailableAttributes = () => {\n    if (!strategyData || Object.keys(strategyData).length === 0) return [];\n    const firstMonth = Object.keys(strategyData)[0];\n    const firstStrategy = Object.keys(strategyData[firstMonth])[0];\n    return Object.keys(strategyData[firstMonth][firstStrategy]);\n  };\n\n  // Get available months and years\n  const getAvailableMonthsYears = () => {\n    if (!strategyData) return {\n      months: [],\n      years: []\n    };\n    const monthsYears = Object.keys(strategyData).map(parseMonthYear);\n    const months = [...new Set(monthsYears.map(([year, month]) => month))].sort();\n    const years = [...new Set(monthsYears.map(([year, month]) => year))].sort();\n    return {\n      months,\n      years\n    };\n  };\n\n  // Set end date to current month/year\n  const setToNow = () => {\n    const now = new Date();\n    const currentMonth = (now.getMonth() + 1).toString().padStart(2, '0');\n    const currentYear = now.getFullYear().toString();\n    setEndMonth(currentMonth);\n    setEndYear(currentYear);\n  };\n\n  // Set start date to first available month/year\n  const setToStart = () => {\n    if (!strategyData || Object.keys(strategyData).length === 0) return;\n    const months = Object.keys(strategyData).sort();\n    if (months.length > 0) {\n      const [startYearMonth, startMonthNum] = parseMonthYear(months[0]);\n      setStartMonth(startMonthNum);\n      setStartYear(startYearMonth);\n    }\n  };\n\n  // Get chart data filtered by selected strategy\n  const getChartData = () => {\n    if (!strategyData || !selectedAttribute || !startMonth || !startYear || !endMonth || !endYear) {\n      return {\n        labels: [],\n        datasets: []\n      };\n    }\n\n    // Filter data by date range\n    const filteredData = {};\n    Object.keys(strategyData).forEach(monthYear => {\n      const [year, month] = parseMonthYear(monthYear);\n      const yearNum = parseInt(year);\n      const monthNum = parseInt(month);\n      const startYearNum = parseInt(startYear);\n      const startMonthNum = parseInt(startMonth);\n      const endYearNum = parseInt(endYear);\n      const endMonthNum = parseInt(endMonth);\n      const isInRange = (yearNum > startYearNum || yearNum === startYearNum && monthNum >= startMonthNum) && (yearNum < endYearNum || yearNum === endYearNum && monthNum <= endMonthNum);\n      if (isInRange) {\n        // Filter by selected strategy\n        const monthData = strategyData[monthYear];\n        if (currentStrategy === \"Alle (Durchschnitt)\") {\n          filteredData[monthYear] = monthData;\n        } else if (monthData[currentStrategy]) {\n          filteredData[monthYear] = {\n            [currentStrategy]: monthData[currentStrategy]\n          };\n        }\n      }\n    });\n\n    // Sort months chronologically\n    const sortedMonths = Object.keys(filteredData).sort((a, b) => {\n      const [yearA, monthA] = parseMonthYear(a);\n      const [yearB, monthB] = parseMonthYear(b);\n      return new Date(yearA, monthA - 1) - new Date(yearB, monthB - 1);\n    });\n\n    // Create labels (formatted month names)\n    const labels = sortedMonths.map(monthYear => {\n      const [year, month] = parseMonthYear(monthYear);\n      const date = new Date(year, month - 1);\n      return date.toLocaleDateString('en-US', {\n        month: 'short',\n        year: 'numeric'\n      });\n    });\n\n    // Get all unique currencies/subcategories for the selected attribute\n    const allSubcategories = new Set();\n    sortedMonths.forEach(monthYear => {\n      Object.values(filteredData[monthYear]).forEach(strategy => {\n        if (strategy[selectedAttribute]) {\n          Object.keys(strategy[selectedAttribute]).forEach(subcat => {\n            allSubcategories.add(subcat);\n          });\n        }\n      });\n    });\n\n    // Generate colors for each line\n    const colors = ['rgb(255, 99, 132)', 'rgb(54, 162, 235)', 'rgb(255, 205, 86)', 'rgb(75, 192, 192)', 'rgb(153, 102, 255)', 'rgb(255, 159, 64)', 'rgb(199, 199, 199)', 'rgb(83, 102, 147)'];\n\n    // Create datasets for each subcategory\n    const datasets = Array.from(allSubcategories).map((subcategory, index) => {\n      const data = sortedMonths.map(monthYear => {\n        // Aggregate data across selected strategies for this month and subcategory\n        let total = 0;\n        let count = 0;\n        Object.values(filteredData[monthYear]).forEach(strategy => {\n          if (strategy[selectedAttribute] && strategy[selectedAttribute][subcategory] !== undefined) {\n            total += strategy[selectedAttribute][subcategory];\n            count++;\n          }\n        });\n        return count > 0 ? total / count : 0; // Average across strategies\n      });\n      return {\n        label: subcategory,\n        data: data,\n        borderColor: colors[index % colors.length],\n        backgroundColor: colors[index % colors.length] + '20',\n        tension: 0.1\n      };\n    });\n    return {\n      labels,\n      datasets\n    };\n  };\n  const chartOptions = {\n    responsive: true,\n    plugins: {\n      legend: {\n        position: 'bottom',\n        labels: {\n          usePointStyle: true,\n          pointStyle: 'circle',\n          boxWidth: 8,\n          boxHeight: 8\n        }\n      },\n      title: {\n        display: true,\n        text: `${selectedAttribute} Distribution Over Time`\n      }\n    },\n    scales: {\n      y: {\n        beginAtZero: true,\n        ticks: {\n          callback: function (value) {\n            return value + '%';\n          }\n        },\n        title: {\n          display: true,\n          text: 'Percentage (%)'\n        }\n      },\n      x: {\n        title: {\n          display: true,\n          text: 'Month'\n        }\n      }\n    }\n  };\n  const availableAttributes = getAvailableAttributes();\n  const {\n    months,\n    years\n  } = getAvailableMonthsYears();\n  const chartData = getChartData();\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"content-page\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"home-arrow-container\",\n        children: /*#__PURE__*/_jsxDEV(Link, {\n          to: \"/\",\n          className: \"back-to-home-arrow\",\n          children: [/*#__PURE__*/_jsxDEV(\"i\", {\n            className: \"bi bi-arrow-left\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 281,\n            columnNumber: 13\n          }, this), \" Home\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 280,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 279,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(StrategyDropdown, {\n        currentStrategy: currentStrategy,\n        onStrategyChange: setCurrentStrategy\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 284,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"section_title\",\n        children: \"Strategy Insights\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 288,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"hr\", {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 289,\n        columnNumber: 9\n      }, this), loading ? /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"spinner-border\",\n          role: \"status\",\n          children: /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"visually-hidden\",\n            children: \"Loading...\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 293,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 292,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 291,\n        columnNumber: 11\n      }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"strategy-content\",\n        children: strategyData && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"chart-container\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"chart-controls\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"control-group attribute-dropdown\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"control-label\",\n                children: \"Attribute\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 304,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                className: \"form-select\",\n                value: selectedAttribute,\n                onChange: e => setSelectedAttribute(e.target.value),\n                children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"\",\n                  children: \"Select Attribute\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 310,\n                  columnNumber: 23\n                }, this), availableAttributes.map(attr => /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: attr,\n                  children: attr\n                }, attr, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 312,\n                  columnNumber: 25\n                }, this))]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 305,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 303,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"date-controls\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"date-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"control-label\",\n                  children: \"Start Date\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 321,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"start-date-container\",\n                  children: [/*#__PURE__*/_jsxDEV(\"select\", {\n                    value: startMonth,\n                    onChange: e => setStartMonth(e.target.value),\n                    children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                      value: \"\",\n                      children: \"Month\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 327,\n                      columnNumber: 27\n                    }, this), months.map(month => /*#__PURE__*/_jsxDEV(\"option\", {\n                      value: month,\n                      children: new Date(2000, month - 1).toLocaleDateString('en-US', {\n                        month: 'short'\n                      })\n                    }, month, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 329,\n                      columnNumber: 29\n                    }, this))]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 323,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                    value: startYear,\n                    onChange: e => setStartYear(e.target.value),\n                    children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                      value: \"\",\n                      children: \"Year\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 338,\n                      columnNumber: 27\n                    }, this), years.map(year => /*#__PURE__*/_jsxDEV(\"option\", {\n                      value: year,\n                      children: year\n                    }, year, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 340,\n                      columnNumber: 29\n                    }, this))]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 334,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    className: \"start-button\",\n                    onClick: setToStart,\n                    children: \"Start\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 343,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 322,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 320,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"date-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"control-label\",\n                  children: \"End Date\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 354,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"end-date-container\",\n                  children: [/*#__PURE__*/_jsxDEV(\"select\", {\n                    value: endMonth,\n                    onChange: e => setEndMonth(e.target.value),\n                    children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                      value: \"\",\n                      children: \"Month\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 360,\n                      columnNumber: 27\n                    }, this), months.map(month => /*#__PURE__*/_jsxDEV(\"option\", {\n                      value: month,\n                      children: new Date(2000, month - 1).toLocaleDateString('en-US', {\n                        month: 'short'\n                      })\n                    }, month, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 362,\n                      columnNumber: 29\n                    }, this))]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 356,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                    value: endYear,\n                    onChange: e => setEndYear(e.target.value),\n                    children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                      value: \"\",\n                      children: \"Year\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 371,\n                      columnNumber: 27\n                    }, this), years.map(year => /*#__PURE__*/_jsxDEV(\"option\", {\n                      value: year,\n                      children: year\n                    }, year, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 373,\n                      columnNumber: 29\n                    }, this))]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 367,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    className: \"now-button\",\n                    onClick: setToNow,\n                    children: \"Now\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 376,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 355,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 353,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 318,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 301,\n            columnNumber: 17\n          }, this), selectedAttribute && chartData.datasets.length > 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"chart-wrapper\",\n            style: {\n              height: '400px'\n            },\n            children: /*#__PURE__*/_jsxDEV(Line, {\n              data: chartData,\n              options: chartOptions\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 390,\n              columnNumber: 21\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 389,\n            columnNumber: 19\n          }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"alert alert-info\",\n            children: \"Please select an attribute and ensure data is available for the selected date range.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 393,\n            columnNumber: 19\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 299,\n          columnNumber: 15\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 297,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 278,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 276,\n    columnNumber: 5\n  }, this);\n};\n_s(Strategy, \"atB9glxjlJs0fJdw26zYur8N8BQ=\", false, function () {\n  return [useParams];\n});\n_c = Strategy;\nexport default Strategy;\nvar _c;\n$RefreshReg$(_c, \"Strategy\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Link", "useParams", "axios", "StrategyDropdown", "Chart", "ChartJS", "CategoryScale", "LinearScale", "PointElement", "LineElement", "Title", "<PERSON><PERSON><PERSON>", "Legend", "Line", "jsxDEV", "_jsxDEV", "register", "Strategy", "_s", "strategyName", "currentStrategy", "setCurrentStrategy", "strategyData", "setStrategyData", "loading", "setLoading", "selectedAttribute", "setSelectedAttribute", "startMonth", "setStartMonth", "startYear", "setStartYear", "endMonth", "setEndMonth", "endYear", "setEndYear", "fetchStrategyData", "strategy", "response", "get", "data", "Object", "keys", "length", "firstMonth", "firstStrategy", "attributes", "months", "sort", "startYearMonth", "startMonthNum", "parseMonthYear", "endYearMonth", "endMonthNum", "error", "console", "monthYearStr", "year", "month", "split", "getAvailableAttributes", "getAvailableMonthsYears", "years", "monthsYears", "map", "Set", "setToNow", "now", "Date", "currentMonth", "getMonth", "toString", "padStart", "currentYear", "getFullYear", "setToStart", "getChartData", "labels", "datasets", "filteredData", "for<PERSON>ach", "monthYear", "yearNum", "parseInt", "monthNum", "startYearNum", "endYearNum", "isInRange", "monthData", "sortedMonths", "a", "b", "yearA", "monthA", "yearB", "monthB", "date", "toLocaleDateString", "allSubcategories", "values", "subcat", "add", "colors", "Array", "from", "subcategory", "index", "total", "count", "undefined", "label", "borderColor", "backgroundColor", "tension", "chartOptions", "responsive", "plugins", "legend", "position", "usePointStyle", "pointStyle", "boxWidth", "boxHeight", "title", "display", "text", "scales", "y", "beginAtZero", "ticks", "callback", "value", "x", "availableAttributes", "chartData", "children", "className", "to", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onStrategyChange", "role", "onChange", "e", "target", "attr", "onClick", "style", "height", "options", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Desktop/Coding/BVS/CrystalBall/insights-app/frontend/src/components/Strategy.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { Link, useParams } from 'react-router-dom';\nimport axios from 'axios';\nimport StrategyDropdown from './StrategyDropdown';\nimport {\n  Chart as ChartJS,\n  CategoryScale,\n  LinearScale,\n  PointElement,\n  LineElement,\n  Title,\n  Tooltip,\n  Legend,\n} from 'chart.js';\nimport { Line } from 'react-chartjs-2';\n\nChartJS.register(\n  CategoryScale,\n  LinearScale,\n  PointElement,\n  LineElement,\n  Title,\n  Tooltip,\n  Legend\n);\n\nconst Strategy = () => {\n  const { strategyName } = useParams();\n  const [currentStrategy, setCurrentStrategy] = useState(strategyName || 'Alle (Durchschnitt)');\n  const [strategyData, setStrategyData] = useState(null);\n  const [loading, setLoading] = useState(true);\n\n  // Chart state\n  const [selectedAttribute, setSelectedAttribute] = useState('');\n  const [startMonth, setStartMonth] = useState('');\n  const [startYear, setStartYear] = useState('');\n  const [endMonth, setEndMonth] = useState('');\n  const [endYear, setEndYear] = useState('');\n\n  useEffect(() => {\n    fetchStrategyData(currentStrategy);\n  }, [currentStrategy]);\n\n  const fetchStrategyData = async (strategy) => {\n    try {\n      setLoading(true);\n      const response = await axios.get(`/api/strategy/${strategy}/`);\n      setStrategyData(response.data);\n\n      // Initialize dropdowns with first available data\n      if (response.data && Object.keys(response.data).length > 0) {\n        const firstMonth = Object.keys(response.data)[0];\n        const firstStrategy = Object.keys(response.data[firstMonth])[0];\n        const attributes = Object.keys(response.data[firstMonth][firstStrategy]);\n\n        if (attributes.length > 0 && !selectedAttribute) {\n          setSelectedAttribute(attributes[0]);\n        }\n\n        // Set default date range\n        const months = Object.keys(response.data).sort();\n        if (months.length > 0) {\n          const [startYearMonth, startMonthNum] = parseMonthYear(months[0]);\n          const [endYearMonth, endMonthNum] = parseMonthYear(months[months.length - 1]);\n\n          if (!startMonth) {\n            setStartMonth(startMonthNum);\n            setStartYear(startYearMonth);\n          }\n          if (!endMonth) {\n            setEndMonth(endMonthNum);\n            setEndYear(endYearMonth);\n          }\n        }\n      }\n    } catch (error) {\n      console.error('Error fetching strategy data:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Helper function to parse month-year string like \"2025 - 04\"\n  const parseMonthYear = (monthYearStr) => {\n    const [year, month] = monthYearStr.split(' - ');\n    return [year, month];\n  };\n\n  // Helper function to create month-year string\n  // const createMonthYearStr = (year, month) => {\n  //   return `${year} - ${month.padStart(2, '0')}`;\n  // };\n\n  // Get available attributes from data\n  const getAvailableAttributes = () => {\n    if (!strategyData || Object.keys(strategyData).length === 0) return [];\n\n    const firstMonth = Object.keys(strategyData)[0];\n    const firstStrategy = Object.keys(strategyData[firstMonth])[0];\n    return Object.keys(strategyData[firstMonth][firstStrategy]);\n  };\n\n  // Get available months and years\n  const getAvailableMonthsYears = () => {\n    if (!strategyData) return { months: [], years: [] };\n\n    const monthsYears = Object.keys(strategyData).map(parseMonthYear);\n    const months = [...new Set(monthsYears.map(([year, month]) => month))].sort();\n    const years = [...new Set(monthsYears.map(([year, month]) => year))].sort();\n\n    return { months, years };\n  };\n\n  // Set end date to current month/year\n  const setToNow = () => {\n    const now = new Date();\n    const currentMonth = (now.getMonth() + 1).toString().padStart(2, '0');\n    const currentYear = now.getFullYear().toString();\n\n    setEndMonth(currentMonth);\n    setEndYear(currentYear);\n  };\n\n  // Set start date to first available month/year\n  const setToStart = () => {\n    if (!strategyData || Object.keys(strategyData).length === 0) return;\n    \n    const months = Object.keys(strategyData).sort();\n    if (months.length > 0) {\n      const [startYearMonth, startMonthNum] = parseMonthYear(months[0]);\n      setStartMonth(startMonthNum);\n      setStartYear(startYearMonth);\n    }\n  };\n\n  // Get chart data filtered by selected strategy\n  const getChartData = () => {\n    if (!strategyData || !selectedAttribute || !startMonth || !startYear || !endMonth || !endYear) {\n      return { labels: [], datasets: [] };\n    }\n\n    // Filter data by date range\n    const filteredData = {};\n    Object.keys(strategyData).forEach(monthYear => {\n      const [year, month] = parseMonthYear(monthYear);\n      const yearNum = parseInt(year);\n      const monthNum = parseInt(month);\n      const startYearNum = parseInt(startYear);\n      const startMonthNum = parseInt(startMonth);\n      const endYearNum = parseInt(endYear);\n      const endMonthNum = parseInt(endMonth);\n\n      const isInRange = (yearNum > startYearNum || (yearNum === startYearNum && monthNum >= startMonthNum)) &&\n                       (yearNum < endYearNum || (yearNum === endYearNum && monthNum <= endMonthNum));\n\n      if (isInRange) {\n        // Filter by selected strategy\n        const monthData = strategyData[monthYear];\n        if (currentStrategy === \"Alle (Durchschnitt)\") {\n          filteredData[monthYear] = monthData;\n        } else if (monthData[currentStrategy]) {\n          filteredData[monthYear] = { [currentStrategy]: monthData[currentStrategy] };\n        }\n      }\n    });\n\n    // Sort months chronologically\n    const sortedMonths = Object.keys(filteredData).sort((a, b) => {\n      const [yearA, monthA] = parseMonthYear(a);\n      const [yearB, monthB] = parseMonthYear(b);\n      return new Date(yearA, monthA - 1) - new Date(yearB, monthB - 1);\n    });\n\n    // Create labels (formatted month names)\n    const labels = sortedMonths.map(monthYear => {\n      const [year, month] = parseMonthYear(monthYear);\n      const date = new Date(year, month - 1);\n      return date.toLocaleDateString('en-US', { month: 'short', year: 'numeric' });\n    });\n\n    // Get all unique currencies/subcategories for the selected attribute\n    const allSubcategories = new Set();\n    sortedMonths.forEach(monthYear => {\n      Object.values(filteredData[monthYear]).forEach(strategy => {\n        if (strategy[selectedAttribute]) {\n          Object.keys(strategy[selectedAttribute]).forEach(subcat => {\n            allSubcategories.add(subcat);\n          });\n        }\n      });\n    });\n\n    // Generate colors for each line\n    const colors = [\n      'rgb(255, 99, 132)',\n      'rgb(54, 162, 235)',\n      'rgb(255, 205, 86)',\n      'rgb(75, 192, 192)',\n      'rgb(153, 102, 255)',\n      'rgb(255, 159, 64)',\n      'rgb(199, 199, 199)',\n      'rgb(83, 102, 147)',\n    ];\n\n    // Create datasets for each subcategory\n    const datasets = Array.from(allSubcategories).map((subcategory, index) => {\n      const data = sortedMonths.map(monthYear => {\n        // Aggregate data across selected strategies for this month and subcategory\n        let total = 0;\n        let count = 0;\n        Object.values(filteredData[monthYear]).forEach(strategy => {\n          if (strategy[selectedAttribute] && strategy[selectedAttribute][subcategory] !== undefined) {\n            total += strategy[selectedAttribute][subcategory];\n            count++;\n          }\n        });\n        return count > 0 ? total / count : 0; // Average across strategies\n      });\n\n      return {\n        label: subcategory,\n        data: data,\n        borderColor: colors[index % colors.length],\n        backgroundColor: colors[index % colors.length] + '20',\n        tension: 0.1,\n      };\n    });\n\n    return { labels, datasets };\n  };\n\n  const chartOptions = {\n    responsive: true,\n    plugins: {\n      legend: {\n        position: 'bottom',\n        labels: {\n          usePointStyle: true,\n          pointStyle: 'circle',\n          boxWidth: 8,\n          boxHeight: 8,\n        }\n      },\n      title: {\n        display: true,\n        text: `${selectedAttribute} Distribution Over Time`,\n      },\n    },\n    scales: {\n      y: {\n        beginAtZero: true,\n        ticks: {\n          callback: function(value) {\n            return value + '%';\n          }\n        },\n        title: {\n          display: true,\n          text: 'Percentage (%)'\n        }\n      },\n      x: {\n        title: {\n          display: true,\n          text: 'Month'\n        }\n      }\n    },\n  };\n\n  const availableAttributes = getAvailableAttributes();\n  const { months, years } = getAvailableMonthsYears();\n  const chartData = getChartData();\n\n  return (\n    <div>\n      \n      <div className=\"content-page\">\n        <div className='home-arrow-container'>\n          <Link to=\"/\" className=\"back-to-home-arrow\">\n            <i className='bi bi-arrow-left'></i> Home\n          </Link>\n        </div>\n        <StrategyDropdown\n          currentStrategy={currentStrategy}\n          onStrategyChange={setCurrentStrategy}\n        />\n        <p className=\"section_title\">Strategy Insights</p>\n        <hr></hr>\n        {loading ? (\n          <div className=\"text-center\">\n            <div className=\"spinner-border\" role=\"status\">\n              <span className=\"visually-hidden\">Loading...</span>\n            </div>\n          </div>\n        ) : (\n          <div className=\"strategy-content\">\n            {strategyData && (\n              <div className=\"chart-container\">\n                {/* Chart Controls */}\n                <div className=\"chart-controls\">\n                  {/* Attribute Dropdown */}\n                  <div className=\"control-group attribute-dropdown\">\n                    <label className=\"control-label\">Attribute</label>\n                    <select\n                      className=\"form-select\"\n                      value={selectedAttribute}\n                      onChange={(e) => setSelectedAttribute(e.target.value)}\n                    >\n                      <option value=\"\">Select Attribute</option>\n                      {availableAttributes.map(attr => (\n                        <option key={attr} value={attr}>{attr}</option>\n                      ))}\n                    </select>\n                  </div>\n\n                  {/* Date Controls */}\n                  <div className=\"date-controls\">\n                    {/* Start Date */}\n                    <div className=\"date-group\">\n                      <label className=\"control-label\">Start Date</label>\n                      <div className=\"start-date-container\">\n                        <select\n                          value={startMonth}\n                          onChange={(e) => setStartMonth(e.target.value)}\n                        >\n                          <option value=\"\">Month</option>\n                          {months.map(month => (\n                            <option key={month} value={month}>\n                              {new Date(2000, month - 1).toLocaleDateString('en-US', { month: 'short' })}\n                            </option>\n                          ))}\n                        </select>\n                        <select\n                          value={startYear}\n                          onChange={(e) => setStartYear(e.target.value)}\n                        >\n                          <option value=\"\">Year</option>\n                          {years.map(year => (\n                            <option key={year} value={year}>{year}</option>\n                          ))}\n                        </select>\n                        <button\n                          className=\"start-button\"\n                          onClick={setToStart}\n                        >\n                          Start\n                        </button>\n                      </div>\n                    </div>\n\n                    {/* End Date */}\n                    <div className=\"date-group\">\n                      <label className=\"control-label\">End Date</label>\n                      <div className=\"end-date-container\">\n                        <select\n                          value={endMonth}\n                          onChange={(e) => setEndMonth(e.target.value)}\n                        >\n                          <option value=\"\">Month</option>\n                          {months.map(month => (\n                            <option key={month} value={month}>\n                              {new Date(2000, month - 1).toLocaleDateString('en-US', { month: 'short' })}\n                            </option>\n                          ))}\n                        </select>\n                        <select\n                          value={endYear}\n                          onChange={(e) => setEndYear(e.target.value)}\n                        >\n                          <option value=\"\">Year</option>\n                          {years.map(year => (\n                            <option key={year} value={year}>{year}</option>\n                          ))}\n                        </select>\n                        <button\n                          className=\"now-button\"\n                          onClick={setToNow}\n                        >\n                          Now\n                        </button>\n                      </div>\n                    </div>\n                  </div>\n                </div>\n\n                {/* Chart */}\n                {selectedAttribute && chartData.datasets.length > 0 ? (\n                  <div className=\"chart-wrapper\" style={{ height: '400px' }}>\n                    <Line data={chartData} options={chartOptions} />\n                  </div>\n                ) : (\n                  <div className=\"alert alert-info\">\n                    Please select an attribute and ensure data is available for the selected date range.\n                  </div>\n                )}\n              </div>\n            )}\n          </div>\n        )}\n      </div>\n    </div>\n  );\n};\n\nexport default Strategy;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,IAAI,EAAEC,SAAS,QAAQ,kBAAkB;AAClD,OAAOC,KAAK,MAAM,OAAO;AACzB,OAAOC,gBAAgB,MAAM,oBAAoB;AACjD,SACEC,KAAK,IAAIC,OAAO,EAChBC,aAAa,EACbC,WAAW,EACXC,YAAY,EACZC,WAAW,EACXC,KAAK,EACLC,OAAO,EACPC,MAAM,QACD,UAAU;AACjB,SAASC,IAAI,QAAQ,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEvCV,OAAO,CAACW,QAAQ,CACdV,aAAa,EACbC,WAAW,EACXC,YAAY,EACZC,WAAW,EACXC,KAAK,EACLC,OAAO,EACPC,MACF,CAAC;AAED,MAAMK,QAAQ,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACrB,MAAM;IAAEC;EAAa,CAAC,GAAGlB,SAAS,CAAC,CAAC;EACpC,MAAM,CAACmB,eAAe,EAAEC,kBAAkB,CAAC,GAAGvB,QAAQ,CAACqB,YAAY,IAAI,qBAAqB,CAAC;EAC7F,MAAM,CAACG,YAAY,EAAEC,eAAe,CAAC,GAAGzB,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAAC0B,OAAO,EAAEC,UAAU,CAAC,GAAG3B,QAAQ,CAAC,IAAI,CAAC;;EAE5C;EACA,MAAM,CAAC4B,iBAAiB,EAAEC,oBAAoB,CAAC,GAAG7B,QAAQ,CAAC,EAAE,CAAC;EAC9D,MAAM,CAAC8B,UAAU,EAAEC,aAAa,CAAC,GAAG/B,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACgC,SAAS,EAAEC,YAAY,CAAC,GAAGjC,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAACkC,QAAQ,EAAEC,WAAW,CAAC,GAAGnC,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACoC,OAAO,EAAEC,UAAU,CAAC,GAAGrC,QAAQ,CAAC,EAAE,CAAC;EAE1CC,SAAS,CAAC,MAAM;IACdqC,iBAAiB,CAAChB,eAAe,CAAC;EACpC,CAAC,EAAE,CAACA,eAAe,CAAC,CAAC;EAErB,MAAMgB,iBAAiB,GAAG,MAAOC,QAAQ,IAAK;IAC5C,IAAI;MACFZ,UAAU,CAAC,IAAI,CAAC;MAChB,MAAMa,QAAQ,GAAG,MAAMpC,KAAK,CAACqC,GAAG,CAAC,iBAAiBF,QAAQ,GAAG,CAAC;MAC9Dd,eAAe,CAACe,QAAQ,CAACE,IAAI,CAAC;;MAE9B;MACA,IAAIF,QAAQ,CAACE,IAAI,IAAIC,MAAM,CAACC,IAAI,CAACJ,QAAQ,CAACE,IAAI,CAAC,CAACG,MAAM,GAAG,CAAC,EAAE;QAC1D,MAAMC,UAAU,GAAGH,MAAM,CAACC,IAAI,CAACJ,QAAQ,CAACE,IAAI,CAAC,CAAC,CAAC,CAAC;QAChD,MAAMK,aAAa,GAAGJ,MAAM,CAACC,IAAI,CAACJ,QAAQ,CAACE,IAAI,CAACI,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC;QAC/D,MAAME,UAAU,GAAGL,MAAM,CAACC,IAAI,CAACJ,QAAQ,CAACE,IAAI,CAACI,UAAU,CAAC,CAACC,aAAa,CAAC,CAAC;QAExE,IAAIC,UAAU,CAACH,MAAM,GAAG,CAAC,IAAI,CAACjB,iBAAiB,EAAE;UAC/CC,oBAAoB,CAACmB,UAAU,CAAC,CAAC,CAAC,CAAC;QACrC;;QAEA;QACA,MAAMC,MAAM,GAAGN,MAAM,CAACC,IAAI,CAACJ,QAAQ,CAACE,IAAI,CAAC,CAACQ,IAAI,CAAC,CAAC;QAChD,IAAID,MAAM,CAACJ,MAAM,GAAG,CAAC,EAAE;UACrB,MAAM,CAACM,cAAc,EAAEC,aAAa,CAAC,GAAGC,cAAc,CAACJ,MAAM,CAAC,CAAC,CAAC,CAAC;UACjE,MAAM,CAACK,YAAY,EAAEC,WAAW,CAAC,GAAGF,cAAc,CAACJ,MAAM,CAACA,MAAM,CAACJ,MAAM,GAAG,CAAC,CAAC,CAAC;UAE7E,IAAI,CAACf,UAAU,EAAE;YACfC,aAAa,CAACqB,aAAa,CAAC;YAC5BnB,YAAY,CAACkB,cAAc,CAAC;UAC9B;UACA,IAAI,CAACjB,QAAQ,EAAE;YACbC,WAAW,CAACoB,WAAW,CAAC;YACxBlB,UAAU,CAACiB,YAAY,CAAC;UAC1B;QACF;MACF;IACF,CAAC,CAAC,OAAOE,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;IACvD,CAAC,SAAS;MACR7B,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAM0B,cAAc,GAAIK,YAAY,IAAK;IACvC,MAAM,CAACC,IAAI,EAAEC,KAAK,CAAC,GAAGF,YAAY,CAACG,KAAK,CAAC,KAAK,CAAC;IAC/C,OAAO,CAACF,IAAI,EAAEC,KAAK,CAAC;EACtB,CAAC;;EAED;EACA;EACA;EACA;;EAEA;EACA,MAAME,sBAAsB,GAAGA,CAAA,KAAM;IACnC,IAAI,CAACtC,YAAY,IAAImB,MAAM,CAACC,IAAI,CAACpB,YAAY,CAAC,CAACqB,MAAM,KAAK,CAAC,EAAE,OAAO,EAAE;IAEtE,MAAMC,UAAU,GAAGH,MAAM,CAACC,IAAI,CAACpB,YAAY,CAAC,CAAC,CAAC,CAAC;IAC/C,MAAMuB,aAAa,GAAGJ,MAAM,CAACC,IAAI,CAACpB,YAAY,CAACsB,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC;IAC9D,OAAOH,MAAM,CAACC,IAAI,CAACpB,YAAY,CAACsB,UAAU,CAAC,CAACC,aAAa,CAAC,CAAC;EAC7D,CAAC;;EAED;EACA,MAAMgB,uBAAuB,GAAGA,CAAA,KAAM;IACpC,IAAI,CAACvC,YAAY,EAAE,OAAO;MAAEyB,MAAM,EAAE,EAAE;MAAEe,KAAK,EAAE;IAAG,CAAC;IAEnD,MAAMC,WAAW,GAAGtB,MAAM,CAACC,IAAI,CAACpB,YAAY,CAAC,CAAC0C,GAAG,CAACb,cAAc,CAAC;IACjE,MAAMJ,MAAM,GAAG,CAAC,GAAG,IAAIkB,GAAG,CAACF,WAAW,CAACC,GAAG,CAAC,CAAC,CAACP,IAAI,EAAEC,KAAK,CAAC,KAAKA,KAAK,CAAC,CAAC,CAAC,CAACV,IAAI,CAAC,CAAC;IAC7E,MAAMc,KAAK,GAAG,CAAC,GAAG,IAAIG,GAAG,CAACF,WAAW,CAACC,GAAG,CAAC,CAAC,CAACP,IAAI,EAAEC,KAAK,CAAC,KAAKD,IAAI,CAAC,CAAC,CAAC,CAACT,IAAI,CAAC,CAAC;IAE3E,OAAO;MAAED,MAAM;MAAEe;IAAM,CAAC;EAC1B,CAAC;;EAED;EACA,MAAMI,QAAQ,GAAGA,CAAA,KAAM;IACrB,MAAMC,GAAG,GAAG,IAAIC,IAAI,CAAC,CAAC;IACtB,MAAMC,YAAY,GAAG,CAACF,GAAG,CAACG,QAAQ,CAAC,CAAC,GAAG,CAAC,EAAEC,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;IACrE,MAAMC,WAAW,GAAGN,GAAG,CAACO,WAAW,CAAC,CAAC,CAACH,QAAQ,CAAC,CAAC;IAEhDtC,WAAW,CAACoC,YAAY,CAAC;IACzBlC,UAAU,CAACsC,WAAW,CAAC;EACzB,CAAC;;EAED;EACA,MAAME,UAAU,GAAGA,CAAA,KAAM;IACvB,IAAI,CAACrD,YAAY,IAAImB,MAAM,CAACC,IAAI,CAACpB,YAAY,CAAC,CAACqB,MAAM,KAAK,CAAC,EAAE;IAE7D,MAAMI,MAAM,GAAGN,MAAM,CAACC,IAAI,CAACpB,YAAY,CAAC,CAAC0B,IAAI,CAAC,CAAC;IAC/C,IAAID,MAAM,CAACJ,MAAM,GAAG,CAAC,EAAE;MACrB,MAAM,CAACM,cAAc,EAAEC,aAAa,CAAC,GAAGC,cAAc,CAACJ,MAAM,CAAC,CAAC,CAAC,CAAC;MACjElB,aAAa,CAACqB,aAAa,CAAC;MAC5BnB,YAAY,CAACkB,cAAc,CAAC;IAC9B;EACF,CAAC;;EAED;EACA,MAAM2B,YAAY,GAAGA,CAAA,KAAM;IACzB,IAAI,CAACtD,YAAY,IAAI,CAACI,iBAAiB,IAAI,CAACE,UAAU,IAAI,CAACE,SAAS,IAAI,CAACE,QAAQ,IAAI,CAACE,OAAO,EAAE;MAC7F,OAAO;QAAE2C,MAAM,EAAE,EAAE;QAAEC,QAAQ,EAAE;MAAG,CAAC;IACrC;;IAEA;IACA,MAAMC,YAAY,GAAG,CAAC,CAAC;IACvBtC,MAAM,CAACC,IAAI,CAACpB,YAAY,CAAC,CAAC0D,OAAO,CAACC,SAAS,IAAI;MAC7C,MAAM,CAACxB,IAAI,EAAEC,KAAK,CAAC,GAAGP,cAAc,CAAC8B,SAAS,CAAC;MAC/C,MAAMC,OAAO,GAAGC,QAAQ,CAAC1B,IAAI,CAAC;MAC9B,MAAM2B,QAAQ,GAAGD,QAAQ,CAACzB,KAAK,CAAC;MAChC,MAAM2B,YAAY,GAAGF,QAAQ,CAACrD,SAAS,CAAC;MACxC,MAAMoB,aAAa,GAAGiC,QAAQ,CAACvD,UAAU,CAAC;MAC1C,MAAM0D,UAAU,GAAGH,QAAQ,CAACjD,OAAO,CAAC;MACpC,MAAMmB,WAAW,GAAG8B,QAAQ,CAACnD,QAAQ,CAAC;MAEtC,MAAMuD,SAAS,GAAG,CAACL,OAAO,GAAGG,YAAY,IAAKH,OAAO,KAAKG,YAAY,IAAID,QAAQ,IAAIlC,aAAc,MAClFgC,OAAO,GAAGI,UAAU,IAAKJ,OAAO,KAAKI,UAAU,IAAIF,QAAQ,IAAI/B,WAAY,CAAC;MAE9F,IAAIkC,SAAS,EAAE;QACb;QACA,MAAMC,SAAS,GAAGlE,YAAY,CAAC2D,SAAS,CAAC;QACzC,IAAI7D,eAAe,KAAK,qBAAqB,EAAE;UAC7C2D,YAAY,CAACE,SAAS,CAAC,GAAGO,SAAS;QACrC,CAAC,MAAM,IAAIA,SAAS,CAACpE,eAAe,CAAC,EAAE;UACrC2D,YAAY,CAACE,SAAS,CAAC,GAAG;YAAE,CAAC7D,eAAe,GAAGoE,SAAS,CAACpE,eAAe;UAAE,CAAC;QAC7E;MACF;IACF,CAAC,CAAC;;IAEF;IACA,MAAMqE,YAAY,GAAGhD,MAAM,CAACC,IAAI,CAACqC,YAAY,CAAC,CAAC/B,IAAI,CAAC,CAAC0C,CAAC,EAAEC,CAAC,KAAK;MAC5D,MAAM,CAACC,KAAK,EAAEC,MAAM,CAAC,GAAG1C,cAAc,CAACuC,CAAC,CAAC;MACzC,MAAM,CAACI,KAAK,EAAEC,MAAM,CAAC,GAAG5C,cAAc,CAACwC,CAAC,CAAC;MACzC,OAAO,IAAIvB,IAAI,CAACwB,KAAK,EAAEC,MAAM,GAAG,CAAC,CAAC,GAAG,IAAIzB,IAAI,CAAC0B,KAAK,EAAEC,MAAM,GAAG,CAAC,CAAC;IAClE,CAAC,CAAC;;IAEF;IACA,MAAMlB,MAAM,GAAGY,YAAY,CAACzB,GAAG,CAACiB,SAAS,IAAI;MAC3C,MAAM,CAACxB,IAAI,EAAEC,KAAK,CAAC,GAAGP,cAAc,CAAC8B,SAAS,CAAC;MAC/C,MAAMe,IAAI,GAAG,IAAI5B,IAAI,CAACX,IAAI,EAAEC,KAAK,GAAG,CAAC,CAAC;MACtC,OAAOsC,IAAI,CAACC,kBAAkB,CAAC,OAAO,EAAE;QAAEvC,KAAK,EAAE,OAAO;QAAED,IAAI,EAAE;MAAU,CAAC,CAAC;IAC9E,CAAC,CAAC;;IAEF;IACA,MAAMyC,gBAAgB,GAAG,IAAIjC,GAAG,CAAC,CAAC;IAClCwB,YAAY,CAACT,OAAO,CAACC,SAAS,IAAI;MAChCxC,MAAM,CAAC0D,MAAM,CAACpB,YAAY,CAACE,SAAS,CAAC,CAAC,CAACD,OAAO,CAAC3C,QAAQ,IAAI;QACzD,IAAIA,QAAQ,CAACX,iBAAiB,CAAC,EAAE;UAC/Be,MAAM,CAACC,IAAI,CAACL,QAAQ,CAACX,iBAAiB,CAAC,CAAC,CAACsD,OAAO,CAACoB,MAAM,IAAI;YACzDF,gBAAgB,CAACG,GAAG,CAACD,MAAM,CAAC;UAC9B,CAAC,CAAC;QACJ;MACF,CAAC,CAAC;IACJ,CAAC,CAAC;;IAEF;IACA,MAAME,MAAM,GAAG,CACb,mBAAmB,EACnB,mBAAmB,EACnB,mBAAmB,EACnB,mBAAmB,EACnB,oBAAoB,EACpB,mBAAmB,EACnB,oBAAoB,EACpB,mBAAmB,CACpB;;IAED;IACA,MAAMxB,QAAQ,GAAGyB,KAAK,CAACC,IAAI,CAACN,gBAAgB,CAAC,CAAClC,GAAG,CAAC,CAACyC,WAAW,EAAEC,KAAK,KAAK;MACxE,MAAMlE,IAAI,GAAGiD,YAAY,CAACzB,GAAG,CAACiB,SAAS,IAAI;QACzC;QACA,IAAI0B,KAAK,GAAG,CAAC;QACb,IAAIC,KAAK,GAAG,CAAC;QACbnE,MAAM,CAAC0D,MAAM,CAACpB,YAAY,CAACE,SAAS,CAAC,CAAC,CAACD,OAAO,CAAC3C,QAAQ,IAAI;UACzD,IAAIA,QAAQ,CAACX,iBAAiB,CAAC,IAAIW,QAAQ,CAACX,iBAAiB,CAAC,CAAC+E,WAAW,CAAC,KAAKI,SAAS,EAAE;YACzFF,KAAK,IAAItE,QAAQ,CAACX,iBAAiB,CAAC,CAAC+E,WAAW,CAAC;YACjDG,KAAK,EAAE;UACT;QACF,CAAC,CAAC;QACF,OAAOA,KAAK,GAAG,CAAC,GAAGD,KAAK,GAAGC,KAAK,GAAG,CAAC,CAAC,CAAC;MACxC,CAAC,CAAC;MAEF,OAAO;QACLE,KAAK,EAAEL,WAAW;QAClBjE,IAAI,EAAEA,IAAI;QACVuE,WAAW,EAAET,MAAM,CAACI,KAAK,GAAGJ,MAAM,CAAC3D,MAAM,CAAC;QAC1CqE,eAAe,EAAEV,MAAM,CAACI,KAAK,GAAGJ,MAAM,CAAC3D,MAAM,CAAC,GAAG,IAAI;QACrDsE,OAAO,EAAE;MACX,CAAC;IACH,CAAC,CAAC;IAEF,OAAO;MAAEpC,MAAM;MAAEC;IAAS,CAAC;EAC7B,CAAC;EAED,MAAMoC,YAAY,GAAG;IACnBC,UAAU,EAAE,IAAI;IAChBC,OAAO,EAAE;MACPC,MAAM,EAAE;QACNC,QAAQ,EAAE,QAAQ;QAClBzC,MAAM,EAAE;UACN0C,aAAa,EAAE,IAAI;UACnBC,UAAU,EAAE,QAAQ;UACpBC,QAAQ,EAAE,CAAC;UACXC,SAAS,EAAE;QACb;MACF,CAAC;MACDC,KAAK,EAAE;QACLC,OAAO,EAAE,IAAI;QACbC,IAAI,EAAE,GAAGnG,iBAAiB;MAC5B;IACF,CAAC;IACDoG,MAAM,EAAE;MACNC,CAAC,EAAE;QACDC,WAAW,EAAE,IAAI;QACjBC,KAAK,EAAE;UACLC,QAAQ,EAAE,SAAAA,CAASC,KAAK,EAAE;YACxB,OAAOA,KAAK,GAAG,GAAG;UACpB;QACF,CAAC;QACDR,KAAK,EAAE;UACLC,OAAO,EAAE,IAAI;UACbC,IAAI,EAAE;QACR;MACF,CAAC;MACDO,CAAC,EAAE;QACDT,KAAK,EAAE;UACLC,OAAO,EAAE,IAAI;UACbC,IAAI,EAAE;QACR;MACF;IACF;EACF,CAAC;EAED,MAAMQ,mBAAmB,GAAGzE,sBAAsB,CAAC,CAAC;EACpD,MAAM;IAAEb,MAAM;IAAEe;EAAM,CAAC,GAAGD,uBAAuB,CAAC,CAAC;EACnD,MAAMyE,SAAS,GAAG1D,YAAY,CAAC,CAAC;EAEhC,oBACE7D,OAAA;IAAAwH,QAAA,eAEExH,OAAA;MAAKyH,SAAS,EAAC,cAAc;MAAAD,QAAA,gBAC3BxH,OAAA;QAAKyH,SAAS,EAAC,sBAAsB;QAAAD,QAAA,eACnCxH,OAAA,CAACf,IAAI;UAACyI,EAAE,EAAC,GAAG;UAACD,SAAS,EAAC,oBAAoB;UAAAD,QAAA,gBACzCxH,OAAA;YAAGyH,SAAS,EAAC;UAAkB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,SACtC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACN9H,OAAA,CAACZ,gBAAgB;QACfiB,eAAe,EAAEA,eAAgB;QACjC0H,gBAAgB,EAAEzH;MAAmB;QAAAqH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACtC,CAAC,eACF9H,OAAA;QAAGyH,SAAS,EAAC,eAAe;QAAAD,QAAA,EAAC;MAAiB;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC,eAClD9H,OAAA;QAAA2H,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,EACRrH,OAAO,gBACNT,OAAA;QAAKyH,SAAS,EAAC,aAAa;QAAAD,QAAA,eAC1BxH,OAAA;UAAKyH,SAAS,EAAC,gBAAgB;UAACO,IAAI,EAAC,QAAQ;UAAAR,QAAA,eAC3CxH,OAAA;YAAMyH,SAAS,EAAC,iBAAiB;YAAAD,QAAA,EAAC;UAAU;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChD;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,gBAEN9H,OAAA;QAAKyH,SAAS,EAAC,kBAAkB;QAAAD,QAAA,EAC9BjH,YAAY,iBACXP,OAAA;UAAKyH,SAAS,EAAC,iBAAiB;UAAAD,QAAA,gBAE9BxH,OAAA;YAAKyH,SAAS,EAAC,gBAAgB;YAAAD,QAAA,gBAE7BxH,OAAA;cAAKyH,SAAS,EAAC,kCAAkC;cAAAD,QAAA,gBAC/CxH,OAAA;gBAAOyH,SAAS,EAAC,eAAe;gBAAAD,QAAA,EAAC;cAAS;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAClD9H,OAAA;gBACEyH,SAAS,EAAC,aAAa;gBACvBL,KAAK,EAAEzG,iBAAkB;gBACzBsH,QAAQ,EAAGC,CAAC,IAAKtH,oBAAoB,CAACsH,CAAC,CAACC,MAAM,CAACf,KAAK,CAAE;gBAAAI,QAAA,gBAEtDxH,OAAA;kBAAQoH,KAAK,EAAC,EAAE;kBAAAI,QAAA,EAAC;gBAAgB;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,EACzCR,mBAAmB,CAACrE,GAAG,CAACmF,IAAI,iBAC3BpI,OAAA;kBAAmBoH,KAAK,EAAEgB,IAAK;kBAAAZ,QAAA,EAAEY;gBAAI,GAAxBA,IAAI;kBAAAT,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAA6B,CAC/C,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,eAGN9H,OAAA;cAAKyH,SAAS,EAAC,eAAe;cAAAD,QAAA,gBAE5BxH,OAAA;gBAAKyH,SAAS,EAAC,YAAY;gBAAAD,QAAA,gBACzBxH,OAAA;kBAAOyH,SAAS,EAAC,eAAe;kBAAAD,QAAA,EAAC;gBAAU;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACnD9H,OAAA;kBAAKyH,SAAS,EAAC,sBAAsB;kBAAAD,QAAA,gBACnCxH,OAAA;oBACEoH,KAAK,EAAEvG,UAAW;oBAClBoH,QAAQ,EAAGC,CAAC,IAAKpH,aAAa,CAACoH,CAAC,CAACC,MAAM,CAACf,KAAK,CAAE;oBAAAI,QAAA,gBAE/CxH,OAAA;sBAAQoH,KAAK,EAAC,EAAE;sBAAAI,QAAA,EAAC;oBAAK;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,EAC9B9F,MAAM,CAACiB,GAAG,CAACN,KAAK,iBACf3C,OAAA;sBAAoBoH,KAAK,EAAEzE,KAAM;sBAAA6E,QAAA,EAC9B,IAAInE,IAAI,CAAC,IAAI,EAAEV,KAAK,GAAG,CAAC,CAAC,CAACuC,kBAAkB,CAAC,OAAO,EAAE;wBAAEvC,KAAK,EAAE;sBAAQ,CAAC;oBAAC,GAD/DA,KAAK;sBAAAgF,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAEV,CACT,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACI,CAAC,eACT9H,OAAA;oBACEoH,KAAK,EAAErG,SAAU;oBACjBkH,QAAQ,EAAGC,CAAC,IAAKlH,YAAY,CAACkH,CAAC,CAACC,MAAM,CAACf,KAAK,CAAE;oBAAAI,QAAA,gBAE9CxH,OAAA;sBAAQoH,KAAK,EAAC,EAAE;sBAAAI,QAAA,EAAC;oBAAI;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,EAC7B/E,KAAK,CAACE,GAAG,CAACP,IAAI,iBACb1C,OAAA;sBAAmBoH,KAAK,EAAE1E,IAAK;sBAAA8E,QAAA,EAAE9E;oBAAI,GAAxBA,IAAI;sBAAAiF,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAA6B,CAC/C,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACI,CAAC,eACT9H,OAAA;oBACEyH,SAAS,EAAC,cAAc;oBACxBY,OAAO,EAAEzE,UAAW;oBAAA4D,QAAA,EACrB;kBAED;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAGN9H,OAAA;gBAAKyH,SAAS,EAAC,YAAY;gBAAAD,QAAA,gBACzBxH,OAAA;kBAAOyH,SAAS,EAAC,eAAe;kBAAAD,QAAA,EAAC;gBAAQ;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACjD9H,OAAA;kBAAKyH,SAAS,EAAC,oBAAoB;kBAAAD,QAAA,gBACjCxH,OAAA;oBACEoH,KAAK,EAAEnG,QAAS;oBAChBgH,QAAQ,EAAGC,CAAC,IAAKhH,WAAW,CAACgH,CAAC,CAACC,MAAM,CAACf,KAAK,CAAE;oBAAAI,QAAA,gBAE7CxH,OAAA;sBAAQoH,KAAK,EAAC,EAAE;sBAAAI,QAAA,EAAC;oBAAK;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,EAC9B9F,MAAM,CAACiB,GAAG,CAACN,KAAK,iBACf3C,OAAA;sBAAoBoH,KAAK,EAAEzE,KAAM;sBAAA6E,QAAA,EAC9B,IAAInE,IAAI,CAAC,IAAI,EAAEV,KAAK,GAAG,CAAC,CAAC,CAACuC,kBAAkB,CAAC,OAAO,EAAE;wBAAEvC,KAAK,EAAE;sBAAQ,CAAC;oBAAC,GAD/DA,KAAK;sBAAAgF,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAEV,CACT,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACI,CAAC,eACT9H,OAAA;oBACEoH,KAAK,EAAEjG,OAAQ;oBACf8G,QAAQ,EAAGC,CAAC,IAAK9G,UAAU,CAAC8G,CAAC,CAACC,MAAM,CAACf,KAAK,CAAE;oBAAAI,QAAA,gBAE5CxH,OAAA;sBAAQoH,KAAK,EAAC,EAAE;sBAAAI,QAAA,EAAC;oBAAI;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,EAC7B/E,KAAK,CAACE,GAAG,CAACP,IAAI,iBACb1C,OAAA;sBAAmBoH,KAAK,EAAE1E,IAAK;sBAAA8E,QAAA,EAAE9E;oBAAI,GAAxBA,IAAI;sBAAAiF,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAA6B,CAC/C,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACI,CAAC,eACT9H,OAAA;oBACEyH,SAAS,EAAC,YAAY;oBACtBY,OAAO,EAAElF,QAAS;oBAAAqE,QAAA,EACnB;kBAED;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,EAGLnH,iBAAiB,IAAI4G,SAAS,CAACxD,QAAQ,CAACnC,MAAM,GAAG,CAAC,gBACjD5B,OAAA;YAAKyH,SAAS,EAAC,eAAe;YAACa,KAAK,EAAE;cAAEC,MAAM,EAAE;YAAQ,CAAE;YAAAf,QAAA,eACxDxH,OAAA,CAACF,IAAI;cAAC2B,IAAI,EAAE8F,SAAU;cAACiB,OAAO,EAAErC;YAAa;cAAAwB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7C,CAAC,gBAEN9H,OAAA;YAAKyH,SAAS,EAAC,kBAAkB;YAAAD,QAAA,EAAC;UAElC;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MACN;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAC3H,EAAA,CAzXID,QAAQ;EAAA,QACahB,SAAS;AAAA;AAAAuJ,EAAA,GAD9BvI,QAAQ;AA2Xd,eAAeA,QAAQ;AAAC,IAAAuI,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}