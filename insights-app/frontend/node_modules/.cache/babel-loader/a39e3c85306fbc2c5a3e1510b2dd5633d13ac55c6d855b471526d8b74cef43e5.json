{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Desktop/Coding/BVS/CrystalBall/insights-app/frontend/src/components/Strategy.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { HashRouter, useParams } from 'react-router-dom';\nimport axios from 'axios';\nimport StrategyDropdown from './StrategyDropdown';\nimport { Chart as ChartJS, CategoryScale, LinearScale, PointElement, LineElement, Title, Tooltip, Legend } from 'chart.js';\nimport { Line } from 'react-chartjs-2';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nChartJS.register(CategoryScale, LinearScale, PointElement, LineElement, Title, Tooltip, Legend);\nconst Strategy = () => {\n  _s();\n  const {\n    strategyName\n  } = useParams();\n  const [currentStrategy, setCurrentStrategy] = useState(strategyName || 'Alle');\n  const [strategyData, setStrategyData] = useState(null);\n  const [loading, setLoading] = useState(true);\n\n  // Chart state\n  const [selectedAttribute, setSelectedAttribute] = useState('');\n  const [startMonth, setStartMonth] = useState('');\n  const [startYear, setStartYear] = useState('');\n  const [endMonth, setEndMonth] = useState('');\n  const [endYear, setEndYear] = useState('');\n  useEffect(() => {\n    fetchStrategyData(currentStrategy);\n  }, [currentStrategy]);\n  const fetchStrategyData = async strategy => {\n    try {\n      setLoading(true);\n      const response = await axios.get(`/api/strategy/${strategy}/`);\n      setStrategyData(response.data);\n\n      // Initialize dropdowns with first available data\n      if (response.data && Object.keys(response.data).length > 0) {\n        const firstMonth = Object.keys(response.data)[0];\n        const firstStrategy = Object.keys(response.data[firstMonth])[0];\n        const attributes = Object.keys(response.data[firstMonth][firstStrategy]);\n        if (attributes.length > 0 && !selectedAttribute) {\n          setSelectedAttribute(attributes[0]);\n        }\n\n        // Set default date range\n        const months = Object.keys(response.data).sort();\n        if (months.length > 0) {\n          const [startYearMonth, startMonthNum] = parseMonthYear(months[0]);\n          const [endYearMonth, endMonthNum] = parseMonthYear(months[months.length - 1]);\n          if (!startMonth) {\n            setStartMonth(startMonthNum);\n            setStartYear(startYearMonth);\n          }\n          if (!endMonth) {\n            setEndMonth(endMonthNum);\n            setEndYear(endYearMonth);\n          }\n        }\n      }\n    } catch (error) {\n      console.error('Error fetching strategy data:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Helper function to parse month-year string like \"2025 - 04\"\n  const parseMonthYear = monthYearStr => {\n    const [year, month] = monthYearStr.split(' - ');\n    return [year, month];\n  };\n\n  // Helper function to create month-year string\n  const createMonthYearStr = (year, month) => {\n    return `${year} - ${month.padStart(2, '0')}`;\n  };\n\n  // Get available attributes from data\n  const getAvailableAttributes = () => {\n    if (!strategyData || Object.keys(strategyData).length === 0) return [];\n    const firstMonth = Object.keys(strategyData)[0];\n    const firstStrategy = Object.keys(strategyData[firstMonth])[0];\n    return Object.keys(strategyData[firstMonth][firstStrategy]);\n  };\n\n  // Get available months and years\n  const getAvailableMonthsYears = () => {\n    if (!strategyData) return {\n      months: [],\n      years: []\n    };\n    const monthsYears = Object.keys(strategyData).map(parseMonthYear);\n    const months = [...new Set(monthsYears.map(([year, month]) => month))].sort();\n    const years = [...new Set(monthsYears.map(([year, month]) => year))].sort();\n    return {\n      months,\n      years\n    };\n  };\n\n  // Set end date to current month/year\n  const setToNow = () => {\n    const now = new Date();\n    const currentMonth = (now.getMonth() + 1).toString().padStart(2, '0');\n    const currentYear = now.getFullYear().toString();\n    setEndMonth(currentMonth);\n    setEndYear(currentYear);\n  };\n\n  // Process data for chart\n  const getChartData = () => {\n    if (!strategyData || !selectedAttribute || !startMonth || !startYear || !endMonth || !endYear) {\n      return {\n        labels: [],\n        datasets: []\n      };\n    }\n\n    // Filter data by date range\n    const filteredData = {};\n    Object.keys(strategyData).forEach(monthYear => {\n      const [year, month] = parseMonthYear(monthYear);\n      const yearNum = parseInt(year);\n      const monthNum = parseInt(month);\n      const startYearNum = parseInt(startYear);\n      const startMonthNum = parseInt(startMonth);\n      const endYearNum = parseInt(endYear);\n      const endMonthNum = parseInt(endMonth);\n      const isInRange = (yearNum > startYearNum || yearNum === startYearNum && monthNum >= startMonthNum) && (yearNum < endYearNum || yearNum === endYearNum && monthNum <= endMonthNum);\n      if (isInRange) {\n        filteredData[monthYear] = strategyData[monthYear];\n      }\n    });\n\n    // Sort months chronologically\n    const sortedMonths = Object.keys(filteredData).sort((a, b) => {\n      const [yearA, monthA] = parseMonthYear(a);\n      const [yearB, monthB] = parseMonthYear(b);\n      return new Date(yearA, monthA - 1) - new Date(yearB, monthB - 1);\n    });\n\n    // Create labels (formatted month names)\n    const labels = sortedMonths.map(monthYear => {\n      const [year, month] = parseMonthYear(monthYear);\n      const date = new Date(year, month - 1);\n      return date.toLocaleDateString('en-US', {\n        month: 'short',\n        year: 'numeric'\n      });\n    });\n\n    // Get all unique currencies/subcategories for the selected attribute\n    const allSubcategories = new Set();\n    sortedMonths.forEach(monthYear => {\n      Object.values(filteredData[monthYear]).forEach(strategy => {\n        if (strategy[selectedAttribute]) {\n          Object.keys(strategy[selectedAttribute]).forEach(subcat => {\n            allSubcategories.add(subcat);\n          });\n        }\n      });\n    });\n\n    // Generate colors for each line\n    const colors = ['rgb(255, 99, 132)', 'rgb(54, 162, 235)', 'rgb(255, 205, 86)', 'rgb(75, 192, 192)', 'rgb(153, 102, 255)', 'rgb(255, 159, 64)', 'rgb(199, 199, 199)', 'rgb(83, 102, 147)'];\n\n    // Create datasets for each subcategory\n    const datasets = Array.from(allSubcategories).map((subcategory, index) => {\n      const data = sortedMonths.map(monthYear => {\n        // Aggregate data across all strategies for this month and subcategory\n        let total = 0;\n        let count = 0;\n        Object.values(filteredData[monthYear]).forEach(strategy => {\n          if (strategy[selectedAttribute] && strategy[selectedAttribute][subcategory] !== undefined) {\n            total += strategy[selectedAttribute][subcategory];\n            count++;\n          }\n        });\n        return count > 0 ? total / count : 0; // Average across strategies\n      });\n      return {\n        label: subcategory,\n        data: data,\n        borderColor: colors[index % colors.length],\n        backgroundColor: colors[index % colors.length] + '20',\n        tension: 0.1\n      };\n    });\n    return {\n      labels,\n      datasets\n    };\n  };\n  const chartOptions = {\n    responsive: true,\n    plugins: {\n      legend: {\n        position: 'bottom'\n      },\n      title: {\n        display: true,\n        text: `${selectedAttribute} Distribution Over Time`\n      }\n    },\n    scales: {\n      y: {\n        beginAtZero: true,\n        ticks: {\n          callback: function (value) {\n            return value + '%';\n          }\n        },\n        title: {\n          display: true,\n          text: 'Percentage (%)'\n        }\n      },\n      x: {\n        title: {\n          display: true,\n          text: 'Month'\n        }\n      }\n    }\n  };\n  const availableAttributes = getAvailableAttributes();\n  const {\n    months,\n    years\n  } = getAvailableMonthsYears();\n  const chartData = getChartData();\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"content-page\",\n    children: [/*#__PURE__*/_jsxDEV(StrategyDropdown, {\n      currentStrategy: currentStrategy,\n      onStrategyChange: setCurrentStrategy\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 253,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n      className: \"section_title\",\n      children: \"Strategy Insights\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 257,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"hr\", {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 258,\n      columnNumber: 7\n    }, this), loading ? /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"text-center\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"spinner-border\",\n        role: \"status\",\n        children: /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"visually-hidden\",\n          children: \"Loading...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 262,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 261,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 260,\n      columnNumber: 9\n    }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"strategy-content\",\n      children: strategyData && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"chart-container\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"chart-controls\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"control-group attribute-dropdown\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"control-label\",\n              children: \"Attribute\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 273,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n              className: \"form-select\",\n              value: selectedAttribute,\n              onChange: e => setSelectedAttribute(e.target.value),\n              children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"\",\n                children: \"Select Attribute\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 279,\n                columnNumber: 21\n              }, this), availableAttributes.map(attr => /*#__PURE__*/_jsxDEV(\"option\", {\n                value: attr,\n                children: attr\n              }, attr, false, {\n                fileName: _jsxFileName,\n                lineNumber: 281,\n                columnNumber: 23\n              }, this))]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 274,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 272,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"date-controls\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"date-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"control-label\",\n                children: \"Start Date\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 290,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"date-container\",\n                children: [/*#__PURE__*/_jsxDEV(\"select\", {\n                  value: startMonth,\n                  onChange: e => setStartMonth(e.target.value),\n                  children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"\",\n                    children: \"Month\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 296,\n                    columnNumber: 25\n                  }, this), months.map(month => /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: month,\n                    children: new Date(2000, month - 1).toLocaleDateString('en-US', {\n                      month: 'short'\n                    })\n                  }, month, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 298,\n                    columnNumber: 27\n                  }, this))]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 292,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                  value: startYear,\n                  onChange: e => setStartYear(e.target.value),\n                  children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"\",\n                    children: \"Year\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 307,\n                    columnNumber: 25\n                  }, this), years.map(year => /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: year,\n                    children: year\n                  }, year, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 309,\n                    columnNumber: 27\n                  }, this))]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 303,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 291,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 289,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"date-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"control-label\",\n                children: \"End Date\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 317,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"end-date-container\",\n                children: [/*#__PURE__*/_jsxDEV(\"select\", {\n                  value: endMonth,\n                  onChange: e => setEndMonth(e.target.value),\n                  children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"\",\n                    children: \"Month\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 323,\n                    columnNumber: 25\n                  }, this), months.map(month => /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: month,\n                    children: new Date(2000, month - 1).toLocaleDateString('en-US', {\n                      month: 'short'\n                    })\n                  }, month, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 325,\n                    columnNumber: 27\n                  }, this))]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 319,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                  value: endYear,\n                  onChange: e => setEndYear(e.target.value),\n                  children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"\",\n                    children: \"Year\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 334,\n                    columnNumber: 25\n                  }, this), years.map(year => /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: year,\n                    children: year\n                  }, year, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 336,\n                    columnNumber: 27\n                  }, this))]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 330,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  className: \"now-button\",\n                  onClick: setToNow,\n                  children: \"Now\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 339,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 318,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 316,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 287,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 270,\n          columnNumber: 15\n        }, this), selectedAttribute && chartData.datasets.length > 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"chart-wrapper\",\n          style: {\n            height: '400px'\n          },\n          children: /*#__PURE__*/_jsxDEV(Line, {\n            data: chartData,\n            options: chartOptions\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 353,\n            columnNumber: 19\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 352,\n          columnNumber: 17\n        }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"alert alert-info\",\n          children: \"Please select an attribute and ensure data is available for the selected date range.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 356,\n          columnNumber: 17\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 268,\n        columnNumber: 13\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 266,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 252,\n    columnNumber: 5\n  }, this);\n};\n_s(Strategy, \"FXPu1PVWL27qn/kjax75gpk9gsM=\", false, function () {\n  return [useParams];\n});\n_c = Strategy;\nexport default Strategy;\nvar _c;\n$RefreshReg$(_c, \"Strategy\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "useParams", "axios", "StrategyDropdown", "Chart", "ChartJS", "CategoryScale", "LinearScale", "PointElement", "LineElement", "Title", "<PERSON><PERSON><PERSON>", "Legend", "Line", "jsxDEV", "_jsxDEV", "register", "Strategy", "_s", "strategyName", "currentStrategy", "setCurrentStrategy", "strategyData", "setStrategyData", "loading", "setLoading", "selectedAttribute", "setSelectedAttribute", "startMonth", "setStartMonth", "startYear", "setStartYear", "endMonth", "setEndMonth", "endYear", "setEndYear", "fetchStrategyData", "strategy", "response", "get", "data", "Object", "keys", "length", "firstMonth", "firstStrategy", "attributes", "months", "sort", "startYearMonth", "startMonthNum", "parseMonthYear", "endYearMonth", "endMonthNum", "error", "console", "monthYearStr", "year", "month", "split", "createMonthYearStr", "padStart", "getAvailableAttributes", "getAvailableMonthsYears", "years", "monthsYears", "map", "Set", "setToNow", "now", "Date", "currentMonth", "getMonth", "toString", "currentYear", "getFullYear", "getChartData", "labels", "datasets", "filteredData", "for<PERSON>ach", "monthYear", "yearNum", "parseInt", "monthNum", "startYearNum", "endYearNum", "isInRange", "sortedMonths", "a", "b", "yearA", "monthA", "yearB", "monthB", "date", "toLocaleDateString", "allSubcategories", "values", "subcat", "add", "colors", "Array", "from", "subcategory", "index", "total", "count", "undefined", "label", "borderColor", "backgroundColor", "tension", "chartOptions", "responsive", "plugins", "legend", "position", "title", "display", "text", "scales", "y", "beginAtZero", "ticks", "callback", "value", "x", "availableAttributes", "chartData", "className", "children", "onStrategyChange", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "role", "onChange", "e", "target", "attr", "onClick", "style", "height", "options", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Desktop/Coding/BVS/CrystalBall/insights-app/frontend/src/components/Strategy.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { <PERSON>hRouter, useParams } from 'react-router-dom';\nimport axios from 'axios';\nimport StrategyDropdown from './StrategyDropdown';\nimport {\n  Chart as ChartJS,\n  CategoryScale,\n  LinearScale,\n  PointElement,\n  LineElement,\n  Title,\n  Tooltip,\n  Legend,\n} from 'chart.js';\nimport { Line } from 'react-chartjs-2';\n\nChartJS.register(\n  CategoryScale,\n  LinearScale,\n  PointElement,\n  LineElement,\n  Title,\n  Tooltip,\n  Legend\n);\n\nconst Strategy = () => {\n  const { strategyName } = useParams();\n  const [currentStrategy, setCurrentStrategy] = useState(strategyName || 'Alle');\n  const [strategyData, setStrategyData] = useState(null);\n  const [loading, setLoading] = useState(true);\n\n  // Chart state\n  const [selectedAttribute, setSelectedAttribute] = useState('');\n  const [startMonth, setStartMonth] = useState('');\n  const [startYear, setStartYear] = useState('');\n  const [endMonth, setEndMonth] = useState('');\n  const [endYear, setEndYear] = useState('');\n\n  useEffect(() => {\n    fetchStrategyData(currentStrategy);\n  }, [currentStrategy]);\n\n  const fetchStrategyData = async (strategy) => {\n    try {\n      setLoading(true);\n      const response = await axios.get(`/api/strategy/${strategy}/`);\n      setStrategyData(response.data);\n\n      // Initialize dropdowns with first available data\n      if (response.data && Object.keys(response.data).length > 0) {\n        const firstMonth = Object.keys(response.data)[0];\n        const firstStrategy = Object.keys(response.data[firstMonth])[0];\n        const attributes = Object.keys(response.data[firstMonth][firstStrategy]);\n\n        if (attributes.length > 0 && !selectedAttribute) {\n          setSelectedAttribute(attributes[0]);\n        }\n\n        // Set default date range\n        const months = Object.keys(response.data).sort();\n        if (months.length > 0) {\n          const [startYearMonth, startMonthNum] = parseMonthYear(months[0]);\n          const [endYearMonth, endMonthNum] = parseMonthYear(months[months.length - 1]);\n\n          if (!startMonth) {\n            setStartMonth(startMonthNum);\n            setStartYear(startYearMonth);\n          }\n          if (!endMonth) {\n            setEndMonth(endMonthNum);\n            setEndYear(endYearMonth);\n          }\n        }\n      }\n    } catch (error) {\n      console.error('Error fetching strategy data:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Helper function to parse month-year string like \"2025 - 04\"\n  const parseMonthYear = (monthYearStr) => {\n    const [year, month] = monthYearStr.split(' - ');\n    return [year, month];\n  };\n\n  // Helper function to create month-year string\n  const createMonthYearStr = (year, month) => {\n    return `${year} - ${month.padStart(2, '0')}`;\n  };\n\n  // Get available attributes from data\n  const getAvailableAttributes = () => {\n    if (!strategyData || Object.keys(strategyData).length === 0) return [];\n\n    const firstMonth = Object.keys(strategyData)[0];\n    const firstStrategy = Object.keys(strategyData[firstMonth])[0];\n    return Object.keys(strategyData[firstMonth][firstStrategy]);\n  };\n\n  // Get available months and years\n  const getAvailableMonthsYears = () => {\n    if (!strategyData) return { months: [], years: [] };\n\n    const monthsYears = Object.keys(strategyData).map(parseMonthYear);\n    const months = [...new Set(monthsYears.map(([year, month]) => month))].sort();\n    const years = [...new Set(monthsYears.map(([year, month]) => year))].sort();\n\n    return { months, years };\n  };\n\n  // Set end date to current month/year\n  const setToNow = () => {\n    const now = new Date();\n    const currentMonth = (now.getMonth() + 1).toString().padStart(2, '0');\n    const currentYear = now.getFullYear().toString();\n\n    setEndMonth(currentMonth);\n    setEndYear(currentYear);\n  };\n\n  // Process data for chart\n  const getChartData = () => {\n    if (!strategyData || !selectedAttribute || !startMonth || !startYear || !endMonth || !endYear) {\n      return { labels: [], datasets: [] };\n    }\n\n    // Filter data by date range\n    const filteredData = {};\n    Object.keys(strategyData).forEach(monthYear => {\n      const [year, month] = parseMonthYear(monthYear);\n      const yearNum = parseInt(year);\n      const monthNum = parseInt(month);\n      const startYearNum = parseInt(startYear);\n      const startMonthNum = parseInt(startMonth);\n      const endYearNum = parseInt(endYear);\n      const endMonthNum = parseInt(endMonth);\n\n      const isInRange = (yearNum > startYearNum || (yearNum === startYearNum && monthNum >= startMonthNum)) &&\n                       (yearNum < endYearNum || (yearNum === endYearNum && monthNum <= endMonthNum));\n\n      if (isInRange) {\n        filteredData[monthYear] = strategyData[monthYear];\n      }\n    });\n\n    // Sort months chronologically\n    const sortedMonths = Object.keys(filteredData).sort((a, b) => {\n      const [yearA, monthA] = parseMonthYear(a);\n      const [yearB, monthB] = parseMonthYear(b);\n      return new Date(yearA, monthA - 1) - new Date(yearB, monthB - 1);\n    });\n\n    // Create labels (formatted month names)\n    const labels = sortedMonths.map(monthYear => {\n      const [year, month] = parseMonthYear(monthYear);\n      const date = new Date(year, month - 1);\n      return date.toLocaleDateString('en-US', { month: 'short', year: 'numeric' });\n    });\n\n    // Get all unique currencies/subcategories for the selected attribute\n    const allSubcategories = new Set();\n    sortedMonths.forEach(monthYear => {\n      Object.values(filteredData[monthYear]).forEach(strategy => {\n        if (strategy[selectedAttribute]) {\n          Object.keys(strategy[selectedAttribute]).forEach(subcat => {\n            allSubcategories.add(subcat);\n          });\n        }\n      });\n    });\n\n    // Generate colors for each line\n    const colors = [\n      'rgb(255, 99, 132)',\n      'rgb(54, 162, 235)',\n      'rgb(255, 205, 86)',\n      'rgb(75, 192, 192)',\n      'rgb(153, 102, 255)',\n      'rgb(255, 159, 64)',\n      'rgb(199, 199, 199)',\n      'rgb(83, 102, 147)',\n    ];\n\n    // Create datasets for each subcategory\n    const datasets = Array.from(allSubcategories).map((subcategory, index) => {\n      const data = sortedMonths.map(monthYear => {\n        // Aggregate data across all strategies for this month and subcategory\n        let total = 0;\n        let count = 0;\n        Object.values(filteredData[monthYear]).forEach(strategy => {\n          if (strategy[selectedAttribute] && strategy[selectedAttribute][subcategory] !== undefined) {\n            total += strategy[selectedAttribute][subcategory];\n            count++;\n          }\n        });\n        return count > 0 ? total / count : 0; // Average across strategies\n      });\n\n      return {\n        label: subcategory,\n        data: data,\n        borderColor: colors[index % colors.length],\n        backgroundColor: colors[index % colors.length] + '20',\n        tension: 0.1,\n      };\n    });\n\n    return { labels, datasets };\n  };\n\n  const chartOptions = {\n    responsive: true,\n    plugins: {\n      legend: {\n        position: 'bottom',\n      },\n      title: {\n        display: true,\n        text: `${selectedAttribute} Distribution Over Time`,\n      },\n    },\n    scales: {\n      y: {\n        beginAtZero: true,\n        ticks: {\n          callback: function(value) {\n            return value + '%';\n          }\n        },\n        title: {\n          display: true,\n          text: 'Percentage (%)'\n        }\n      },\n      x: {\n        title: {\n          display: true,\n          text: 'Month'\n        }\n      }\n    },\n  };\n\n  const availableAttributes = getAvailableAttributes();\n  const { months, years } = getAvailableMonthsYears();\n  const chartData = getChartData();\n\n  return (\n    <div className=\"content-page\">\n      <StrategyDropdown\n        currentStrategy={currentStrategy}\n        onStrategyChange={setCurrentStrategy}\n      />\n      <p className=\"section_title\">Strategy Insights</p>\n      <hr></hr>\n      {loading ? (\n        <div className=\"text-center\">\n          <div className=\"spinner-border\" role=\"status\">\n            <span className=\"visually-hidden\">Loading...</span>\n          </div>\n        </div>\n      ) : (\n        <div className=\"strategy-content\">\n          {strategyData && (\n            <div className=\"chart-container\">\n              {/* Chart Controls */}\n              <div className=\"chart-controls\">\n                {/* Attribute Dropdown */}\n                <div className=\"control-group attribute-dropdown\">\n                  <label className=\"control-label\">Attribute</label>\n                  <select\n                    className=\"form-select\"\n                    value={selectedAttribute}\n                    onChange={(e) => setSelectedAttribute(e.target.value)}\n                  >\n                    <option value=\"\">Select Attribute</option>\n                    {availableAttributes.map(attr => (\n                      <option key={attr} value={attr}>{attr}</option>\n                    ))}\n                  </select>\n                </div>\n\n                {/* Date Controls */}\n                <div className=\"date-controls\">\n                  {/* Start Date */}\n                  <div className=\"date-group\">\n                    <label className=\"control-label\">Start Date</label>\n                    <div className=\"date-container\">\n                      <select\n                        value={startMonth}\n                        onChange={(e) => setStartMonth(e.target.value)}\n                      >\n                        <option value=\"\">Month</option>\n                        {months.map(month => (\n                          <option key={month} value={month}>\n                            {new Date(2000, month - 1).toLocaleDateString('en-US', { month: 'short' })}\n                          </option>\n                        ))}\n                      </select>\n                      <select\n                        value={startYear}\n                        onChange={(e) => setStartYear(e.target.value)}\n                      >\n                        <option value=\"\">Year</option>\n                        {years.map(year => (\n                          <option key={year} value={year}>{year}</option>\n                        ))}\n                      </select>\n                    </div>\n                  </div>\n\n                  {/* End Date */}\n                  <div className=\"date-group\">\n                    <label className=\"control-label\">End Date</label>\n                    <div className=\"end-date-container\">\n                      <select\n                        value={endMonth}\n                        onChange={(e) => setEndMonth(e.target.value)}\n                      >\n                        <option value=\"\">Month</option>\n                        {months.map(month => (\n                          <option key={month} value={month}>\n                            {new Date(2000, month - 1).toLocaleDateString('en-US', { month: 'short' })}\n                          </option>\n                        ))}\n                      </select>\n                      <select\n                        value={endYear}\n                        onChange={(e) => setEndYear(e.target.value)}\n                      >\n                        <option value=\"\">Year</option>\n                        {years.map(year => (\n                          <option key={year} value={year}>{year}</option>\n                        ))}\n                      </select>\n                      <button\n                        className=\"now-button\"\n                        onClick={setToNow}\n                      >\n                        Now\n                      </button>\n                    </div>\n                  </div>\n                </div>\n              </div>\n\n              {/* Chart */}\n              {selectedAttribute && chartData.datasets.length > 0 ? (\n                <div className=\"chart-wrapper\" style={{ height: '400px' }}>\n                  <Line data={chartData} options={chartOptions} />\n                </div>\n              ) : (\n                <div className=\"alert alert-info\">\n                  Please select an attribute and ensure data is available for the selected date range.\n                </div>\n              )}\n            </div>\n          )}\n        </div>\n      )}\n    </div>\n  );\n};\n\nexport default Strategy;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,UAAU,EAAEC,SAAS,QAAQ,kBAAkB;AACxD,OAAOC,KAAK,MAAM,OAAO;AACzB,OAAOC,gBAAgB,MAAM,oBAAoB;AACjD,SACEC,KAAK,IAAIC,OAAO,EAChBC,aAAa,EACbC,WAAW,EACXC,YAAY,EACZC,WAAW,EACXC,KAAK,EACLC,OAAO,EACPC,MAAM,QACD,UAAU;AACjB,SAASC,IAAI,QAAQ,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEvCV,OAAO,CAACW,QAAQ,CACdV,aAAa,EACbC,WAAW,EACXC,YAAY,EACZC,WAAW,EACXC,KAAK,EACLC,OAAO,EACPC,MACF,CAAC;AAED,MAAMK,QAAQ,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACrB,MAAM;IAAEC;EAAa,CAAC,GAAGlB,SAAS,CAAC,CAAC;EACpC,MAAM,CAACmB,eAAe,EAAEC,kBAAkB,CAAC,GAAGvB,QAAQ,CAACqB,YAAY,IAAI,MAAM,CAAC;EAC9E,MAAM,CAACG,YAAY,EAAEC,eAAe,CAAC,GAAGzB,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAAC0B,OAAO,EAAEC,UAAU,CAAC,GAAG3B,QAAQ,CAAC,IAAI,CAAC;;EAE5C;EACA,MAAM,CAAC4B,iBAAiB,EAAEC,oBAAoB,CAAC,GAAG7B,QAAQ,CAAC,EAAE,CAAC;EAC9D,MAAM,CAAC8B,UAAU,EAAEC,aAAa,CAAC,GAAG/B,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACgC,SAAS,EAAEC,YAAY,CAAC,GAAGjC,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAACkC,QAAQ,EAAEC,WAAW,CAAC,GAAGnC,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACoC,OAAO,EAAEC,UAAU,CAAC,GAAGrC,QAAQ,CAAC,EAAE,CAAC;EAE1CC,SAAS,CAAC,MAAM;IACdqC,iBAAiB,CAAChB,eAAe,CAAC;EACpC,CAAC,EAAE,CAACA,eAAe,CAAC,CAAC;EAErB,MAAMgB,iBAAiB,GAAG,MAAOC,QAAQ,IAAK;IAC5C,IAAI;MACFZ,UAAU,CAAC,IAAI,CAAC;MAChB,MAAMa,QAAQ,GAAG,MAAMpC,KAAK,CAACqC,GAAG,CAAC,iBAAiBF,QAAQ,GAAG,CAAC;MAC9Dd,eAAe,CAACe,QAAQ,CAACE,IAAI,CAAC;;MAE9B;MACA,IAAIF,QAAQ,CAACE,IAAI,IAAIC,MAAM,CAACC,IAAI,CAACJ,QAAQ,CAACE,IAAI,CAAC,CAACG,MAAM,GAAG,CAAC,EAAE;QAC1D,MAAMC,UAAU,GAAGH,MAAM,CAACC,IAAI,CAACJ,QAAQ,CAACE,IAAI,CAAC,CAAC,CAAC,CAAC;QAChD,MAAMK,aAAa,GAAGJ,MAAM,CAACC,IAAI,CAACJ,QAAQ,CAACE,IAAI,CAACI,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC;QAC/D,MAAME,UAAU,GAAGL,MAAM,CAACC,IAAI,CAACJ,QAAQ,CAACE,IAAI,CAACI,UAAU,CAAC,CAACC,aAAa,CAAC,CAAC;QAExE,IAAIC,UAAU,CAACH,MAAM,GAAG,CAAC,IAAI,CAACjB,iBAAiB,EAAE;UAC/CC,oBAAoB,CAACmB,UAAU,CAAC,CAAC,CAAC,CAAC;QACrC;;QAEA;QACA,MAAMC,MAAM,GAAGN,MAAM,CAACC,IAAI,CAACJ,QAAQ,CAACE,IAAI,CAAC,CAACQ,IAAI,CAAC,CAAC;QAChD,IAAID,MAAM,CAACJ,MAAM,GAAG,CAAC,EAAE;UACrB,MAAM,CAACM,cAAc,EAAEC,aAAa,CAAC,GAAGC,cAAc,CAACJ,MAAM,CAAC,CAAC,CAAC,CAAC;UACjE,MAAM,CAACK,YAAY,EAAEC,WAAW,CAAC,GAAGF,cAAc,CAACJ,MAAM,CAACA,MAAM,CAACJ,MAAM,GAAG,CAAC,CAAC,CAAC;UAE7E,IAAI,CAACf,UAAU,EAAE;YACfC,aAAa,CAACqB,aAAa,CAAC;YAC5BnB,YAAY,CAACkB,cAAc,CAAC;UAC9B;UACA,IAAI,CAACjB,QAAQ,EAAE;YACbC,WAAW,CAACoB,WAAW,CAAC;YACxBlB,UAAU,CAACiB,YAAY,CAAC;UAC1B;QACF;MACF;IACF,CAAC,CAAC,OAAOE,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;IACvD,CAAC,SAAS;MACR7B,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAM0B,cAAc,GAAIK,YAAY,IAAK;IACvC,MAAM,CAACC,IAAI,EAAEC,KAAK,CAAC,GAAGF,YAAY,CAACG,KAAK,CAAC,KAAK,CAAC;IAC/C,OAAO,CAACF,IAAI,EAAEC,KAAK,CAAC;EACtB,CAAC;;EAED;EACA,MAAME,kBAAkB,GAAGA,CAACH,IAAI,EAAEC,KAAK,KAAK;IAC1C,OAAO,GAAGD,IAAI,MAAMC,KAAK,CAACG,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE;EAC9C,CAAC;;EAED;EACA,MAAMC,sBAAsB,GAAGA,CAAA,KAAM;IACnC,IAAI,CAACxC,YAAY,IAAImB,MAAM,CAACC,IAAI,CAACpB,YAAY,CAAC,CAACqB,MAAM,KAAK,CAAC,EAAE,OAAO,EAAE;IAEtE,MAAMC,UAAU,GAAGH,MAAM,CAACC,IAAI,CAACpB,YAAY,CAAC,CAAC,CAAC,CAAC;IAC/C,MAAMuB,aAAa,GAAGJ,MAAM,CAACC,IAAI,CAACpB,YAAY,CAACsB,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC;IAC9D,OAAOH,MAAM,CAACC,IAAI,CAACpB,YAAY,CAACsB,UAAU,CAAC,CAACC,aAAa,CAAC,CAAC;EAC7D,CAAC;;EAED;EACA,MAAMkB,uBAAuB,GAAGA,CAAA,KAAM;IACpC,IAAI,CAACzC,YAAY,EAAE,OAAO;MAAEyB,MAAM,EAAE,EAAE;MAAEiB,KAAK,EAAE;IAAG,CAAC;IAEnD,MAAMC,WAAW,GAAGxB,MAAM,CAACC,IAAI,CAACpB,YAAY,CAAC,CAAC4C,GAAG,CAACf,cAAc,CAAC;IACjE,MAAMJ,MAAM,GAAG,CAAC,GAAG,IAAIoB,GAAG,CAACF,WAAW,CAACC,GAAG,CAAC,CAAC,CAACT,IAAI,EAAEC,KAAK,CAAC,KAAKA,KAAK,CAAC,CAAC,CAAC,CAACV,IAAI,CAAC,CAAC;IAC7E,MAAMgB,KAAK,GAAG,CAAC,GAAG,IAAIG,GAAG,CAACF,WAAW,CAACC,GAAG,CAAC,CAAC,CAACT,IAAI,EAAEC,KAAK,CAAC,KAAKD,IAAI,CAAC,CAAC,CAAC,CAACT,IAAI,CAAC,CAAC;IAE3E,OAAO;MAAED,MAAM;MAAEiB;IAAM,CAAC;EAC1B,CAAC;;EAED;EACA,MAAMI,QAAQ,GAAGA,CAAA,KAAM;IACrB,MAAMC,GAAG,GAAG,IAAIC,IAAI,CAAC,CAAC;IACtB,MAAMC,YAAY,GAAG,CAACF,GAAG,CAACG,QAAQ,CAAC,CAAC,GAAG,CAAC,EAAEC,QAAQ,CAAC,CAAC,CAACZ,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;IACrE,MAAMa,WAAW,GAAGL,GAAG,CAACM,WAAW,CAAC,CAAC,CAACF,QAAQ,CAAC,CAAC;IAEhDxC,WAAW,CAACsC,YAAY,CAAC;IACzBpC,UAAU,CAACuC,WAAW,CAAC;EACzB,CAAC;;EAED;EACA,MAAME,YAAY,GAAGA,CAAA,KAAM;IACzB,IAAI,CAACtD,YAAY,IAAI,CAACI,iBAAiB,IAAI,CAACE,UAAU,IAAI,CAACE,SAAS,IAAI,CAACE,QAAQ,IAAI,CAACE,OAAO,EAAE;MAC7F,OAAO;QAAE2C,MAAM,EAAE,EAAE;QAAEC,QAAQ,EAAE;MAAG,CAAC;IACrC;;IAEA;IACA,MAAMC,YAAY,GAAG,CAAC,CAAC;IACvBtC,MAAM,CAACC,IAAI,CAACpB,YAAY,CAAC,CAAC0D,OAAO,CAACC,SAAS,IAAI;MAC7C,MAAM,CAACxB,IAAI,EAAEC,KAAK,CAAC,GAAGP,cAAc,CAAC8B,SAAS,CAAC;MAC/C,MAAMC,OAAO,GAAGC,QAAQ,CAAC1B,IAAI,CAAC;MAC9B,MAAM2B,QAAQ,GAAGD,QAAQ,CAACzB,KAAK,CAAC;MAChC,MAAM2B,YAAY,GAAGF,QAAQ,CAACrD,SAAS,CAAC;MACxC,MAAMoB,aAAa,GAAGiC,QAAQ,CAACvD,UAAU,CAAC;MAC1C,MAAM0D,UAAU,GAAGH,QAAQ,CAACjD,OAAO,CAAC;MACpC,MAAMmB,WAAW,GAAG8B,QAAQ,CAACnD,QAAQ,CAAC;MAEtC,MAAMuD,SAAS,GAAG,CAACL,OAAO,GAAGG,YAAY,IAAKH,OAAO,KAAKG,YAAY,IAAID,QAAQ,IAAIlC,aAAc,MAClFgC,OAAO,GAAGI,UAAU,IAAKJ,OAAO,KAAKI,UAAU,IAAIF,QAAQ,IAAI/B,WAAY,CAAC;MAE9F,IAAIkC,SAAS,EAAE;QACbR,YAAY,CAACE,SAAS,CAAC,GAAG3D,YAAY,CAAC2D,SAAS,CAAC;MACnD;IACF,CAAC,CAAC;;IAEF;IACA,MAAMO,YAAY,GAAG/C,MAAM,CAACC,IAAI,CAACqC,YAAY,CAAC,CAAC/B,IAAI,CAAC,CAACyC,CAAC,EAAEC,CAAC,KAAK;MAC5D,MAAM,CAACC,KAAK,EAAEC,MAAM,CAAC,GAAGzC,cAAc,CAACsC,CAAC,CAAC;MACzC,MAAM,CAACI,KAAK,EAAEC,MAAM,CAAC,GAAG3C,cAAc,CAACuC,CAAC,CAAC;MACzC,OAAO,IAAIpB,IAAI,CAACqB,KAAK,EAAEC,MAAM,GAAG,CAAC,CAAC,GAAG,IAAItB,IAAI,CAACuB,KAAK,EAAEC,MAAM,GAAG,CAAC,CAAC;IAClE,CAAC,CAAC;;IAEF;IACA,MAAMjB,MAAM,GAAGW,YAAY,CAACtB,GAAG,CAACe,SAAS,IAAI;MAC3C,MAAM,CAACxB,IAAI,EAAEC,KAAK,CAAC,GAAGP,cAAc,CAAC8B,SAAS,CAAC;MAC/C,MAAMc,IAAI,GAAG,IAAIzB,IAAI,CAACb,IAAI,EAAEC,KAAK,GAAG,CAAC,CAAC;MACtC,OAAOqC,IAAI,CAACC,kBAAkB,CAAC,OAAO,EAAE;QAAEtC,KAAK,EAAE,OAAO;QAAED,IAAI,EAAE;MAAU,CAAC,CAAC;IAC9E,CAAC,CAAC;;IAEF;IACA,MAAMwC,gBAAgB,GAAG,IAAI9B,GAAG,CAAC,CAAC;IAClCqB,YAAY,CAACR,OAAO,CAACC,SAAS,IAAI;MAChCxC,MAAM,CAACyD,MAAM,CAACnB,YAAY,CAACE,SAAS,CAAC,CAAC,CAACD,OAAO,CAAC3C,QAAQ,IAAI;QACzD,IAAIA,QAAQ,CAACX,iBAAiB,CAAC,EAAE;UAC/Be,MAAM,CAACC,IAAI,CAACL,QAAQ,CAACX,iBAAiB,CAAC,CAAC,CAACsD,OAAO,CAACmB,MAAM,IAAI;YACzDF,gBAAgB,CAACG,GAAG,CAACD,MAAM,CAAC;UAC9B,CAAC,CAAC;QACJ;MACF,CAAC,CAAC;IACJ,CAAC,CAAC;;IAEF;IACA,MAAME,MAAM,GAAG,CACb,mBAAmB,EACnB,mBAAmB,EACnB,mBAAmB,EACnB,mBAAmB,EACnB,oBAAoB,EACpB,mBAAmB,EACnB,oBAAoB,EACpB,mBAAmB,CACpB;;IAED;IACA,MAAMvB,QAAQ,GAAGwB,KAAK,CAACC,IAAI,CAACN,gBAAgB,CAAC,CAAC/B,GAAG,CAAC,CAACsC,WAAW,EAAEC,KAAK,KAAK;MACxE,MAAMjE,IAAI,GAAGgD,YAAY,CAACtB,GAAG,CAACe,SAAS,IAAI;QACzC;QACA,IAAIyB,KAAK,GAAG,CAAC;QACb,IAAIC,KAAK,GAAG,CAAC;QACblE,MAAM,CAACyD,MAAM,CAACnB,YAAY,CAACE,SAAS,CAAC,CAAC,CAACD,OAAO,CAAC3C,QAAQ,IAAI;UACzD,IAAIA,QAAQ,CAACX,iBAAiB,CAAC,IAAIW,QAAQ,CAACX,iBAAiB,CAAC,CAAC8E,WAAW,CAAC,KAAKI,SAAS,EAAE;YACzFF,KAAK,IAAIrE,QAAQ,CAACX,iBAAiB,CAAC,CAAC8E,WAAW,CAAC;YACjDG,KAAK,EAAE;UACT;QACF,CAAC,CAAC;QACF,OAAOA,KAAK,GAAG,CAAC,GAAGD,KAAK,GAAGC,KAAK,GAAG,CAAC,CAAC,CAAC;MACxC,CAAC,CAAC;MAEF,OAAO;QACLE,KAAK,EAAEL,WAAW;QAClBhE,IAAI,EAAEA,IAAI;QACVsE,WAAW,EAAET,MAAM,CAACI,KAAK,GAAGJ,MAAM,CAAC1D,MAAM,CAAC;QAC1CoE,eAAe,EAAEV,MAAM,CAACI,KAAK,GAAGJ,MAAM,CAAC1D,MAAM,CAAC,GAAG,IAAI;QACrDqE,OAAO,EAAE;MACX,CAAC;IACH,CAAC,CAAC;IAEF,OAAO;MAAEnC,MAAM;MAAEC;IAAS,CAAC;EAC7B,CAAC;EAED,MAAMmC,YAAY,GAAG;IACnBC,UAAU,EAAE,IAAI;IAChBC,OAAO,EAAE;MACPC,MAAM,EAAE;QACNC,QAAQ,EAAE;MACZ,CAAC;MACDC,KAAK,EAAE;QACLC,OAAO,EAAE,IAAI;QACbC,IAAI,EAAE,GAAG9F,iBAAiB;MAC5B;IACF,CAAC;IACD+F,MAAM,EAAE;MACNC,CAAC,EAAE;QACDC,WAAW,EAAE,IAAI;QACjBC,KAAK,EAAE;UACLC,QAAQ,EAAE,SAAAA,CAASC,KAAK,EAAE;YACxB,OAAOA,KAAK,GAAG,GAAG;UACpB;QACF,CAAC;QACDR,KAAK,EAAE;UACLC,OAAO,EAAE,IAAI;UACbC,IAAI,EAAE;QACR;MACF,CAAC;MACDO,CAAC,EAAE;QACDT,KAAK,EAAE;UACLC,OAAO,EAAE,IAAI;UACbC,IAAI,EAAE;QACR;MACF;IACF;EACF,CAAC;EAED,MAAMQ,mBAAmB,GAAGlE,sBAAsB,CAAC,CAAC;EACpD,MAAM;IAAEf,MAAM;IAAEiB;EAAM,CAAC,GAAGD,uBAAuB,CAAC,CAAC;EACnD,MAAMkE,SAAS,GAAGrD,YAAY,CAAC,CAAC;EAEhC,oBACE7D,OAAA;IAAKmH,SAAS,EAAC,cAAc;IAAAC,QAAA,gBAC3BpH,OAAA,CAACZ,gBAAgB;MACfiB,eAAe,EAAEA,eAAgB;MACjCgH,gBAAgB,EAAE/G;IAAmB;MAAAgH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACtC,CAAC,eACFzH,OAAA;MAAGmH,SAAS,EAAC,eAAe;MAAAC,QAAA,EAAC;IAAiB;MAAAE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAG,CAAC,eAClDzH,OAAA;MAAAsH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAQ,CAAC,EACRhH,OAAO,gBACNT,OAAA;MAAKmH,SAAS,EAAC,aAAa;MAAAC,QAAA,eAC1BpH,OAAA;QAAKmH,SAAS,EAAC,gBAAgB;QAACO,IAAI,EAAC,QAAQ;QAAAN,QAAA,eAC3CpH,OAAA;UAAMmH,SAAS,EAAC,iBAAiB;UAAAC,QAAA,EAAC;QAAU;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChD;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,gBAENzH,OAAA;MAAKmH,SAAS,EAAC,kBAAkB;MAAAC,QAAA,EAC9B7G,YAAY,iBACXP,OAAA;QAAKmH,SAAS,EAAC,iBAAiB;QAAAC,QAAA,gBAE9BpH,OAAA;UAAKmH,SAAS,EAAC,gBAAgB;UAAAC,QAAA,gBAE7BpH,OAAA;YAAKmH,SAAS,EAAC,kCAAkC;YAAAC,QAAA,gBAC/CpH,OAAA;cAAOmH,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAC;YAAS;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAClDzH,OAAA;cACEmH,SAAS,EAAC,aAAa;cACvBJ,KAAK,EAAEpG,iBAAkB;cACzBgH,QAAQ,EAAGC,CAAC,IAAKhH,oBAAoB,CAACgH,CAAC,CAACC,MAAM,CAACd,KAAK,CAAE;cAAAK,QAAA,gBAEtDpH,OAAA;gBAAQ+G,KAAK,EAAC,EAAE;gBAAAK,QAAA,EAAC;cAAgB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,EACzCR,mBAAmB,CAAC9D,GAAG,CAAC2E,IAAI,iBAC3B9H,OAAA;gBAAmB+G,KAAK,EAAEe,IAAK;gBAAAV,QAAA,EAAEU;cAAI,GAAxBA,IAAI;gBAAAR,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAA6B,CAC/C,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eAGNzH,OAAA;YAAKmH,SAAS,EAAC,eAAe;YAAAC,QAAA,gBAE5BpH,OAAA;cAAKmH,SAAS,EAAC,YAAY;cAAAC,QAAA,gBACzBpH,OAAA;gBAAOmH,SAAS,EAAC,eAAe;gBAAAC,QAAA,EAAC;cAAU;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACnDzH,OAAA;gBAAKmH,SAAS,EAAC,gBAAgB;gBAAAC,QAAA,gBAC7BpH,OAAA;kBACE+G,KAAK,EAAElG,UAAW;kBAClB8G,QAAQ,EAAGC,CAAC,IAAK9G,aAAa,CAAC8G,CAAC,CAACC,MAAM,CAACd,KAAK,CAAE;kBAAAK,QAAA,gBAE/CpH,OAAA;oBAAQ+G,KAAK,EAAC,EAAE;oBAAAK,QAAA,EAAC;kBAAK;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,EAC9BzF,MAAM,CAACmB,GAAG,CAACR,KAAK,iBACf3C,OAAA;oBAAoB+G,KAAK,EAAEpE,KAAM;oBAAAyE,QAAA,EAC9B,IAAI7D,IAAI,CAAC,IAAI,EAAEZ,KAAK,GAAG,CAAC,CAAC,CAACsC,kBAAkB,CAAC,OAAO,EAAE;sBAAEtC,KAAK,EAAE;oBAAQ,CAAC;kBAAC,GAD/DA,KAAK;oBAAA2E,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAEV,CACT,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACI,CAAC,eACTzH,OAAA;kBACE+G,KAAK,EAAEhG,SAAU;kBACjB4G,QAAQ,EAAGC,CAAC,IAAK5G,YAAY,CAAC4G,CAAC,CAACC,MAAM,CAACd,KAAK,CAAE;kBAAAK,QAAA,gBAE9CpH,OAAA;oBAAQ+G,KAAK,EAAC,EAAE;oBAAAK,QAAA,EAAC;kBAAI;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,EAC7BxE,KAAK,CAACE,GAAG,CAACT,IAAI,iBACb1C,OAAA;oBAAmB+G,KAAK,EAAErE,IAAK;oBAAA0E,QAAA,EAAE1E;kBAAI,GAAxBA,IAAI;oBAAA4E,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAA6B,CAC/C,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAGNzH,OAAA;cAAKmH,SAAS,EAAC,YAAY;cAAAC,QAAA,gBACzBpH,OAAA;gBAAOmH,SAAS,EAAC,eAAe;gBAAAC,QAAA,EAAC;cAAQ;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACjDzH,OAAA;gBAAKmH,SAAS,EAAC,oBAAoB;gBAAAC,QAAA,gBACjCpH,OAAA;kBACE+G,KAAK,EAAE9F,QAAS;kBAChB0G,QAAQ,EAAGC,CAAC,IAAK1G,WAAW,CAAC0G,CAAC,CAACC,MAAM,CAACd,KAAK,CAAE;kBAAAK,QAAA,gBAE7CpH,OAAA;oBAAQ+G,KAAK,EAAC,EAAE;oBAAAK,QAAA,EAAC;kBAAK;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,EAC9BzF,MAAM,CAACmB,GAAG,CAACR,KAAK,iBACf3C,OAAA;oBAAoB+G,KAAK,EAAEpE,KAAM;oBAAAyE,QAAA,EAC9B,IAAI7D,IAAI,CAAC,IAAI,EAAEZ,KAAK,GAAG,CAAC,CAAC,CAACsC,kBAAkB,CAAC,OAAO,EAAE;sBAAEtC,KAAK,EAAE;oBAAQ,CAAC;kBAAC,GAD/DA,KAAK;oBAAA2E,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAEV,CACT,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACI,CAAC,eACTzH,OAAA;kBACE+G,KAAK,EAAE5F,OAAQ;kBACfwG,QAAQ,EAAGC,CAAC,IAAKxG,UAAU,CAACwG,CAAC,CAACC,MAAM,CAACd,KAAK,CAAE;kBAAAK,QAAA,gBAE5CpH,OAAA;oBAAQ+G,KAAK,EAAC,EAAE;oBAAAK,QAAA,EAAC;kBAAI;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,EAC7BxE,KAAK,CAACE,GAAG,CAACT,IAAI,iBACb1C,OAAA;oBAAmB+G,KAAK,EAAErE,IAAK;oBAAA0E,QAAA,EAAE1E;kBAAI,GAAxBA,IAAI;oBAAA4E,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAA6B,CAC/C,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACI,CAAC,eACTzH,OAAA;kBACEmH,SAAS,EAAC,YAAY;kBACtBY,OAAO,EAAE1E,QAAS;kBAAA+D,QAAA,EACnB;gBAED;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,EAGL9G,iBAAiB,IAAIuG,SAAS,CAACnD,QAAQ,CAACnC,MAAM,GAAG,CAAC,gBACjD5B,OAAA;UAAKmH,SAAS,EAAC,eAAe;UAACa,KAAK,EAAE;YAAEC,MAAM,EAAE;UAAQ,CAAE;UAAAb,QAAA,eACxDpH,OAAA,CAACF,IAAI;YAAC2B,IAAI,EAAEyF,SAAU;YAACgB,OAAO,EAAEhC;UAAa;YAAAoB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7C,CAAC,gBAENzH,OAAA;UAAKmH,SAAS,EAAC,kBAAkB;UAAAC,QAAA,EAAC;QAElC;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE;IACN;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAACtH,EAAA,CAnVID,QAAQ;EAAA,QACahB,SAAS;AAAA;AAAAiJ,EAAA,GAD9BjI,QAAQ;AAqVd,eAAeA,QAAQ;AAAC,IAAAiI,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}