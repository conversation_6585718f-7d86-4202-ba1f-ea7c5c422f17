{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Desktop/Coding/BVS/CrystalBall/insights-app/frontend/src/components/StrategyDropdown.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport axios from 'axios';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst StrategyDropdown = ({\n  currentStrategy,\n  onStrategyChange\n}) => {\n  _s();\n  const [strategies, setStrategies] = useState([]);\n  const [isOpen, setIsOpen] = useState(false);\n  useEffect(() => {\n    fetchStrategies();\n  }, []);\n  const fetchStrategies = async () => {\n    try {\n      const response = await axios.get('/api/strategies/');\n      setStrategies(response.data.strategies);\n    } catch (error) {\n      console.error('Error fetching strategies:', error);\n      // Fallback to hardcoded strategies to match your original template\n      setStrategies(['Alle (<PERSON><PERSON><PERSON><PERSON><PERSON>)', 'Defensiv', 'Ausgewogen', 'Dividende & Zins', 'Risiko']);\n    }\n  };\n  const handleStrategySelect = strategy => {\n    onStrategyChange(strategy);\n    setIsOpen(false);\n  };\n\n  // Filter out current strategy from dropdown options\n  const filteredStrategies = strategies.filter(strategy => strategy !== currentStrategy);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"dropdown\",\n    children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n      className: \"dropdown-toggle\",\n      onClick: () => setIsOpen(!isOpen),\n      role: \"button\",\n      \"aria-expanded\": isOpen,\n      children: [currentStrategy, \" \", /*#__PURE__*/_jsxDEV(\"span\", {\n        children: \"\\u25BE\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 39,\n        columnNumber: 27\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 33,\n      columnNumber: 7\n    }, this), isOpen && /*#__PURE__*/_jsxDEV(\"ul\", {\n      className: \"dropdown-menu show\",\n      children: filteredStrategies.map(strategy => /*#__PURE__*/_jsxDEV(\"li\", {\n        children: /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"dropdown-item\",\n          type: \"button\",\n          onClick: () => handleStrategySelect(strategy),\n          children: strategy\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 45,\n          columnNumber: 15\n        }, this)\n      }, strategy, false, {\n        fileName: _jsxFileName,\n        lineNumber: 44,\n        columnNumber: 13\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 42,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 32,\n    columnNumber: 5\n  }, this);\n};\n_s(StrategyDropdown, \"EZihrh7rGt5lfk2XaQ/ElPbXQAI=\");\n_c = StrategyDropdown;\nexport default StrategyDropdown;\nvar _c;\n$RefreshReg$(_c, \"StrategyDropdown\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "axios", "jsxDEV", "_jsxDEV", "StrategyDropdown", "currentStrategy", "onStrategyChange", "_s", "strategies", "setStrategies", "isOpen", "setIsOpen", "fetchStrategies", "response", "get", "data", "error", "console", "handleStrategySelect", "strategy", "filteredStrategies", "filter", "className", "children", "onClick", "role", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "map", "type", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Desktop/Coding/BVS/CrystalBall/insights-app/frontend/src/components/StrategyDropdown.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport axios from 'axios';\n\nconst StrategyDropdown = ({ currentStrategy, onStrategyChange }) => {\n  const [strategies, setStrategies] = useState([]);\n  const [isOpen, setIsOpen] = useState(false);\n\n  useEffect(() => {\n    fetchStrategies();\n  }, []);\n\n  const fetchStrategies = async () => {\n    try {\n      const response = await axios.get('/api/strategies/');\n      setStrategies(response.data.strategies);\n    } catch (error) {\n      console.error('Error fetching strategies:', error);\n      // Fallback to hardcoded strategies to match your original template\n      setStrategies(['Alle (Durchschnitt)', 'Defensiv', 'Ausgewogen', 'Dividende & Zins', 'Risiko']);\n    }\n  };\n\n  const handleStrategySelect = (strategy) => {\n    onStrategyChange(strategy);\n    setIsOpen(false);\n  };\n\n  // Filter out current strategy from dropdown options\n  const filteredStrategies = strategies.filter(strategy => strategy !== currentStrategy);\n\n  return (\n    <div className=\"dropdown\">\n      <h1 \n        className=\"dropdown-toggle\" \n        onClick={() => setIsOpen(!isOpen)}\n        role=\"button\"\n        aria-expanded={isOpen}\n      >\n        {currentStrategy} <span>&#9662;</span>\n      </h1>\n      {isOpen && (\n        <ul className=\"dropdown-menu show\">\n          {filteredStrategies.map((strategy) => (\n            <li key={strategy}>\n              <button\n                className=\"dropdown-item\"\n                type=\"button\"\n                onClick={() => handleStrategySelect(strategy)}\n              >\n                {strategy}\n              </button>\n            </li>\n          ))}\n        </ul>\n      )}\n    </div>\n  );\n};\n\nexport default StrategyDropdown;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,OAAOC,KAAK,MAAM,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1B,MAAMC,gBAAgB,GAAGA,CAAC;EAAEC,eAAe;EAAEC;AAAiB,CAAC,KAAK;EAAAC,EAAA;EAClE,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAGV,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACW,MAAM,EAAEC,SAAS,CAAC,GAAGZ,QAAQ,CAAC,KAAK,CAAC;EAE3CC,SAAS,CAAC,MAAM;IACdY,eAAe,CAAC,CAAC;EACnB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMA,eAAe,GAAG,MAAAA,CAAA,KAAY;IAClC,IAAI;MACF,MAAMC,QAAQ,GAAG,MAAMZ,KAAK,CAACa,GAAG,CAAC,kBAAkB,CAAC;MACpDL,aAAa,CAACI,QAAQ,CAACE,IAAI,CAACP,UAAU,CAAC;IACzC,CAAC,CAAC,OAAOQ,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;MAClD;MACAP,aAAa,CAAC,CAAC,qBAAqB,EAAE,UAAU,EAAE,YAAY,EAAE,kBAAkB,EAAE,QAAQ,CAAC,CAAC;IAChG;EACF,CAAC;EAED,MAAMS,oBAAoB,GAAIC,QAAQ,IAAK;IACzCb,gBAAgB,CAACa,QAAQ,CAAC;IAC1BR,SAAS,CAAC,KAAK,CAAC;EAClB,CAAC;;EAED;EACA,MAAMS,kBAAkB,GAAGZ,UAAU,CAACa,MAAM,CAACF,QAAQ,IAAIA,QAAQ,KAAKd,eAAe,CAAC;EAEtF,oBACEF,OAAA;IAAKmB,SAAS,EAAC,UAAU;IAAAC,QAAA,gBACvBpB,OAAA;MACEmB,SAAS,EAAC,iBAAiB;MAC3BE,OAAO,EAAEA,CAAA,KAAMb,SAAS,CAAC,CAACD,MAAM,CAAE;MAClCe,IAAI,EAAC,QAAQ;MACb,iBAAef,MAAO;MAAAa,QAAA,GAErBlB,eAAe,EAAC,GAAC,eAAAF,OAAA;QAAAoB,QAAA,EAAM;MAAO;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACpC,CAAC,EACJnB,MAAM,iBACLP,OAAA;MAAImB,SAAS,EAAC,oBAAoB;MAAAC,QAAA,EAC/BH,kBAAkB,CAACU,GAAG,CAAEX,QAAQ,iBAC/BhB,OAAA;QAAAoB,QAAA,eACEpB,OAAA;UACEmB,SAAS,EAAC,eAAe;UACzBS,IAAI,EAAC,QAAQ;UACbP,OAAO,EAAEA,CAAA,KAAMN,oBAAoB,CAACC,QAAQ,CAAE;UAAAI,QAAA,EAE7CJ;QAAQ;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC,GAPFV,QAAQ;QAAAO,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAQb,CACL;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CACL;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAACtB,EAAA,CAtDIH,gBAAgB;AAAA4B,EAAA,GAAhB5B,gBAAgB;AAwDtB,eAAeA,gBAAgB;AAAC,IAAA4B,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}