{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Desktop/Coding/BVS/CrystalBall/insights-app/frontend/src/components/Strategy.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Link, useParams } from 'react-router-dom';\nimport axios from 'axios';\nimport StrategyDropdown from './StrategyDropdown';\nimport { Chart as ChartJS, CategoryScale, LinearScale, PointElement, LineElement, Title, Tooltip, Legend, elements } from 'chart.js';\nimport { Line } from 'react-chartjs-2';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nChartJS.register(CategoryScale, LinearScale, PointElement, LineElement, Title, Tooltip, Legend);\nconst Strategy = () => {\n  _s();\n  const {\n    strategyName\n  } = useParams();\n  const [currentStrategy, setCurrentStrategy] = useState(strategyName || 'Alle (Durchschnitt)');\n  const [strategyData, setStrategyData] = useState(null);\n  const [loading, setLoading] = useState(true);\n\n  // Chart state\n  const [selectedAttribute, setSelectedAttribute] = useState('');\n  const [startMonth, setStartMonth] = useState('');\n  const [startYear, setStartYear] = useState('');\n  const [endMonth, setEndMonth] = useState('');\n  const [endYear, setEndYear] = useState('');\n  useEffect(() => {\n    fetchStrategyData(currentStrategy);\n  }, [currentStrategy]);\n  const fetchStrategyData = async strategy => {\n    try {\n      setLoading(true);\n      const response = await axios.get(`/api/strategy/${strategy}/`);\n      setStrategyData(response.data);\n\n      // Initialize dropdowns with first available data\n      if (response.data && Object.keys(response.data).length > 0) {\n        const firstMonth = Object.keys(response.data)[0];\n        const firstStrategy = Object.keys(response.data[firstMonth])[0];\n        const attributes = Object.keys(response.data[firstMonth][firstStrategy]);\n        if (attributes.length > 0 && !selectedAttribute) {\n          setSelectedAttribute(attributes[0]);\n        }\n\n        // Set default date range\n        const months = Object.keys(response.data).sort();\n        if (months.length > 0) {\n          const [startYearMonth, startMonthNum] = parseMonthYear(months[0]);\n          const [endYearMonth, endMonthNum] = parseMonthYear(months[months.length - 1]);\n          if (!startMonth) {\n            setStartMonth(startMonthNum);\n            setStartYear(startYearMonth);\n          }\n          if (!endMonth) {\n            setEndMonth(endMonthNum);\n            setEndYear(endYearMonth);\n          }\n        }\n      }\n    } catch (error) {\n      console.error('Error fetching strategy data:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Helper function to parse month-year string like \"2025 - 04\"\n  const parseMonthYear = monthYearStr => {\n    const [year, month] = monthYearStr.split(' - ');\n    return [year, month];\n  };\n\n  // Helper function to create month-year string\n  // const createMonthYearStr = (year, month) => {\n  //   return `${year} - ${month.padStart(2, '0')}`;\n  // };\n\n  // Get available attributes from data\n  const getAvailableAttributes = () => {\n    if (!strategyData || Object.keys(strategyData).length === 0) return [];\n    const firstMonth = Object.keys(strategyData)[0];\n    const firstStrategy = Object.keys(strategyData[firstMonth])[0];\n    return Object.keys(strategyData[firstMonth][firstStrategy]);\n  };\n\n  // Get available months and years\n  const getAvailableMonthsYears = () => {\n    if (!strategyData) return {\n      months: [],\n      years: []\n    };\n    const monthsYears = Object.keys(strategyData).map(parseMonthYear);\n    const months = [...new Set(monthsYears.map(([year, month]) => month))].sort();\n    const years = [...new Set(monthsYears.map(([year, month]) => year))].sort();\n    return {\n      months,\n      years\n    };\n  };\n\n  // Set end date to current month/year\n  const setToNow = () => {\n    const now = new Date();\n    const currentMonth = (now.getMonth() + 1).toString().padStart(2, '0');\n    const currentYear = now.getFullYear().toString();\n    setEndMonth(currentMonth);\n    setEndYear(currentYear);\n  };\n\n  // Set start date to first available month/year\n  const setToStart = () => {\n    if (!strategyData || Object.keys(strategyData).length === 0) return;\n    const months = Object.keys(strategyData).sort();\n    if (months.length > 0) {\n      const [startYearMonth, startMonthNum] = parseMonthYear(months[0]);\n      setStartMonth(startMonthNum);\n      setStartYear(startYearMonth);\n    }\n  };\n\n  // Get chart data filtered by selected strategy\n  const getChartData = () => {\n    if (!strategyData || !selectedAttribute || !startMonth || !startYear || !endMonth || !endYear) {\n      return {\n        labels: [],\n        datasets: []\n      };\n    }\n\n    // Filter data by date range\n    const filteredData = {};\n    Object.keys(strategyData).forEach(monthYear => {\n      const [year, month] = parseMonthYear(monthYear);\n      const yearNum = parseInt(year);\n      const monthNum = parseInt(month);\n      const startYearNum = parseInt(startYear);\n      const startMonthNum = parseInt(startMonth);\n      const endYearNum = parseInt(endYear);\n      const endMonthNum = parseInt(endMonth);\n      const isInRange = (yearNum > startYearNum || yearNum === startYearNum && monthNum >= startMonthNum) && (yearNum < endYearNum || yearNum === endYearNum && monthNum <= endMonthNum);\n      if (isInRange) {\n        // Filter by selected strategy\n        const monthData = strategyData[monthYear];\n        if (currentStrategy === \"Alle (Durchschnitt)\") {\n          filteredData[monthYear] = monthData;\n        } else if (monthData[currentStrategy]) {\n          filteredData[monthYear] = {\n            [currentStrategy]: monthData[currentStrategy]\n          };\n        }\n      }\n    });\n\n    // Sort months chronologically\n    const sortedMonths = Object.keys(filteredData).sort((a, b) => {\n      const [yearA, monthA] = parseMonthYear(a);\n      const [yearB, monthB] = parseMonthYear(b);\n      return new Date(yearA, monthA - 1) - new Date(yearB, monthB - 1);\n    });\n\n    // Create labels (formatted month names)\n    const labels = sortedMonths.map(monthYear => {\n      const [year, month] = parseMonthYear(monthYear);\n      const date = new Date(year, month - 1);\n      return date.toLocaleDateString('en-US', {\n        month: 'short',\n        year: 'numeric'\n      });\n    });\n\n    // Calculate coverage for each month\n    const coverage = sortedMonths.map(monthYear => {\n      let totalCoverage = 0;\n      let strategyCount = 0;\n      Object.values(filteredData[monthYear]).forEach(strategy => {\n        if (strategy[selectedAttribute]) {\n          const monthTotal = Object.values(strategy[selectedAttribute]).reduce((sum, value) => sum + value, 0);\n          totalCoverage += monthTotal;\n          strategyCount++;\n        }\n      });\n      return strategyCount > 0 ? totalCoverage / strategyCount : 0;\n    });\n\n    // Get all unique currencies/subcategories for the selected attribute\n    const allSubcategories = new Set();\n    sortedMonths.forEach(monthYear => {\n      Object.values(filteredData[monthYear]).forEach(strategy => {\n        if (strategy[selectedAttribute]) {\n          Object.keys(strategy[selectedAttribute]).forEach(subcat => {\n            allSubcategories.add(subcat);\n          });\n        }\n      });\n    });\n\n    // Generate colors for each line\n    const colors = ['rgb(255, 99, 132)', 'rgb(54, 162, 235)', 'rgb(255, 205, 86)', 'rgb(75, 192, 192)', 'rgb(153, 102, 255)', 'rgb(255, 159, 64)', 'rgb(199, 199, 199)', 'rgb(83, 102, 147)'];\n\n    // Create datasets for each subcategory\n    const datasets = Array.from(allSubcategories).map((subcategory, index) => {\n      const data = sortedMonths.map(monthYear => {\n        // Aggregate data across selected strategies for this month and subcategory\n        let total = 0;\n        let count = 0;\n        Object.values(filteredData[monthYear]).forEach(strategy => {\n          if (strategy[selectedAttribute] && strategy[selectedAttribute][subcategory] !== undefined) {\n            total += strategy[selectedAttribute][subcategory];\n            count++;\n          }\n        });\n        return count > 0 ? total / count : 0; // Average across strategies\n      });\n      return {\n        label: subcategory,\n        data: data,\n        borderColor: colors[index % colors.length],\n        backgroundColor: colors[index % colors.length],\n        // + '20' to make them a dark shade\n        tension: 0.1\n      };\n    });\n    return {\n      labels,\n      datasets,\n      coverage\n    };\n  };\n\n  // Custom plugin to display coverage at the top\n  const coveragePlugin = {\n    id: 'coverageDisplay',\n    afterDraw: chart => {\n      const {\n        ctx,\n        chartArea,\n        scales\n      } = chart;\n      const chartData = getChartData();\n      if (!chartData.coverage || chartData.coverage.length === 0) return;\n      ctx.save();\n      ctx.font = '12px Arial';\n      ctx.textAlign = 'center';\n      chartData.coverage.forEach((coverage, index) => {\n        // Determine color based on coverage percentage\n        let color = '#000000'; // Black for >= 90%\n        if (coverage < 80) {\n          color = '#dc3545'; // Red for < 80%\n        } else if (coverage < 90) {\n          color = '#ffc107'; // Yellow for < 90%\n        }\n        ctx.fillStyle = color;\n\n        // Calculate x position for each label\n        const xScale = scales.x;\n        const xPos = xScale.getPixelForTick(index);\n        const yPos = chartArea.top - 20;\n\n        // Draw coverage percentage\n        ctx.fillText(`${coverage.toFixed(1)}%`, xPos, yPos);\n      });\n      ctx.restore();\n    }\n  };\n  const chartOptions = {\n    responsive: true,\n    plugins: {\n      legend: {\n        position: 'bottom',\n        labels: {\n          usePointStyle: true,\n          pointStyle: 'circle',\n          boxWidth: 12,\n          boxHeight: 8\n        }\n      },\n      title: {\n        display: true,\n        text: `${selectedAttribute} Distribution Over Time`\n      },\n      coverageDisplay: true\n    },\n    layout: {\n      padding: {\n        top: 150 // Increased from 30 to 50 for more space\n      }\n    },\n    scales: {\n      y: {\n        beginAtZero: true,\n        ticks: {\n          callback: function (value) {\n            return value + '%';\n          }\n        },\n        title: {\n          display: true,\n          text: 'Percentage (%)'\n        }\n      },\n      x: {\n        title: {\n          display: true,\n          text: 'Month'\n        }\n      }\n    },\n    elements: {\n      point: {\n        radius: 4\n      }\n    }\n  };\n  const availableAttributes = getAvailableAttributes();\n  const {\n    months,\n    years\n  } = getAvailableMonthsYears();\n  const chartData = getChartData();\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"content-page\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"home-arrow-container\",\n        children: /*#__PURE__*/_jsxDEV(Link, {\n          to: \"/\",\n          className: \"back-to-home-arrow\",\n          children: [/*#__PURE__*/_jsxDEV(\"i\", {\n            className: \"bi bi-arrow-left\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 346,\n            columnNumber: 13\n          }, this), \" Home\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 345,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 344,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(StrategyDropdown, {\n        currentStrategy: currentStrategy,\n        onStrategyChange: setCurrentStrategy\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 349,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"section_title\",\n        children: \"Strategy Insights\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 353,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"hr\", {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 354,\n        columnNumber: 9\n      }, this), loading ? /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"spinner-border\",\n          role: \"status\",\n          children: /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"visually-hidden\",\n            children: \"Loading...\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 358,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 357,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 356,\n        columnNumber: 11\n      }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"strategy-content\",\n        children: strategyData && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"chart-container\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"chart-controls\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"control-group attribute-dropdown\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"control-label\",\n                children: \"Attribute\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 369,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                className: \"form-select\",\n                value: selectedAttribute,\n                onChange: e => setSelectedAttribute(e.target.value),\n                children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"\",\n                  children: \"Select Attribute\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 375,\n                  columnNumber: 23\n                }, this), availableAttributes.map(attr => /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: attr,\n                  children: attr\n                }, attr, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 377,\n                  columnNumber: 25\n                }, this))]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 370,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 368,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"date-controls\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"date-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"control-label\",\n                  children: \"Start Date\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 386,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"start-date-container\",\n                  children: [/*#__PURE__*/_jsxDEV(\"select\", {\n                    value: startMonth,\n                    onChange: e => setStartMonth(e.target.value),\n                    children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                      value: \"\",\n                      children: \"Month\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 392,\n                      columnNumber: 27\n                    }, this), months.map(month => /*#__PURE__*/_jsxDEV(\"option\", {\n                      value: month,\n                      children: new Date(2000, month - 1).toLocaleDateString('en-US', {\n                        month: 'short'\n                      })\n                    }, month, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 394,\n                      columnNumber: 29\n                    }, this))]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 388,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                    value: startYear,\n                    onChange: e => setStartYear(e.target.value),\n                    children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                      value: \"\",\n                      children: \"Year\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 403,\n                      columnNumber: 27\n                    }, this), years.map(year => /*#__PURE__*/_jsxDEV(\"option\", {\n                      value: year,\n                      children: year\n                    }, year, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 405,\n                      columnNumber: 29\n                    }, this))]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 399,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    className: \"start-button\",\n                    onClick: setToStart,\n                    children: \"Start\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 408,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 387,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 385,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"date-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"control-label\",\n                  children: \"End Date\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 419,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"end-date-container\",\n                  children: [/*#__PURE__*/_jsxDEV(\"select\", {\n                    value: endMonth,\n                    onChange: e => setEndMonth(e.target.value),\n                    children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                      value: \"\",\n                      children: \"Month\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 425,\n                      columnNumber: 27\n                    }, this), months.map(month => /*#__PURE__*/_jsxDEV(\"option\", {\n                      value: month,\n                      children: new Date(2000, month - 1).toLocaleDateString('en-US', {\n                        month: 'short'\n                      })\n                    }, month, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 427,\n                      columnNumber: 29\n                    }, this))]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 421,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                    value: endYear,\n                    onChange: e => setEndYear(e.target.value),\n                    children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                      value: \"\",\n                      children: \"Year\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 436,\n                      columnNumber: 27\n                    }, this), years.map(year => /*#__PURE__*/_jsxDEV(\"option\", {\n                      value: year,\n                      children: year\n                    }, year, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 438,\n                      columnNumber: 29\n                    }, this))]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 432,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    className: \"now-button\",\n                    onClick: setToNow,\n                    children: \"Now\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 441,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 420,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 418,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 383,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 366,\n            columnNumber: 17\n          }, this), selectedAttribute && chartData.datasets.length > 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"chart-wrapper\",\n            style: {\n              height: '400px'\n            },\n            children: /*#__PURE__*/_jsxDEV(Line, {\n              data: chartData,\n              options: chartOptions,\n              plugins: [coveragePlugin]\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 455,\n              columnNumber: 21\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 454,\n            columnNumber: 19\n          }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"alert alert-info\",\n            children: \"Please select an attribute and ensure data is available for the selected date range.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 458,\n            columnNumber: 19\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 364,\n          columnNumber: 15\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 362,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 343,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 341,\n    columnNumber: 5\n  }, this);\n};\n_s(Strategy, \"atB9glxjlJs0fJdw26zYur8N8BQ=\", false, function () {\n  return [useParams];\n});\n_c = Strategy;\nexport default Strategy;\nvar _c;\n$RefreshReg$(_c, \"Strategy\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Link", "useParams", "axios", "StrategyDropdown", "Chart", "ChartJS", "CategoryScale", "LinearScale", "PointElement", "LineElement", "Title", "<PERSON><PERSON><PERSON>", "Legend", "elements", "Line", "jsxDEV", "_jsxDEV", "register", "Strategy", "_s", "strategyName", "currentStrategy", "setCurrentStrategy", "strategyData", "setStrategyData", "loading", "setLoading", "selectedAttribute", "setSelectedAttribute", "startMonth", "setStartMonth", "startYear", "setStartYear", "endMonth", "setEndMonth", "endYear", "setEndYear", "fetchStrategyData", "strategy", "response", "get", "data", "Object", "keys", "length", "firstMonth", "firstStrategy", "attributes", "months", "sort", "startYearMonth", "startMonthNum", "parseMonthYear", "endYearMonth", "endMonthNum", "error", "console", "monthYearStr", "year", "month", "split", "getAvailableAttributes", "getAvailableMonthsYears", "years", "monthsYears", "map", "Set", "setToNow", "now", "Date", "currentMonth", "getMonth", "toString", "padStart", "currentYear", "getFullYear", "setToStart", "getChartData", "labels", "datasets", "filteredData", "for<PERSON>ach", "monthYear", "yearNum", "parseInt", "monthNum", "startYearNum", "endYearNum", "isInRange", "monthData", "sortedMonths", "a", "b", "yearA", "monthA", "yearB", "monthB", "date", "toLocaleDateString", "coverage", "totalCoverage", "strategyCount", "values", "monthTotal", "reduce", "sum", "value", "allSubcategories", "subcat", "add", "colors", "Array", "from", "subcategory", "index", "total", "count", "undefined", "label", "borderColor", "backgroundColor", "tension", "coveragePlugin", "id", "afterDraw", "chart", "ctx", "chartArea", "scales", "chartData", "save", "font", "textAlign", "color", "fillStyle", "xScale", "x", "xPos", "getPixelForTick", "yPos", "top", "fillText", "toFixed", "restore", "chartOptions", "responsive", "plugins", "legend", "position", "usePointStyle", "pointStyle", "boxWidth", "boxHeight", "title", "display", "text", "coverageDisplay", "layout", "padding", "y", "beginAtZero", "ticks", "callback", "point", "radius", "availableAttributes", "children", "className", "to", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onStrategyChange", "role", "onChange", "e", "target", "attr", "onClick", "style", "height", "options", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Desktop/Coding/BVS/CrystalBall/insights-app/frontend/src/components/Strategy.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { Link, useParams } from 'react-router-dom';\nimport axios from 'axios';\nimport StrategyDropdown from './StrategyDropdown';\nimport {\n  Chart as ChartJS,\n  CategoryScale,\n  LinearScale,\n  PointElement,\n  LineElement,\n  Title,\n  Tooltip,\n  Legend,\n  elements,\n} from 'chart.js';\nimport { Line } from 'react-chartjs-2';\n\nChartJS.register(\n  CategoryScale,\n  LinearScale,\n  PointElement,\n  LineElement,\n  Title,\n  Tooltip,\n  Legend\n);\n\nconst Strategy = () => {\n  const { strategyName } = useParams();\n  const [currentStrategy, setCurrentStrategy] = useState(strategyName || 'Alle (Durchschnitt)');\n  const [strategyData, setStrategyData] = useState(null);\n  const [loading, setLoading] = useState(true);\n\n  // Chart state\n  const [selectedAttribute, setSelectedAttribute] = useState('');\n  const [startMonth, setStartMonth] = useState('');\n  const [startYear, setStartYear] = useState('');\n  const [endMonth, setEndMonth] = useState('');\n  const [endYear, setEndYear] = useState('');\n\n  useEffect(() => {\n    fetchStrategyData(currentStrategy);\n  }, [currentStrategy]);\n\n  const fetchStrategyData = async (strategy) => {\n    try {\n      setLoading(true);\n      const response = await axios.get(`/api/strategy/${strategy}/`);\n      setStrategyData(response.data);\n\n      // Initialize dropdowns with first available data\n      if (response.data && Object.keys(response.data).length > 0) {\n        const firstMonth = Object.keys(response.data)[0];\n        const firstStrategy = Object.keys(response.data[firstMonth])[0];\n        const attributes = Object.keys(response.data[firstMonth][firstStrategy]);\n\n        if (attributes.length > 0 && !selectedAttribute) {\n          setSelectedAttribute(attributes[0]);\n        }\n\n        // Set default date range\n        const months = Object.keys(response.data).sort();\n        if (months.length > 0) {\n          const [startYearMonth, startMonthNum] = parseMonthYear(months[0]);\n          const [endYearMonth, endMonthNum] = parseMonthYear(months[months.length - 1]);\n\n          if (!startMonth) {\n            setStartMonth(startMonthNum);\n            setStartYear(startYearMonth);\n          }\n          if (!endMonth) {\n            setEndMonth(endMonthNum);\n            setEndYear(endYearMonth);\n          }\n        }\n      }\n    } catch (error) {\n      console.error('Error fetching strategy data:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Helper function to parse month-year string like \"2025 - 04\"\n  const parseMonthYear = (monthYearStr) => {\n    const [year, month] = monthYearStr.split(' - ');\n    return [year, month];\n  };\n\n  // Helper function to create month-year string\n  // const createMonthYearStr = (year, month) => {\n  //   return `${year} - ${month.padStart(2, '0')}`;\n  // };\n\n  // Get available attributes from data\n  const getAvailableAttributes = () => {\n    if (!strategyData || Object.keys(strategyData).length === 0) return [];\n\n    const firstMonth = Object.keys(strategyData)[0];\n    const firstStrategy = Object.keys(strategyData[firstMonth])[0];\n    return Object.keys(strategyData[firstMonth][firstStrategy]);\n  };\n\n  // Get available months and years\n  const getAvailableMonthsYears = () => {\n    if (!strategyData) return { months: [], years: [] };\n\n    const monthsYears = Object.keys(strategyData).map(parseMonthYear);\n    const months = [...new Set(monthsYears.map(([year, month]) => month))].sort();\n    const years = [...new Set(monthsYears.map(([year, month]) => year))].sort();\n\n    return { months, years };\n  };\n\n  // Set end date to current month/year\n  const setToNow = () => {\n    const now = new Date();\n    const currentMonth = (now.getMonth() + 1).toString().padStart(2, '0');\n    const currentYear = now.getFullYear().toString();\n\n    setEndMonth(currentMonth);\n    setEndYear(currentYear);\n  };\n\n  // Set start date to first available month/year\n  const setToStart = () => {\n    if (!strategyData || Object.keys(strategyData).length === 0) return;\n    \n    const months = Object.keys(strategyData).sort();\n    if (months.length > 0) {\n      const [startYearMonth, startMonthNum] = parseMonthYear(months[0]);\n      setStartMonth(startMonthNum);\n      setStartYear(startYearMonth);\n    }\n  };\n\n  // Get chart data filtered by selected strategy\n  const getChartData = () => {\n    if (!strategyData || !selectedAttribute || !startMonth || !startYear || !endMonth || !endYear) {\n      return { labels: [], datasets: [] };\n    }\n\n    // Filter data by date range\n    const filteredData = {};\n    Object.keys(strategyData).forEach(monthYear => {\n      const [year, month] = parseMonthYear(monthYear);\n      const yearNum = parseInt(year);\n      const monthNum = parseInt(month);\n      const startYearNum = parseInt(startYear);\n      const startMonthNum = parseInt(startMonth);\n      const endYearNum = parseInt(endYear);\n      const endMonthNum = parseInt(endMonth);\n\n      const isInRange = (yearNum > startYearNum || (yearNum === startYearNum && monthNum >= startMonthNum)) &&\n                       (yearNum < endYearNum || (yearNum === endYearNum && monthNum <= endMonthNum));\n\n      if (isInRange) {\n        // Filter by selected strategy\n        const monthData = strategyData[monthYear];\n        if (currentStrategy === \"Alle (Durchschnitt)\") {\n          filteredData[monthYear] = monthData;\n        } else if (monthData[currentStrategy]) {\n          filteredData[monthYear] = { [currentStrategy]: monthData[currentStrategy] };\n        }\n      }\n    });\n\n    // Sort months chronologically\n    const sortedMonths = Object.keys(filteredData).sort((a, b) => {\n      const [yearA, monthA] = parseMonthYear(a);\n      const [yearB, monthB] = parseMonthYear(b);\n      return new Date(yearA, monthA - 1) - new Date(yearB, monthB - 1);\n    });\n\n    // Create labels (formatted month names)\n    const labels = sortedMonths.map(monthYear => {\n      const [year, month] = parseMonthYear(monthYear);\n      const date = new Date(year, month - 1);\n      return date.toLocaleDateString('en-US', { month: 'short', year: 'numeric' });\n    });\n\n    // Calculate coverage for each month\n    const coverage = sortedMonths.map(monthYear => {\n      let totalCoverage = 0;\n      let strategyCount = 0;\n\n      Object.values(filteredData[monthYear]).forEach(strategy => {\n        if (strategy[selectedAttribute]) {\n          const monthTotal = Object.values(strategy[selectedAttribute]).reduce((sum, value) => sum + value, 0);\n          totalCoverage += monthTotal;\n          strategyCount++;\n        }\n      });\n\n      return strategyCount > 0 ? totalCoverage / strategyCount : 0;\n    });\n\n    // Get all unique currencies/subcategories for the selected attribute\n    const allSubcategories = new Set();\n    sortedMonths.forEach(monthYear => {\n      Object.values(filteredData[monthYear]).forEach(strategy => {\n        if (strategy[selectedAttribute]) {\n          Object.keys(strategy[selectedAttribute]).forEach(subcat => {\n            allSubcategories.add(subcat);\n          });\n        }\n      });\n    });\n\n    // Generate colors for each line\n    const colors = [\n      'rgb(255, 99, 132)',\n      'rgb(54, 162, 235)',\n      'rgb(255, 205, 86)',\n      'rgb(75, 192, 192)',\n      'rgb(153, 102, 255)',\n      'rgb(255, 159, 64)',\n      'rgb(199, 199, 199)',\n      'rgb(83, 102, 147)',\n    ];\n\n    // Create datasets for each subcategory\n    const datasets = Array.from(allSubcategories).map((subcategory, index) => {\n      const data = sortedMonths.map(monthYear => {\n        // Aggregate data across selected strategies for this month and subcategory\n        let total = 0;\n        let count = 0;\n        Object.values(filteredData[monthYear]).forEach(strategy => {\n          if (strategy[selectedAttribute] && strategy[selectedAttribute][subcategory] !== undefined) {\n            total += strategy[selectedAttribute][subcategory];\n            count++;\n          }\n        });\n        return count > 0 ? total / count : 0; // Average across strategies\n      });\n\n      return {\n        label: subcategory,\n        data: data,\n        borderColor: colors[index % colors.length],\n        backgroundColor: colors[index % colors.length], // + '20' to make them a dark shade\n        tension: 0.1,\n      };\n    });\n\n    return { labels, datasets, coverage };\n  };\n\n  // Custom plugin to display coverage at the top\n  const coveragePlugin = {\n    id: 'coverageDisplay',\n    afterDraw: (chart) => {\n      const { ctx, chartArea, scales } = chart;\n      const chartData = getChartData();\n\n      if (!chartData.coverage || chartData.coverage.length === 0) return;\n\n      ctx.save();\n      ctx.font = '12px Arial';\n      ctx.textAlign = 'center';\n\n      chartData.coverage.forEach((coverage, index) => {\n        // Determine color based on coverage percentage\n        let color = '#000000'; // Black for >= 90%\n        if (coverage < 80) {\n          color = '#dc3545'; // Red for < 80%\n        } else if (coverage < 90) {\n          color = '#ffc107'; // Yellow for < 90%\n        }\n\n        ctx.fillStyle = color;\n\n        // Calculate x position for each label\n        const xScale = scales.x;\n        const xPos = xScale.getPixelForTick(index);\n        const yPos = chartArea.top - 20;\n\n        // Draw coverage percentage\n        ctx.fillText(`${coverage.toFixed(1)}%`, xPos, yPos);\n      });\n\n      ctx.restore();\n    }\n  };\n\n  const chartOptions = {\n    responsive: true,\n    plugins: {\n      legend: {\n        position: 'bottom',\n        labels: {\n          usePointStyle: true,\n          pointStyle: 'circle',\n          boxWidth: 12,\n          boxHeight: 8,\n        }\n      },\n      title: {\n        display: true,\n        text: `${selectedAttribute} Distribution Over Time`,\n      },\n      coverageDisplay: true,\n    },\n    layout: {\n      padding: {\n        top: 150, // Increased from 30 to 50 for more space\n      }\n    },\n    scales: {\n      y: {\n        beginAtZero: true,\n        ticks: {\n          callback: function(value) {\n            return value + '%';\n          }\n        },\n        title: {\n          display: true,\n          text: 'Percentage (%)'\n        }\n      },\n      x: {\n        title: {\n          display: true,\n          text: 'Month'\n        }\n      }\n    },\n    elements: {\n      point: {\n        radius: 4\n      }\n    }\n  };\n\n  const availableAttributes = getAvailableAttributes();\n  const { months, years } = getAvailableMonthsYears();\n  const chartData = getChartData();\n\n  return (\n    <div>\n      \n      <div className=\"content-page\">\n        <div className='home-arrow-container'>\n          <Link to=\"/\" className=\"back-to-home-arrow\">\n            <i className='bi bi-arrow-left'></i> Home\n          </Link>\n        </div>\n        <StrategyDropdown\n          currentStrategy={currentStrategy}\n          onStrategyChange={setCurrentStrategy}\n        />\n        <p className=\"section_title\">Strategy Insights</p>\n        <hr></hr>\n        {loading ? (\n          <div className=\"text-center\">\n            <div className=\"spinner-border\" role=\"status\">\n              <span className=\"visually-hidden\">Loading...</span>\n            </div>\n          </div>\n        ) : (\n          <div className=\"strategy-content\">\n            {strategyData && (\n              <div className=\"chart-container\">\n                {/* Chart Controls */}\n                <div className=\"chart-controls\">\n                  {/* Attribute Dropdown */}\n                  <div className=\"control-group attribute-dropdown\">\n                    <label className=\"control-label\">Attribute</label>\n                    <select\n                      className=\"form-select\"\n                      value={selectedAttribute}\n                      onChange={(e) => setSelectedAttribute(e.target.value)}\n                    >\n                      <option value=\"\">Select Attribute</option>\n                      {availableAttributes.map(attr => (\n                        <option key={attr} value={attr}>{attr}</option>\n                      ))}\n                    </select>\n                  </div>\n\n                  {/* Date Controls */}\n                  <div className=\"date-controls\">\n                    {/* Start Date */}\n                    <div className=\"date-group\">\n                      <label className=\"control-label\">Start Date</label>\n                      <div className=\"start-date-container\">\n                        <select\n                          value={startMonth}\n                          onChange={(e) => setStartMonth(e.target.value)}\n                        >\n                          <option value=\"\">Month</option>\n                          {months.map(month => (\n                            <option key={month} value={month}>\n                              {new Date(2000, month - 1).toLocaleDateString('en-US', { month: 'short' })}\n                            </option>\n                          ))}\n                        </select>\n                        <select\n                          value={startYear}\n                          onChange={(e) => setStartYear(e.target.value)}\n                        >\n                          <option value=\"\">Year</option>\n                          {years.map(year => (\n                            <option key={year} value={year}>{year}</option>\n                          ))}\n                        </select>\n                        <button\n                          className=\"start-button\"\n                          onClick={setToStart}\n                        >\n                          Start\n                        </button>\n                      </div>\n                    </div>\n\n                    {/* End Date */}\n                    <div className=\"date-group\">\n                      <label className=\"control-label\">End Date</label>\n                      <div className=\"end-date-container\">\n                        <select\n                          value={endMonth}\n                          onChange={(e) => setEndMonth(e.target.value)}\n                        >\n                          <option value=\"\">Month</option>\n                          {months.map(month => (\n                            <option key={month} value={month}>\n                              {new Date(2000, month - 1).toLocaleDateString('en-US', { month: 'short' })}\n                            </option>\n                          ))}\n                        </select>\n                        <select\n                          value={endYear}\n                          onChange={(e) => setEndYear(e.target.value)}\n                        >\n                          <option value=\"\">Year</option>\n                          {years.map(year => (\n                            <option key={year} value={year}>{year}</option>\n                          ))}\n                        </select>\n                        <button\n                          className=\"now-button\"\n                          onClick={setToNow}\n                        >\n                          Now\n                        </button>\n                      </div>\n                    </div>\n                  </div>\n                </div>\n\n                {/* Chart */}\n                {selectedAttribute && chartData.datasets.length > 0 ? (\n                  <div className=\"chart-wrapper\" style={{ height: '400px' }}>\n                    <Line data={chartData} options={chartOptions} plugins={[coveragePlugin]} />\n                  </div>\n                ) : (\n                  <div className=\"alert alert-info\">\n                    Please select an attribute and ensure data is available for the selected date range.\n                  </div>\n                )}\n              </div>\n            )}\n          </div>\n        )}\n      </div>\n    </div>\n  );\n};\n\nexport default Strategy;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,IAAI,EAAEC,SAAS,QAAQ,kBAAkB;AAClD,OAAOC,KAAK,MAAM,OAAO;AACzB,OAAOC,gBAAgB,MAAM,oBAAoB;AACjD,SACEC,KAAK,IAAIC,OAAO,EAChBC,aAAa,EACbC,WAAW,EACXC,YAAY,EACZC,WAAW,EACXC,KAAK,EACLC,OAAO,EACPC,MAAM,EACNC,QAAQ,QACH,UAAU;AACjB,SAASC,IAAI,QAAQ,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEvCX,OAAO,CAACY,QAAQ,CACdX,aAAa,EACbC,WAAW,EACXC,YAAY,EACZC,WAAW,EACXC,KAAK,EACLC,OAAO,EACPC,MACF,CAAC;AAED,MAAMM,QAAQ,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACrB,MAAM;IAAEC;EAAa,CAAC,GAAGnB,SAAS,CAAC,CAAC;EACpC,MAAM,CAACoB,eAAe,EAAEC,kBAAkB,CAAC,GAAGxB,QAAQ,CAACsB,YAAY,IAAI,qBAAqB,CAAC;EAC7F,MAAM,CAACG,YAAY,EAAEC,eAAe,CAAC,GAAG1B,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAAC2B,OAAO,EAAEC,UAAU,CAAC,GAAG5B,QAAQ,CAAC,IAAI,CAAC;;EAE5C;EACA,MAAM,CAAC6B,iBAAiB,EAAEC,oBAAoB,CAAC,GAAG9B,QAAQ,CAAC,EAAE,CAAC;EAC9D,MAAM,CAAC+B,UAAU,EAAEC,aAAa,CAAC,GAAGhC,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACiC,SAAS,EAAEC,YAAY,CAAC,GAAGlC,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAACmC,QAAQ,EAAEC,WAAW,CAAC,GAAGpC,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACqC,OAAO,EAAEC,UAAU,CAAC,GAAGtC,QAAQ,CAAC,EAAE,CAAC;EAE1CC,SAAS,CAAC,MAAM;IACdsC,iBAAiB,CAAChB,eAAe,CAAC;EACpC,CAAC,EAAE,CAACA,eAAe,CAAC,CAAC;EAErB,MAAMgB,iBAAiB,GAAG,MAAOC,QAAQ,IAAK;IAC5C,IAAI;MACFZ,UAAU,CAAC,IAAI,CAAC;MAChB,MAAMa,QAAQ,GAAG,MAAMrC,KAAK,CAACsC,GAAG,CAAC,iBAAiBF,QAAQ,GAAG,CAAC;MAC9Dd,eAAe,CAACe,QAAQ,CAACE,IAAI,CAAC;;MAE9B;MACA,IAAIF,QAAQ,CAACE,IAAI,IAAIC,MAAM,CAACC,IAAI,CAACJ,QAAQ,CAACE,IAAI,CAAC,CAACG,MAAM,GAAG,CAAC,EAAE;QAC1D,MAAMC,UAAU,GAAGH,MAAM,CAACC,IAAI,CAACJ,QAAQ,CAACE,IAAI,CAAC,CAAC,CAAC,CAAC;QAChD,MAAMK,aAAa,GAAGJ,MAAM,CAACC,IAAI,CAACJ,QAAQ,CAACE,IAAI,CAACI,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC;QAC/D,MAAME,UAAU,GAAGL,MAAM,CAACC,IAAI,CAACJ,QAAQ,CAACE,IAAI,CAACI,UAAU,CAAC,CAACC,aAAa,CAAC,CAAC;QAExE,IAAIC,UAAU,CAACH,MAAM,GAAG,CAAC,IAAI,CAACjB,iBAAiB,EAAE;UAC/CC,oBAAoB,CAACmB,UAAU,CAAC,CAAC,CAAC,CAAC;QACrC;;QAEA;QACA,MAAMC,MAAM,GAAGN,MAAM,CAACC,IAAI,CAACJ,QAAQ,CAACE,IAAI,CAAC,CAACQ,IAAI,CAAC,CAAC;QAChD,IAAID,MAAM,CAACJ,MAAM,GAAG,CAAC,EAAE;UACrB,MAAM,CAACM,cAAc,EAAEC,aAAa,CAAC,GAAGC,cAAc,CAACJ,MAAM,CAAC,CAAC,CAAC,CAAC;UACjE,MAAM,CAACK,YAAY,EAAEC,WAAW,CAAC,GAAGF,cAAc,CAACJ,MAAM,CAACA,MAAM,CAACJ,MAAM,GAAG,CAAC,CAAC,CAAC;UAE7E,IAAI,CAACf,UAAU,EAAE;YACfC,aAAa,CAACqB,aAAa,CAAC;YAC5BnB,YAAY,CAACkB,cAAc,CAAC;UAC9B;UACA,IAAI,CAACjB,QAAQ,EAAE;YACbC,WAAW,CAACoB,WAAW,CAAC;YACxBlB,UAAU,CAACiB,YAAY,CAAC;UAC1B;QACF;MACF;IACF,CAAC,CAAC,OAAOE,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;IACvD,CAAC,SAAS;MACR7B,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAM0B,cAAc,GAAIK,YAAY,IAAK;IACvC,MAAM,CAACC,IAAI,EAAEC,KAAK,CAAC,GAAGF,YAAY,CAACG,KAAK,CAAC,KAAK,CAAC;IAC/C,OAAO,CAACF,IAAI,EAAEC,KAAK,CAAC;EACtB,CAAC;;EAED;EACA;EACA;EACA;;EAEA;EACA,MAAME,sBAAsB,GAAGA,CAAA,KAAM;IACnC,IAAI,CAACtC,YAAY,IAAImB,MAAM,CAACC,IAAI,CAACpB,YAAY,CAAC,CAACqB,MAAM,KAAK,CAAC,EAAE,OAAO,EAAE;IAEtE,MAAMC,UAAU,GAAGH,MAAM,CAACC,IAAI,CAACpB,YAAY,CAAC,CAAC,CAAC,CAAC;IAC/C,MAAMuB,aAAa,GAAGJ,MAAM,CAACC,IAAI,CAACpB,YAAY,CAACsB,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC;IAC9D,OAAOH,MAAM,CAACC,IAAI,CAACpB,YAAY,CAACsB,UAAU,CAAC,CAACC,aAAa,CAAC,CAAC;EAC7D,CAAC;;EAED;EACA,MAAMgB,uBAAuB,GAAGA,CAAA,KAAM;IACpC,IAAI,CAACvC,YAAY,EAAE,OAAO;MAAEyB,MAAM,EAAE,EAAE;MAAEe,KAAK,EAAE;IAAG,CAAC;IAEnD,MAAMC,WAAW,GAAGtB,MAAM,CAACC,IAAI,CAACpB,YAAY,CAAC,CAAC0C,GAAG,CAACb,cAAc,CAAC;IACjE,MAAMJ,MAAM,GAAG,CAAC,GAAG,IAAIkB,GAAG,CAACF,WAAW,CAACC,GAAG,CAAC,CAAC,CAACP,IAAI,EAAEC,KAAK,CAAC,KAAKA,KAAK,CAAC,CAAC,CAAC,CAACV,IAAI,CAAC,CAAC;IAC7E,MAAMc,KAAK,GAAG,CAAC,GAAG,IAAIG,GAAG,CAACF,WAAW,CAACC,GAAG,CAAC,CAAC,CAACP,IAAI,EAAEC,KAAK,CAAC,KAAKD,IAAI,CAAC,CAAC,CAAC,CAACT,IAAI,CAAC,CAAC;IAE3E,OAAO;MAAED,MAAM;MAAEe;IAAM,CAAC;EAC1B,CAAC;;EAED;EACA,MAAMI,QAAQ,GAAGA,CAAA,KAAM;IACrB,MAAMC,GAAG,GAAG,IAAIC,IAAI,CAAC,CAAC;IACtB,MAAMC,YAAY,GAAG,CAACF,GAAG,CAACG,QAAQ,CAAC,CAAC,GAAG,CAAC,EAAEC,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;IACrE,MAAMC,WAAW,GAAGN,GAAG,CAACO,WAAW,CAAC,CAAC,CAACH,QAAQ,CAAC,CAAC;IAEhDtC,WAAW,CAACoC,YAAY,CAAC;IACzBlC,UAAU,CAACsC,WAAW,CAAC;EACzB,CAAC;;EAED;EACA,MAAME,UAAU,GAAGA,CAAA,KAAM;IACvB,IAAI,CAACrD,YAAY,IAAImB,MAAM,CAACC,IAAI,CAACpB,YAAY,CAAC,CAACqB,MAAM,KAAK,CAAC,EAAE;IAE7D,MAAMI,MAAM,GAAGN,MAAM,CAACC,IAAI,CAACpB,YAAY,CAAC,CAAC0B,IAAI,CAAC,CAAC;IAC/C,IAAID,MAAM,CAACJ,MAAM,GAAG,CAAC,EAAE;MACrB,MAAM,CAACM,cAAc,EAAEC,aAAa,CAAC,GAAGC,cAAc,CAACJ,MAAM,CAAC,CAAC,CAAC,CAAC;MACjElB,aAAa,CAACqB,aAAa,CAAC;MAC5BnB,YAAY,CAACkB,cAAc,CAAC;IAC9B;EACF,CAAC;;EAED;EACA,MAAM2B,YAAY,GAAGA,CAAA,KAAM;IACzB,IAAI,CAACtD,YAAY,IAAI,CAACI,iBAAiB,IAAI,CAACE,UAAU,IAAI,CAACE,SAAS,IAAI,CAACE,QAAQ,IAAI,CAACE,OAAO,EAAE;MAC7F,OAAO;QAAE2C,MAAM,EAAE,EAAE;QAAEC,QAAQ,EAAE;MAAG,CAAC;IACrC;;IAEA;IACA,MAAMC,YAAY,GAAG,CAAC,CAAC;IACvBtC,MAAM,CAACC,IAAI,CAACpB,YAAY,CAAC,CAAC0D,OAAO,CAACC,SAAS,IAAI;MAC7C,MAAM,CAACxB,IAAI,EAAEC,KAAK,CAAC,GAAGP,cAAc,CAAC8B,SAAS,CAAC;MAC/C,MAAMC,OAAO,GAAGC,QAAQ,CAAC1B,IAAI,CAAC;MAC9B,MAAM2B,QAAQ,GAAGD,QAAQ,CAACzB,KAAK,CAAC;MAChC,MAAM2B,YAAY,GAAGF,QAAQ,CAACrD,SAAS,CAAC;MACxC,MAAMoB,aAAa,GAAGiC,QAAQ,CAACvD,UAAU,CAAC;MAC1C,MAAM0D,UAAU,GAAGH,QAAQ,CAACjD,OAAO,CAAC;MACpC,MAAMmB,WAAW,GAAG8B,QAAQ,CAACnD,QAAQ,CAAC;MAEtC,MAAMuD,SAAS,GAAG,CAACL,OAAO,GAAGG,YAAY,IAAKH,OAAO,KAAKG,YAAY,IAAID,QAAQ,IAAIlC,aAAc,MAClFgC,OAAO,GAAGI,UAAU,IAAKJ,OAAO,KAAKI,UAAU,IAAIF,QAAQ,IAAI/B,WAAY,CAAC;MAE9F,IAAIkC,SAAS,EAAE;QACb;QACA,MAAMC,SAAS,GAAGlE,YAAY,CAAC2D,SAAS,CAAC;QACzC,IAAI7D,eAAe,KAAK,qBAAqB,EAAE;UAC7C2D,YAAY,CAACE,SAAS,CAAC,GAAGO,SAAS;QACrC,CAAC,MAAM,IAAIA,SAAS,CAACpE,eAAe,CAAC,EAAE;UACrC2D,YAAY,CAACE,SAAS,CAAC,GAAG;YAAE,CAAC7D,eAAe,GAAGoE,SAAS,CAACpE,eAAe;UAAE,CAAC;QAC7E;MACF;IACF,CAAC,CAAC;;IAEF;IACA,MAAMqE,YAAY,GAAGhD,MAAM,CAACC,IAAI,CAACqC,YAAY,CAAC,CAAC/B,IAAI,CAAC,CAAC0C,CAAC,EAAEC,CAAC,KAAK;MAC5D,MAAM,CAACC,KAAK,EAAEC,MAAM,CAAC,GAAG1C,cAAc,CAACuC,CAAC,CAAC;MACzC,MAAM,CAACI,KAAK,EAAEC,MAAM,CAAC,GAAG5C,cAAc,CAACwC,CAAC,CAAC;MACzC,OAAO,IAAIvB,IAAI,CAACwB,KAAK,EAAEC,MAAM,GAAG,CAAC,CAAC,GAAG,IAAIzB,IAAI,CAAC0B,KAAK,EAAEC,MAAM,GAAG,CAAC,CAAC;IAClE,CAAC,CAAC;;IAEF;IACA,MAAMlB,MAAM,GAAGY,YAAY,CAACzB,GAAG,CAACiB,SAAS,IAAI;MAC3C,MAAM,CAACxB,IAAI,EAAEC,KAAK,CAAC,GAAGP,cAAc,CAAC8B,SAAS,CAAC;MAC/C,MAAMe,IAAI,GAAG,IAAI5B,IAAI,CAACX,IAAI,EAAEC,KAAK,GAAG,CAAC,CAAC;MACtC,OAAOsC,IAAI,CAACC,kBAAkB,CAAC,OAAO,EAAE;QAAEvC,KAAK,EAAE,OAAO;QAAED,IAAI,EAAE;MAAU,CAAC,CAAC;IAC9E,CAAC,CAAC;;IAEF;IACA,MAAMyC,QAAQ,GAAGT,YAAY,CAACzB,GAAG,CAACiB,SAAS,IAAI;MAC7C,IAAIkB,aAAa,GAAG,CAAC;MACrB,IAAIC,aAAa,GAAG,CAAC;MAErB3D,MAAM,CAAC4D,MAAM,CAACtB,YAAY,CAACE,SAAS,CAAC,CAAC,CAACD,OAAO,CAAC3C,QAAQ,IAAI;QACzD,IAAIA,QAAQ,CAACX,iBAAiB,CAAC,EAAE;UAC/B,MAAM4E,UAAU,GAAG7D,MAAM,CAAC4D,MAAM,CAAChE,QAAQ,CAACX,iBAAiB,CAAC,CAAC,CAAC6E,MAAM,CAAC,CAACC,GAAG,EAAEC,KAAK,KAAKD,GAAG,GAAGC,KAAK,EAAE,CAAC,CAAC;UACpGN,aAAa,IAAIG,UAAU;UAC3BF,aAAa,EAAE;QACjB;MACF,CAAC,CAAC;MAEF,OAAOA,aAAa,GAAG,CAAC,GAAGD,aAAa,GAAGC,aAAa,GAAG,CAAC;IAC9D,CAAC,CAAC;;IAEF;IACA,MAAMM,gBAAgB,GAAG,IAAIzC,GAAG,CAAC,CAAC;IAClCwB,YAAY,CAACT,OAAO,CAACC,SAAS,IAAI;MAChCxC,MAAM,CAAC4D,MAAM,CAACtB,YAAY,CAACE,SAAS,CAAC,CAAC,CAACD,OAAO,CAAC3C,QAAQ,IAAI;QACzD,IAAIA,QAAQ,CAACX,iBAAiB,CAAC,EAAE;UAC/Be,MAAM,CAACC,IAAI,CAACL,QAAQ,CAACX,iBAAiB,CAAC,CAAC,CAACsD,OAAO,CAAC2B,MAAM,IAAI;YACzDD,gBAAgB,CAACE,GAAG,CAACD,MAAM,CAAC;UAC9B,CAAC,CAAC;QACJ;MACF,CAAC,CAAC;IACJ,CAAC,CAAC;;IAEF;IACA,MAAME,MAAM,GAAG,CACb,mBAAmB,EACnB,mBAAmB,EACnB,mBAAmB,EACnB,mBAAmB,EACnB,oBAAoB,EACpB,mBAAmB,EACnB,oBAAoB,EACpB,mBAAmB,CACpB;;IAED;IACA,MAAM/B,QAAQ,GAAGgC,KAAK,CAACC,IAAI,CAACL,gBAAgB,CAAC,CAAC1C,GAAG,CAAC,CAACgD,WAAW,EAAEC,KAAK,KAAK;MACxE,MAAMzE,IAAI,GAAGiD,YAAY,CAACzB,GAAG,CAACiB,SAAS,IAAI;QACzC;QACA,IAAIiC,KAAK,GAAG,CAAC;QACb,IAAIC,KAAK,GAAG,CAAC;QACb1E,MAAM,CAAC4D,MAAM,CAACtB,YAAY,CAACE,SAAS,CAAC,CAAC,CAACD,OAAO,CAAC3C,QAAQ,IAAI;UACzD,IAAIA,QAAQ,CAACX,iBAAiB,CAAC,IAAIW,QAAQ,CAACX,iBAAiB,CAAC,CAACsF,WAAW,CAAC,KAAKI,SAAS,EAAE;YACzFF,KAAK,IAAI7E,QAAQ,CAACX,iBAAiB,CAAC,CAACsF,WAAW,CAAC;YACjDG,KAAK,EAAE;UACT;QACF,CAAC,CAAC;QACF,OAAOA,KAAK,GAAG,CAAC,GAAGD,KAAK,GAAGC,KAAK,GAAG,CAAC,CAAC,CAAC;MACxC,CAAC,CAAC;MAEF,OAAO;QACLE,KAAK,EAAEL,WAAW;QAClBxE,IAAI,EAAEA,IAAI;QACV8E,WAAW,EAAET,MAAM,CAACI,KAAK,GAAGJ,MAAM,CAAClE,MAAM,CAAC;QAC1C4E,eAAe,EAAEV,MAAM,CAACI,KAAK,GAAGJ,MAAM,CAAClE,MAAM,CAAC;QAAE;QAChD6E,OAAO,EAAE;MACX,CAAC;IACH,CAAC,CAAC;IAEF,OAAO;MAAE3C,MAAM;MAAEC,QAAQ;MAAEoB;IAAS,CAAC;EACvC,CAAC;;EAED;EACA,MAAMuB,cAAc,GAAG;IACrBC,EAAE,EAAE,iBAAiB;IACrBC,SAAS,EAAGC,KAAK,IAAK;MACpB,MAAM;QAAEC,GAAG;QAAEC,SAAS;QAAEC;MAAO,CAAC,GAAGH,KAAK;MACxC,MAAMI,SAAS,GAAGpD,YAAY,CAAC,CAAC;MAEhC,IAAI,CAACoD,SAAS,CAAC9B,QAAQ,IAAI8B,SAAS,CAAC9B,QAAQ,CAACvD,MAAM,KAAK,CAAC,EAAE;MAE5DkF,GAAG,CAACI,IAAI,CAAC,CAAC;MACVJ,GAAG,CAACK,IAAI,GAAG,YAAY;MACvBL,GAAG,CAACM,SAAS,GAAG,QAAQ;MAExBH,SAAS,CAAC9B,QAAQ,CAAClB,OAAO,CAAC,CAACkB,QAAQ,EAAEe,KAAK,KAAK;QAC9C;QACA,IAAImB,KAAK,GAAG,SAAS,CAAC,CAAC;QACvB,IAAIlC,QAAQ,GAAG,EAAE,EAAE;UACjBkC,KAAK,GAAG,SAAS,CAAC,CAAC;QACrB,CAAC,MAAM,IAAIlC,QAAQ,GAAG,EAAE,EAAE;UACxBkC,KAAK,GAAG,SAAS,CAAC,CAAC;QACrB;QAEAP,GAAG,CAACQ,SAAS,GAAGD,KAAK;;QAErB;QACA,MAAME,MAAM,GAAGP,MAAM,CAACQ,CAAC;QACvB,MAAMC,IAAI,GAAGF,MAAM,CAACG,eAAe,CAACxB,KAAK,CAAC;QAC1C,MAAMyB,IAAI,GAAGZ,SAAS,CAACa,GAAG,GAAG,EAAE;;QAE/B;QACAd,GAAG,CAACe,QAAQ,CAAC,GAAG1C,QAAQ,CAAC2C,OAAO,CAAC,CAAC,CAAC,GAAG,EAAEL,IAAI,EAAEE,IAAI,CAAC;MACrD,CAAC,CAAC;MAEFb,GAAG,CAACiB,OAAO,CAAC,CAAC;IACf;EACF,CAAC;EAED,MAAMC,YAAY,GAAG;IACnBC,UAAU,EAAE,IAAI;IAChBC,OAAO,EAAE;MACPC,MAAM,EAAE;QACNC,QAAQ,EAAE,QAAQ;QAClBtE,MAAM,EAAE;UACNuE,aAAa,EAAE,IAAI;UACnBC,UAAU,EAAE,QAAQ;UACpBC,QAAQ,EAAE,EAAE;UACZC,SAAS,EAAE;QACb;MACF,CAAC;MACDC,KAAK,EAAE;QACLC,OAAO,EAAE,IAAI;QACbC,IAAI,EAAE,GAAGhI,iBAAiB;MAC5B,CAAC;MACDiI,eAAe,EAAE;IACnB,CAAC;IACDC,MAAM,EAAE;MACNC,OAAO,EAAE;QACPlB,GAAG,EAAE,GAAG,CAAE;MACZ;IACF,CAAC;IACDZ,MAAM,EAAE;MACN+B,CAAC,EAAE;QACDC,WAAW,EAAE,IAAI;QACjBC,KAAK,EAAE;UACLC,QAAQ,EAAE,SAAAA,CAASxD,KAAK,EAAE;YACxB,OAAOA,KAAK,GAAG,GAAG;UACpB;QACF,CAAC;QACD+C,KAAK,EAAE;UACLC,OAAO,EAAE,IAAI;UACbC,IAAI,EAAE;QACR;MACF,CAAC;MACDnB,CAAC,EAAE;QACDiB,KAAK,EAAE;UACLC,OAAO,EAAE,IAAI;UACbC,IAAI,EAAE;QACR;MACF;IACF,CAAC;IACD9I,QAAQ,EAAE;MACRsJ,KAAK,EAAE;QACLC,MAAM,EAAE;MACV;IACF;EACF,CAAC;EAED,MAAMC,mBAAmB,GAAGxG,sBAAsB,CAAC,CAAC;EACpD,MAAM;IAAEb,MAAM;IAAEe;EAAM,CAAC,GAAGD,uBAAuB,CAAC,CAAC;EACnD,MAAMmE,SAAS,GAAGpD,YAAY,CAAC,CAAC;EAEhC,oBACE7D,OAAA;IAAAsJ,QAAA,eAEEtJ,OAAA;MAAKuJ,SAAS,EAAC,cAAc;MAAAD,QAAA,gBAC3BtJ,OAAA;QAAKuJ,SAAS,EAAC,sBAAsB;QAAAD,QAAA,eACnCtJ,OAAA,CAAChB,IAAI;UAACwK,EAAE,EAAC,GAAG;UAACD,SAAS,EAAC,oBAAoB;UAAAD,QAAA,gBACzCtJ,OAAA;YAAGuJ,SAAS,EAAC;UAAkB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,SACtC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACN5J,OAAA,CAACb,gBAAgB;QACfkB,eAAe,EAAEA,eAAgB;QACjCwJ,gBAAgB,EAAEvJ;MAAmB;QAAAmJ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACtC,CAAC,eACF5J,OAAA;QAAGuJ,SAAS,EAAC,eAAe;QAAAD,QAAA,EAAC;MAAiB;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC,eAClD5J,OAAA;QAAAyJ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,EACRnJ,OAAO,gBACNT,OAAA;QAAKuJ,SAAS,EAAC,aAAa;QAAAD,QAAA,eAC1BtJ,OAAA;UAAKuJ,SAAS,EAAC,gBAAgB;UAACO,IAAI,EAAC,QAAQ;UAAAR,QAAA,eAC3CtJ,OAAA;YAAMuJ,SAAS,EAAC,iBAAiB;YAAAD,QAAA,EAAC;UAAU;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChD;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,gBAEN5J,OAAA;QAAKuJ,SAAS,EAAC,kBAAkB;QAAAD,QAAA,EAC9B/I,YAAY,iBACXP,OAAA;UAAKuJ,SAAS,EAAC,iBAAiB;UAAAD,QAAA,gBAE9BtJ,OAAA;YAAKuJ,SAAS,EAAC,gBAAgB;YAAAD,QAAA,gBAE7BtJ,OAAA;cAAKuJ,SAAS,EAAC,kCAAkC;cAAAD,QAAA,gBAC/CtJ,OAAA;gBAAOuJ,SAAS,EAAC,eAAe;gBAAAD,QAAA,EAAC;cAAS;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAClD5J,OAAA;gBACEuJ,SAAS,EAAC,aAAa;gBACvB7D,KAAK,EAAE/E,iBAAkB;gBACzBoJ,QAAQ,EAAGC,CAAC,IAAKpJ,oBAAoB,CAACoJ,CAAC,CAACC,MAAM,CAACvE,KAAK,CAAE;gBAAA4D,QAAA,gBAEtDtJ,OAAA;kBAAQ0F,KAAK,EAAC,EAAE;kBAAA4D,QAAA,EAAC;gBAAgB;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,EACzCP,mBAAmB,CAACpG,GAAG,CAACiH,IAAI,iBAC3BlK,OAAA;kBAAmB0F,KAAK,EAAEwE,IAAK;kBAAAZ,QAAA,EAAEY;gBAAI,GAAxBA,IAAI;kBAAAT,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAA6B,CAC/C,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,eAGN5J,OAAA;cAAKuJ,SAAS,EAAC,eAAe;cAAAD,QAAA,gBAE5BtJ,OAAA;gBAAKuJ,SAAS,EAAC,YAAY;gBAAAD,QAAA,gBACzBtJ,OAAA;kBAAOuJ,SAAS,EAAC,eAAe;kBAAAD,QAAA,EAAC;gBAAU;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACnD5J,OAAA;kBAAKuJ,SAAS,EAAC,sBAAsB;kBAAAD,QAAA,gBACnCtJ,OAAA;oBACE0F,KAAK,EAAE7E,UAAW;oBAClBkJ,QAAQ,EAAGC,CAAC,IAAKlJ,aAAa,CAACkJ,CAAC,CAACC,MAAM,CAACvE,KAAK,CAAE;oBAAA4D,QAAA,gBAE/CtJ,OAAA;sBAAQ0F,KAAK,EAAC,EAAE;sBAAA4D,QAAA,EAAC;oBAAK;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,EAC9B5H,MAAM,CAACiB,GAAG,CAACN,KAAK,iBACf3C,OAAA;sBAAoB0F,KAAK,EAAE/C,KAAM;sBAAA2G,QAAA,EAC9B,IAAIjG,IAAI,CAAC,IAAI,EAAEV,KAAK,GAAG,CAAC,CAAC,CAACuC,kBAAkB,CAAC,OAAO,EAAE;wBAAEvC,KAAK,EAAE;sBAAQ,CAAC;oBAAC,GAD/DA,KAAK;sBAAA8G,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAEV,CACT,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACI,CAAC,eACT5J,OAAA;oBACE0F,KAAK,EAAE3E,SAAU;oBACjBgJ,QAAQ,EAAGC,CAAC,IAAKhJ,YAAY,CAACgJ,CAAC,CAACC,MAAM,CAACvE,KAAK,CAAE;oBAAA4D,QAAA,gBAE9CtJ,OAAA;sBAAQ0F,KAAK,EAAC,EAAE;sBAAA4D,QAAA,EAAC;oBAAI;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,EAC7B7G,KAAK,CAACE,GAAG,CAACP,IAAI,iBACb1C,OAAA;sBAAmB0F,KAAK,EAAEhD,IAAK;sBAAA4G,QAAA,EAAE5G;oBAAI,GAAxBA,IAAI;sBAAA+G,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAA6B,CAC/C,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACI,CAAC,eACT5J,OAAA;oBACEuJ,SAAS,EAAC,cAAc;oBACxBY,OAAO,EAAEvG,UAAW;oBAAA0F,QAAA,EACrB;kBAED;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAGN5J,OAAA;gBAAKuJ,SAAS,EAAC,YAAY;gBAAAD,QAAA,gBACzBtJ,OAAA;kBAAOuJ,SAAS,EAAC,eAAe;kBAAAD,QAAA,EAAC;gBAAQ;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACjD5J,OAAA;kBAAKuJ,SAAS,EAAC,oBAAoB;kBAAAD,QAAA,gBACjCtJ,OAAA;oBACE0F,KAAK,EAAEzE,QAAS;oBAChB8I,QAAQ,EAAGC,CAAC,IAAK9I,WAAW,CAAC8I,CAAC,CAACC,MAAM,CAACvE,KAAK,CAAE;oBAAA4D,QAAA,gBAE7CtJ,OAAA;sBAAQ0F,KAAK,EAAC,EAAE;sBAAA4D,QAAA,EAAC;oBAAK;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,EAC9B5H,MAAM,CAACiB,GAAG,CAACN,KAAK,iBACf3C,OAAA;sBAAoB0F,KAAK,EAAE/C,KAAM;sBAAA2G,QAAA,EAC9B,IAAIjG,IAAI,CAAC,IAAI,EAAEV,KAAK,GAAG,CAAC,CAAC,CAACuC,kBAAkB,CAAC,OAAO,EAAE;wBAAEvC,KAAK,EAAE;sBAAQ,CAAC;oBAAC,GAD/DA,KAAK;sBAAA8G,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAEV,CACT,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACI,CAAC,eACT5J,OAAA;oBACE0F,KAAK,EAAEvE,OAAQ;oBACf4I,QAAQ,EAAGC,CAAC,IAAK5I,UAAU,CAAC4I,CAAC,CAACC,MAAM,CAACvE,KAAK,CAAE;oBAAA4D,QAAA,gBAE5CtJ,OAAA;sBAAQ0F,KAAK,EAAC,EAAE;sBAAA4D,QAAA,EAAC;oBAAI;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,EAC7B7G,KAAK,CAACE,GAAG,CAACP,IAAI,iBACb1C,OAAA;sBAAmB0F,KAAK,EAAEhD,IAAK;sBAAA4G,QAAA,EAAE5G;oBAAI,GAAxBA,IAAI;sBAAA+G,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAA6B,CAC/C,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACI,CAAC,eACT5J,OAAA;oBACEuJ,SAAS,EAAC,YAAY;oBACtBY,OAAO,EAAEhH,QAAS;oBAAAmG,QAAA,EACnB;kBAED;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,EAGLjJ,iBAAiB,IAAIsG,SAAS,CAAClD,QAAQ,CAACnC,MAAM,GAAG,CAAC,gBACjD5B,OAAA;YAAKuJ,SAAS,EAAC,eAAe;YAACa,KAAK,EAAE;cAAEC,MAAM,EAAE;YAAQ,CAAE;YAAAf,QAAA,eACxDtJ,OAAA,CAACF,IAAI;cAAC2B,IAAI,EAAEwF,SAAU;cAACqD,OAAO,EAAEtC,YAAa;cAACE,OAAO,EAAE,CAACxB,cAAc;YAAE;cAAA+C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxE,CAAC,gBAEN5J,OAAA;YAAKuJ,SAAS,EAAC,kBAAkB;YAAAD,QAAA,EAAC;UAElC;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MACN;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACzJ,EAAA,CAzbID,QAAQ;EAAA,QACajB,SAAS;AAAA;AAAAsL,EAAA,GAD9BrK,QAAQ;AA2bd,eAAeA,QAAQ;AAAC,IAAAqK,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}