{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Desktop/Coding/BVS/CrystalBall/insights-app/frontend/src/components/Strategy.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Link, useParams } from 'react-router-dom';\nimport axios from 'axios';\nimport StrategyDropdown from './StrategyDropdown';\nimport { Chart as ChartJS, CategoryScale, LinearScale, PointElement, LineElement, Title, Tooltip, Legend, elements } from 'chart.js';\nimport { Line } from 'react-chartjs-2';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nChartJS.register(CategoryScale, LinearScale, PointElement, LineElement, Title, Tooltip, Legend);\nconst Strategy = () => {\n  _s();\n  const {\n    strategyName\n  } = useParams();\n  const [currentStrategy, setCurrentStrategy] = useState(strategyName || 'Alle (Durchschnitt)');\n  const [strategyData, setStrategyData] = useState(null);\n  const [loading, setLoading] = useState(true);\n\n  // Chart state\n  const [selectedAttribute, setSelectedAttribute] = useState('');\n  const [startMonth, setStartMonth] = useState('');\n  const [startYear, setStartYear] = useState('');\n  const [endMonth, setEndMonth] = useState('');\n  const [endYear, setEndYear] = useState('');\n  useEffect(() => {\n    fetchStrategyData(currentStrategy);\n  }, [currentStrategy]);\n  const fetchStrategyData = async strategy => {\n    try {\n      setLoading(true);\n      const response = await axios.get(`/api/strategy/${strategy}/`);\n      setStrategyData(response.data);\n\n      // Initialize dropdowns with first available data\n      if (response.data && Object.keys(response.data).length > 0) {\n        const firstMonth = Object.keys(response.data)[0];\n        const firstStrategy = Object.keys(response.data[firstMonth])[0];\n        const attributes = Object.keys(response.data[firstMonth][firstStrategy]);\n        if (attributes.length > 0 && !selectedAttribute) {\n          setSelectedAttribute(attributes[0]);\n        }\n\n        // Set default date range\n        const months = Object.keys(response.data).sort();\n        if (months.length > 0) {\n          const [startYearMonth, startMonthNum] = parseMonthYear(months[0]);\n          const [endYearMonth, endMonthNum] = parseMonthYear(months[months.length - 1]);\n          if (!startMonth) {\n            setStartMonth(startMonthNum);\n            setStartYear(startYearMonth);\n          }\n          if (!endMonth) {\n            setEndMonth(endMonthNum);\n            setEndYear(endYearMonth);\n          }\n        }\n      }\n    } catch (error) {\n      console.error('Error fetching strategy data:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Helper function to parse month-year string like \"2025 - 04\"\n  const parseMonthYear = monthYearStr => {\n    const [year, month] = monthYearStr.split(' - ');\n    return [year, month];\n  };\n\n  // Helper function to create month-year string\n  // const createMonthYearStr = (year, month) => {\n  //   return `${year} - ${month.padStart(2, '0')}`;\n  // };\n\n  // Get available attributes from data\n  const getAvailableAttributes = () => {\n    if (!strategyData || Object.keys(strategyData).length === 0) return [];\n    const firstMonth = Object.keys(strategyData)[0];\n    const firstStrategy = Object.keys(strategyData[firstMonth])[0];\n    return Object.keys(strategyData[firstMonth][firstStrategy]);\n  };\n\n  // Get available months and years\n  const getAvailableMonthsYears = () => {\n    if (!strategyData) return {\n      months: [],\n      years: []\n    };\n    const monthsYears = Object.keys(strategyData).map(parseMonthYear);\n    const months = [...new Set(monthsYears.map(([year, month]) => month))].sort();\n    const years = [...new Set(monthsYears.map(([year, month]) => year))].sort();\n    return {\n      months,\n      years\n    };\n  };\n\n  // Set end date to current month/year\n  const setToNow = () => {\n    const now = new Date();\n    const currentMonth = (now.getMonth() + 1).toString().padStart(2, '0');\n    const currentYear = now.getFullYear().toString();\n    setEndMonth(currentMonth);\n    setEndYear(currentYear);\n  };\n\n  // Set start date to first available month/year\n  const setToStart = () => {\n    if (!strategyData || Object.keys(strategyData).length === 0) return;\n    const months = Object.keys(strategyData).sort();\n    if (months.length > 0) {\n      const [startYearMonth, startMonthNum] = parseMonthYear(months[0]);\n      setStartMonth(startMonthNum);\n      setStartYear(startYearMonth);\n    }\n  };\n\n  // Get chart data filtered by selected strategy\n  const getChartData = () => {\n    if (!strategyData || !selectedAttribute || !startMonth || !startYear || !endMonth || !endYear) {\n      return {\n        labels: [],\n        datasets: []\n      };\n    }\n\n    // Filter data by date range\n    const filteredData = {};\n    Object.keys(strategyData).forEach(monthYear => {\n      const [year, month] = parseMonthYear(monthYear);\n      const yearNum = parseInt(year);\n      const monthNum = parseInt(month);\n      const startYearNum = parseInt(startYear);\n      const startMonthNum = parseInt(startMonth);\n      const endYearNum = parseInt(endYear);\n      const endMonthNum = parseInt(endMonth);\n      const isInRange = (yearNum > startYearNum || yearNum === startYearNum && monthNum >= startMonthNum) && (yearNum < endYearNum || yearNum === endYearNum && monthNum <= endMonthNum);\n      if (isInRange) {\n        // Filter by selected strategy\n        const monthData = strategyData[monthYear];\n        if (currentStrategy === \"Alle (Durchschnitt)\") {\n          filteredData[monthYear] = monthData;\n        } else if (monthData[currentStrategy]) {\n          filteredData[monthYear] = {\n            [currentStrategy]: monthData[currentStrategy]\n          };\n        }\n      }\n    });\n\n    // Sort months chronologically\n    const sortedMonths = Object.keys(filteredData).sort((a, b) => {\n      const [yearA, monthA] = parseMonthYear(a);\n      const [yearB, monthB] = parseMonthYear(b);\n      return new Date(yearA, monthA - 1) - new Date(yearB, monthB - 1);\n    });\n\n    // Create labels (formatted month names)\n    const labels = sortedMonths.map(monthYear => {\n      const [year, month] = parseMonthYear(monthYear);\n      const date = new Date(year, month - 1);\n      return date.toLocaleDateString('en-US', {\n        month: 'short',\n        year: 'numeric'\n      });\n    });\n\n    // Calculate coverage for each month\n    const coverage = sortedMonths.map(monthYear => {\n      let totalCoverage = 0;\n      let strategyCount = 0;\n      Object.values(filteredData[monthYear]).forEach(strategy => {\n        if (strategy[selectedAttribute]) {\n          const monthTotal = Object.values(strategy[selectedAttribute]).reduce((sum, value) => sum + value, 0);\n          totalCoverage += monthTotal;\n          strategyCount++;\n        }\n      });\n      return strategyCount > 0 ? totalCoverage / strategyCount : 0;\n    });\n\n    // Get all unique currencies/subcategories for the selected attribute\n    const allSubcategories = new Set();\n    sortedMonths.forEach(monthYear => {\n      Object.values(filteredData[monthYear]).forEach(strategy => {\n        if (strategy[selectedAttribute]) {\n          Object.keys(strategy[selectedAttribute]).forEach(subcat => {\n            allSubcategories.add(subcat);\n          });\n        }\n      });\n    });\n\n    // Generate colors for each line\n    const colors = ['rgb(255, 99, 132)', 'rgb(54, 162, 235)', 'rgb(255, 205, 86)', 'rgb(75, 192, 192)', 'rgb(153, 102, 255)', 'rgb(255, 159, 64)', 'rgb(199, 199, 199)', 'rgb(83, 102, 147)', 'rgb(255, 87, 51)', 'rgb(46, 204, 113)', 'rgb(155, 89, 182)', 'rgb(241, 196, 15)', 'rgb(230, 126, 34)', 'rgb(52, 152, 219)', 'rgb(231, 76, 60)', 'rgb(26, 188, 156)', 'rgb(142, 68, 173)', 'rgb(39, 174, 96)', 'rgb(192, 57, 43)', 'rgb(41, 128, 185)'];\n\n    // Create datasets for each subcategory\n    const datasets = Array.from(allSubcategories).map((subcategory, index) => {\n      const data = sortedMonths.map(monthYear => {\n        // Aggregate data across selected strategies for this month and subcategory\n        let total = 0;\n        let count = 0;\n        Object.values(filteredData[monthYear]).forEach(strategy => {\n          if (strategy[selectedAttribute] && strategy[selectedAttribute][subcategory] !== undefined) {\n            total += strategy[selectedAttribute][subcategory];\n            count++;\n          }\n        });\n        return count > 0 ? total / count : 0; // Average across strategies\n      });\n      return {\n        label: subcategory,\n        data: data,\n        borderColor: colors[index % colors.length],\n        backgroundColor: colors[index % colors.length],\n        // + '20' to make them a dark shade\n        tension: 0.1\n      };\n    });\n    return {\n      labels,\n      datasets,\n      coverage\n    };\n  };\n\n  // Custom plugin to display coverage at the top\n  const coveragePlugin = {\n    id: 'coverageDisplay',\n    afterDraw: chart => {\n      const {\n        ctx,\n        chartArea,\n        scales\n      } = chart;\n\n      // Get coverage data from chart's custom property\n      const coverage = chart.config.options.coverageData;\n      if (!coverage || coverage.length === 0) return;\n      ctx.save();\n      ctx.font = '12px Arial';\n      ctx.textAlign = 'center';\n      coverage.forEach((coverageValue, index) => {\n        // Determine color based on coverage percentage\n        let color = '#000000'; // Black for >= 90%\n        if (coverageValue < 80) {\n          color = '#dc3545'; // Red for < 80%\n        } else if (coverageValue < 90) {\n          color = '#ffc107'; // Yellow for < 90%\n        }\n        ctx.fillStyle = color;\n\n        // Calculate x position for each label\n        const xScale = scales.x;\n        const xPos = xScale.getPixelForTick(index);\n        const yPos = chartArea.top - 20;\n\n        // Draw coverage percentage\n        ctx.fillText(`${coverageValue.toFixed(1)}%`, xPos, yPos);\n      });\n      ctx.restore();\n    }\n  };\n  const chartOptions = {\n    responsive: true,\n    plugins: {\n      legend: {\n        position: 'bottom',\n        labels: {\n          usePointStyle: true,\n          pointStyle: 'circle',\n          boxWidth: 12,\n          boxHeight: 8\n        }\n      },\n      title: {\n        display: true,\n        text: `${selectedAttribute} Distribution Over Time`,\n        font: {\n          size: 16\n        },\n        padding: {\n          bottom: 50\n        }\n      },\n      coverageDisplay: true\n    },\n    layout: {\n      padding: {\n        top: 0 // Add padding at top for coverage display\n      }\n    },\n    scales: {\n      y: {\n        beginAtZero: true,\n        ticks: {\n          callback: function (value) {\n            return value + '%';\n          }\n        },\n        title: {\n          display: true,\n          text: 'Percentage (%)'\n        }\n      },\n      x: {\n        title: {\n          display: true,\n          text: 'Month'\n        }\n      }\n    },\n    elements: {\n      point: {\n        radius: 4\n      }\n    }\n  };\n  const availableAttributes = getAvailableAttributes();\n  const {\n    months,\n    years\n  } = getAvailableMonthsYears();\n  const chartData = getChartData();\n\n  // Update chart options with current coverage data\n  const chartOptionsWithCoverage = {\n    ...chartOptions,\n    coverageData: chartData.coverage\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"content-page\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"home-arrow-container\",\n        children: /*#__PURE__*/_jsxDEV(Link, {\n          to: \"/\",\n          className: \"back-to-home-arrow\",\n          children: [/*#__PURE__*/_jsxDEV(\"i\", {\n            className: \"bi bi-arrow-left\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 372,\n            columnNumber: 13\n          }, this), \" Home\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 371,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 370,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(StrategyDropdown, {\n        currentStrategy: currentStrategy,\n        onStrategyChange: setCurrentStrategy\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 375,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"section_title\",\n        children: \"Strategy Insights\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 379,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"hr\", {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 380,\n        columnNumber: 9\n      }, this), loading ? /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"spinner-border\",\n          role: \"status\",\n          children: /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"visually-hidden\",\n            children: \"Loading...\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 384,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 383,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 382,\n        columnNumber: 11\n      }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"strategy-content\",\n        children: strategyData && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"chart-container\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"chart-controls\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"control-group attribute-dropdown\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"control-label\",\n                children: \"Attribute\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 395,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                className: \"form-select\",\n                value: selectedAttribute,\n                onChange: e => setSelectedAttribute(e.target.value),\n                children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"\",\n                  children: \"Select Attribute\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 401,\n                  columnNumber: 23\n                }, this), availableAttributes.map(attr => /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: attr,\n                  children: attr\n                }, attr, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 403,\n                  columnNumber: 25\n                }, this))]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 396,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 394,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"date-controls\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"date-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"control-label\",\n                  children: \"Start Date\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 412,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"start-date-container\",\n                  children: [/*#__PURE__*/_jsxDEV(\"select\", {\n                    value: startMonth,\n                    onChange: e => setStartMonth(e.target.value),\n                    children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                      value: \"\",\n                      children: \"Month\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 418,\n                      columnNumber: 27\n                    }, this), months.map(month => /*#__PURE__*/_jsxDEV(\"option\", {\n                      value: month,\n                      children: new Date(2000, month - 1).toLocaleDateString('en-US', {\n                        month: 'short'\n                      })\n                    }, month, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 420,\n                      columnNumber: 29\n                    }, this))]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 414,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                    value: startYear,\n                    onChange: e => setStartYear(e.target.value),\n                    children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                      value: \"\",\n                      children: \"Year\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 429,\n                      columnNumber: 27\n                    }, this), years.map(year => /*#__PURE__*/_jsxDEV(\"option\", {\n                      value: year,\n                      children: year\n                    }, year, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 431,\n                      columnNumber: 29\n                    }, this))]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 425,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    className: \"start-button\",\n                    onClick: setToStart,\n                    children: \"Start\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 434,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 413,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 411,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"date-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"control-label\",\n                  children: \"End Date\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 445,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"end-date-container\",\n                  children: [/*#__PURE__*/_jsxDEV(\"select\", {\n                    value: endMonth,\n                    onChange: e => setEndMonth(e.target.value),\n                    children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                      value: \"\",\n                      children: \"Month\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 451,\n                      columnNumber: 27\n                    }, this), months.map(month => /*#__PURE__*/_jsxDEV(\"option\", {\n                      value: month,\n                      children: new Date(2000, month - 1).toLocaleDateString('en-US', {\n                        month: 'short'\n                      })\n                    }, month, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 453,\n                      columnNumber: 29\n                    }, this))]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 447,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                    value: endYear,\n                    onChange: e => setEndYear(e.target.value),\n                    children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                      value: \"\",\n                      children: \"Year\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 462,\n                      columnNumber: 27\n                    }, this), years.map(year => /*#__PURE__*/_jsxDEV(\"option\", {\n                      value: year,\n                      children: year\n                    }, year, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 464,\n                      columnNumber: 29\n                    }, this))]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 458,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    className: \"now-button\",\n                    onClick: setToNow,\n                    children: \"Now\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 467,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 446,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 444,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 409,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 392,\n            columnNumber: 17\n          }, this), selectedAttribute && chartData.datasets.length > 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"chart-wrapper\",\n            style: {\n              height: '400px'\n            },\n            children: /*#__PURE__*/_jsxDEV(Line, {\n              data: chartData,\n              options: chartOptionsWithCoverage,\n              plugins: [coveragePlugin]\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 481,\n              columnNumber: 21\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 480,\n            columnNumber: 19\n          }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"alert alert-info\",\n            children: \"Please select an attribute and ensure data is available for the selected date range.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 484,\n            columnNumber: 19\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 390,\n          columnNumber: 15\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 388,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 369,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 367,\n    columnNumber: 5\n  }, this);\n};\n_s(Strategy, \"atB9glxjlJs0fJdw26zYur8N8BQ=\", false, function () {\n  return [useParams];\n});\n_c = Strategy;\nexport default Strategy;\nvar _c;\n$RefreshReg$(_c, \"Strategy\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Link", "useParams", "axios", "StrategyDropdown", "Chart", "ChartJS", "CategoryScale", "LinearScale", "PointElement", "LineElement", "Title", "<PERSON><PERSON><PERSON>", "Legend", "elements", "Line", "jsxDEV", "_jsxDEV", "register", "Strategy", "_s", "strategyName", "currentStrategy", "setCurrentStrategy", "strategyData", "setStrategyData", "loading", "setLoading", "selectedAttribute", "setSelectedAttribute", "startMonth", "setStartMonth", "startYear", "setStartYear", "endMonth", "setEndMonth", "endYear", "setEndYear", "fetchStrategyData", "strategy", "response", "get", "data", "Object", "keys", "length", "firstMonth", "firstStrategy", "attributes", "months", "sort", "startYearMonth", "startMonthNum", "parseMonthYear", "endYearMonth", "endMonthNum", "error", "console", "monthYearStr", "year", "month", "split", "getAvailableAttributes", "getAvailableMonthsYears", "years", "monthsYears", "map", "Set", "setToNow", "now", "Date", "currentMonth", "getMonth", "toString", "padStart", "currentYear", "getFullYear", "setToStart", "getChartData", "labels", "datasets", "filteredData", "for<PERSON>ach", "monthYear", "yearNum", "parseInt", "monthNum", "startYearNum", "endYearNum", "isInRange", "monthData", "sortedMonths", "a", "b", "yearA", "monthA", "yearB", "monthB", "date", "toLocaleDateString", "coverage", "totalCoverage", "strategyCount", "values", "monthTotal", "reduce", "sum", "value", "allSubcategories", "subcat", "add", "colors", "Array", "from", "subcategory", "index", "total", "count", "undefined", "label", "borderColor", "backgroundColor", "tension", "coveragePlugin", "id", "afterDraw", "chart", "ctx", "chartArea", "scales", "config", "options", "coverageData", "save", "font", "textAlign", "coverageValue", "color", "fillStyle", "xScale", "x", "xPos", "getPixelForTick", "yPos", "top", "fillText", "toFixed", "restore", "chartOptions", "responsive", "plugins", "legend", "position", "usePointStyle", "pointStyle", "boxWidth", "boxHeight", "title", "display", "text", "size", "padding", "bottom", "coverageDisplay", "layout", "y", "beginAtZero", "ticks", "callback", "point", "radius", "availableAttributes", "chartData", "chartOptionsWithCoverage", "children", "className", "to", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onStrategyChange", "role", "onChange", "e", "target", "attr", "onClick", "style", "height", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Desktop/Coding/BVS/CrystalBall/insights-app/frontend/src/components/Strategy.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { Link, useParams } from 'react-router-dom';\nimport axios from 'axios';\nimport StrategyDropdown from './StrategyDropdown';\nimport {\n  Chart as ChartJS,\n  CategoryScale,\n  LinearScale,\n  PointElement,\n  LineElement,\n  Title,\n  Tooltip,\n  Legend,\n  elements,\n} from 'chart.js';\nimport { Line } from 'react-chartjs-2';\n\nChartJS.register(\n  CategoryScale,\n  LinearScale,\n  PointElement,\n  LineElement,\n  Title,\n  Tooltip,\n  Legend\n);\n\nconst Strategy = () => {\n  const { strategyName } = useParams();\n  const [currentStrategy, setCurrentStrategy] = useState(strategyName || 'Alle (Durchschnitt)');\n  const [strategyData, setStrategyData] = useState(null);\n  const [loading, setLoading] = useState(true);\n\n  // Chart state\n  const [selectedAttribute, setSelectedAttribute] = useState('');\n  const [startMonth, setStartMonth] = useState('');\n  const [startYear, setStartYear] = useState('');\n  const [endMonth, setEndMonth] = useState('');\n  const [endYear, setEndYear] = useState('');\n\n  useEffect(() => {\n    fetchStrategyData(currentStrategy);\n  }, [currentStrategy]);\n\n  const fetchStrategyData = async (strategy) => {\n    try {\n      setLoading(true);\n      const response = await axios.get(`/api/strategy/${strategy}/`);\n      setStrategyData(response.data);\n\n      // Initialize dropdowns with first available data\n      if (response.data && Object.keys(response.data).length > 0) {\n        const firstMonth = Object.keys(response.data)[0];\n        const firstStrategy = Object.keys(response.data[firstMonth])[0];\n        const attributes = Object.keys(response.data[firstMonth][firstStrategy]);\n\n        if (attributes.length > 0 && !selectedAttribute) {\n          setSelectedAttribute(attributes[0]);\n        }\n\n        // Set default date range\n        const months = Object.keys(response.data).sort();\n        if (months.length > 0) {\n          const [startYearMonth, startMonthNum] = parseMonthYear(months[0]);\n          const [endYearMonth, endMonthNum] = parseMonthYear(months[months.length - 1]);\n\n          if (!startMonth) {\n            setStartMonth(startMonthNum);\n            setStartYear(startYearMonth);\n          }\n          if (!endMonth) {\n            setEndMonth(endMonthNum);\n            setEndYear(endYearMonth);\n          }\n        }\n      }\n    } catch (error) {\n      console.error('Error fetching strategy data:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Helper function to parse month-year string like \"2025 - 04\"\n  const parseMonthYear = (monthYearStr) => {\n    const [year, month] = monthYearStr.split(' - ');\n    return [year, month];\n  };\n\n  // Helper function to create month-year string\n  // const createMonthYearStr = (year, month) => {\n  //   return `${year} - ${month.padStart(2, '0')}`;\n  // };\n\n  // Get available attributes from data\n  const getAvailableAttributes = () => {\n    if (!strategyData || Object.keys(strategyData).length === 0) return [];\n\n    const firstMonth = Object.keys(strategyData)[0];\n    const firstStrategy = Object.keys(strategyData[firstMonth])[0];\n    return Object.keys(strategyData[firstMonth][firstStrategy]);\n  };\n\n  // Get available months and years\n  const getAvailableMonthsYears = () => {\n    if (!strategyData) return { months: [], years: [] };\n\n    const monthsYears = Object.keys(strategyData).map(parseMonthYear);\n    const months = [...new Set(monthsYears.map(([year, month]) => month))].sort();\n    const years = [...new Set(monthsYears.map(([year, month]) => year))].sort();\n\n    return { months, years };\n  };\n\n  // Set end date to current month/year\n  const setToNow = () => {\n    const now = new Date();\n    const currentMonth = (now.getMonth() + 1).toString().padStart(2, '0');\n    const currentYear = now.getFullYear().toString();\n\n    setEndMonth(currentMonth);\n    setEndYear(currentYear);\n  };\n\n  // Set start date to first available month/year\n  const setToStart = () => {\n    if (!strategyData || Object.keys(strategyData).length === 0) return;\n    \n    const months = Object.keys(strategyData).sort();\n    if (months.length > 0) {\n      const [startYearMonth, startMonthNum] = parseMonthYear(months[0]);\n      setStartMonth(startMonthNum);\n      setStartYear(startYearMonth);\n    }\n  };\n\n  // Get chart data filtered by selected strategy\n  const getChartData = () => {\n    if (!strategyData || !selectedAttribute || !startMonth || !startYear || !endMonth || !endYear) {\n      return { labels: [], datasets: [] };\n    }\n\n    // Filter data by date range\n    const filteredData = {};\n    Object.keys(strategyData).forEach(monthYear => {\n      const [year, month] = parseMonthYear(monthYear);\n      const yearNum = parseInt(year);\n      const monthNum = parseInt(month);\n      const startYearNum = parseInt(startYear);\n      const startMonthNum = parseInt(startMonth);\n      const endYearNum = parseInt(endYear);\n      const endMonthNum = parseInt(endMonth);\n\n      const isInRange = (yearNum > startYearNum || (yearNum === startYearNum && monthNum >= startMonthNum)) &&\n                       (yearNum < endYearNum || (yearNum === endYearNum && monthNum <= endMonthNum));\n\n      if (isInRange) {\n        // Filter by selected strategy\n        const monthData = strategyData[monthYear];\n        if (currentStrategy === \"Alle (Durchschnitt)\") {\n          filteredData[monthYear] = monthData;\n        } else if (monthData[currentStrategy]) {\n          filteredData[monthYear] = { [currentStrategy]: monthData[currentStrategy] };\n        }\n      }\n    });\n\n    // Sort months chronologically\n    const sortedMonths = Object.keys(filteredData).sort((a, b) => {\n      const [yearA, monthA] = parseMonthYear(a);\n      const [yearB, monthB] = parseMonthYear(b);\n      return new Date(yearA, monthA - 1) - new Date(yearB, monthB - 1);\n    });\n\n    // Create labels (formatted month names)\n    const labels = sortedMonths.map(monthYear => {\n      const [year, month] = parseMonthYear(monthYear);\n      const date = new Date(year, month - 1);\n      return date.toLocaleDateString('en-US', { month: 'short', year: 'numeric' });\n    });\n\n    // Calculate coverage for each month\n    const coverage = sortedMonths.map(monthYear => {\n      let totalCoverage = 0;\n      let strategyCount = 0;\n\n      Object.values(filteredData[monthYear]).forEach(strategy => {\n        if (strategy[selectedAttribute]) {\n          const monthTotal = Object.values(strategy[selectedAttribute]).reduce((sum, value) => sum + value, 0);\n          totalCoverage += monthTotal;\n          strategyCount++;\n        }\n      });\n\n      return strategyCount > 0 ? totalCoverage / strategyCount : 0;\n    });\n\n    // Get all unique currencies/subcategories for the selected attribute\n    const allSubcategories = new Set();\n    sortedMonths.forEach(monthYear => {\n      Object.values(filteredData[monthYear]).forEach(strategy => {\n        if (strategy[selectedAttribute]) {\n          Object.keys(strategy[selectedAttribute]).forEach(subcat => {\n            allSubcategories.add(subcat);\n          });\n        }\n      });\n    });\n\n    // Generate colors for each line\n    const colors = [\n      'rgb(255, 99, 132)',\n      'rgb(54, 162, 235)',\n      'rgb(255, 205, 86)',\n      'rgb(75, 192, 192)',\n      'rgb(153, 102, 255)',\n      'rgb(255, 159, 64)',\n      'rgb(199, 199, 199)',\n      'rgb(83, 102, 147)',\n      'rgb(255, 87, 51)',\n      'rgb(46, 204, 113)',\n      'rgb(155, 89, 182)',\n      'rgb(241, 196, 15)',\n      'rgb(230, 126, 34)',\n      'rgb(52, 152, 219)',\n      'rgb(231, 76, 60)',\n      'rgb(26, 188, 156)',\n      'rgb(142, 68, 173)',\n      'rgb(39, 174, 96)',\n      'rgb(192, 57, 43)',\n      'rgb(41, 128, 185)',\n    ];\n\n    // Create datasets for each subcategory\n    const datasets = Array.from(allSubcategories).map((subcategory, index) => {\n      const data = sortedMonths.map(monthYear => {\n        // Aggregate data across selected strategies for this month and subcategory\n        let total = 0;\n        let count = 0;\n        Object.values(filteredData[monthYear]).forEach(strategy => {\n          if (strategy[selectedAttribute] && strategy[selectedAttribute][subcategory] !== undefined) {\n            total += strategy[selectedAttribute][subcategory];\n            count++;\n          }\n        });\n        return count > 0 ? total / count : 0; // Average across strategies\n      });\n\n      return {\n        label: subcategory,\n        data: data,\n        borderColor: colors[index % colors.length],\n        backgroundColor: colors[index % colors.length], // + '20' to make them a dark shade\n        tension: 0.1,\n      };\n    });\n\n    return { labels, datasets, coverage };\n  };\n\n  // Custom plugin to display coverage at the top\n  const coveragePlugin = {\n    id: 'coverageDisplay',\n    afterDraw: (chart) => {\n      const { ctx, chartArea, scales } = chart;\n\n      // Get coverage data from chart's custom property\n      const coverage = chart.config.options.coverageData;\n\n      if (!coverage || coverage.length === 0) return;\n\n      ctx.save();\n      ctx.font = '12px Arial';\n      ctx.textAlign = 'center';\n\n      coverage.forEach((coverageValue, index) => {\n        // Determine color based on coverage percentage\n        let color = '#000000'; // Black for >= 90%\n        if (coverageValue < 80) {\n          color = '#dc3545'; // Red for < 80%\n        } else if (coverageValue < 90) {\n          color = '#ffc107'; // Yellow for < 90%\n        }\n\n        ctx.fillStyle = color;\n\n        // Calculate x position for each label\n        const xScale = scales.x;\n        const xPos = xScale.getPixelForTick(index);\n        const yPos = chartArea.top - 20;\n\n        // Draw coverage percentage\n        ctx.fillText(`${coverageValue.toFixed(1)}%`, xPos, yPos);\n      });\n\n      ctx.restore();\n    }\n  };\n\n  const chartOptions = {\n    responsive: true,\n    plugins: {\n      legend: {\n        position: 'bottom',\n        labels: {\n          usePointStyle: true,\n          pointStyle: 'circle',\n          boxWidth: 12,\n          boxHeight: 8,\n        }\n      },\n      title: {\n        display: true,\n        text: `${selectedAttribute} Distribution Over Time`,\n        font: {\n          size: 16\n        },\n        padding: {\n          bottom: 50\n        }\n      },\n      coverageDisplay: true,\n    },\n    layout: {\n      padding: {\n        top: 0, // Add padding at top for coverage display\n      }\n    },\n    scales: {\n      y: {\n        beginAtZero: true,\n        ticks: {\n          callback: function(value) {\n            return value + '%';\n          }\n        },\n        title: {\n          display: true,\n          text: 'Percentage (%)'\n        }\n      },\n      x: {\n        title: {\n          display: true,\n          text: 'Month'\n        }\n      }\n    },\n    elements: {\n      point: {\n        radius: 4\n      }\n    }\n  };\n\n  const availableAttributes = getAvailableAttributes();\n  const { months, years } = getAvailableMonthsYears();\n  const chartData = getChartData();\n\n  // Update chart options with current coverage data\n  const chartOptionsWithCoverage = {\n    ...chartOptions,\n    coverageData: chartData.coverage\n  };\n\n  return (\n    <div>\n      \n      <div className=\"content-page\">\n        <div className='home-arrow-container'>\n          <Link to=\"/\" className=\"back-to-home-arrow\">\n            <i className='bi bi-arrow-left'></i> Home\n          </Link>\n        </div>\n        <StrategyDropdown\n          currentStrategy={currentStrategy}\n          onStrategyChange={setCurrentStrategy}\n        />\n        <p className=\"section_title\">Strategy Insights</p>\n        <hr></hr>\n        {loading ? (\n          <div className=\"text-center\">\n            <div className=\"spinner-border\" role=\"status\">\n              <span className=\"visually-hidden\">Loading...</span>\n            </div>\n          </div>\n        ) : (\n          <div className=\"strategy-content\">\n            {strategyData && (\n              <div className=\"chart-container\">\n                {/* Chart Controls */}\n                <div className=\"chart-controls\">\n                  {/* Attribute Dropdown */}\n                  <div className=\"control-group attribute-dropdown\">\n                    <label className=\"control-label\">Attribute</label>\n                    <select\n                      className=\"form-select\"\n                      value={selectedAttribute}\n                      onChange={(e) => setSelectedAttribute(e.target.value)}\n                    >\n                      <option value=\"\">Select Attribute</option>\n                      {availableAttributes.map(attr => (\n                        <option key={attr} value={attr}>{attr}</option>\n                      ))}\n                    </select>\n                  </div>\n\n                  {/* Date Controls */}\n                  <div className=\"date-controls\">\n                    {/* Start Date */}\n                    <div className=\"date-group\">\n                      <label className=\"control-label\">Start Date</label>\n                      <div className=\"start-date-container\">\n                        <select\n                          value={startMonth}\n                          onChange={(e) => setStartMonth(e.target.value)}\n                        >\n                          <option value=\"\">Month</option>\n                          {months.map(month => (\n                            <option key={month} value={month}>\n                              {new Date(2000, month - 1).toLocaleDateString('en-US', { month: 'short' })}\n                            </option>\n                          ))}\n                        </select>\n                        <select\n                          value={startYear}\n                          onChange={(e) => setStartYear(e.target.value)}\n                        >\n                          <option value=\"\">Year</option>\n                          {years.map(year => (\n                            <option key={year} value={year}>{year}</option>\n                          ))}\n                        </select>\n                        <button\n                          className=\"start-button\"\n                          onClick={setToStart}\n                        >\n                          Start\n                        </button>\n                      </div>\n                    </div>\n\n                    {/* End Date */}\n                    <div className=\"date-group\">\n                      <label className=\"control-label\">End Date</label>\n                      <div className=\"end-date-container\">\n                        <select\n                          value={endMonth}\n                          onChange={(e) => setEndMonth(e.target.value)}\n                        >\n                          <option value=\"\">Month</option>\n                          {months.map(month => (\n                            <option key={month} value={month}>\n                              {new Date(2000, month - 1).toLocaleDateString('en-US', { month: 'short' })}\n                            </option>\n                          ))}\n                        </select>\n                        <select\n                          value={endYear}\n                          onChange={(e) => setEndYear(e.target.value)}\n                        >\n                          <option value=\"\">Year</option>\n                          {years.map(year => (\n                            <option key={year} value={year}>{year}</option>\n                          ))}\n                        </select>\n                        <button\n                          className=\"now-button\"\n                          onClick={setToNow}\n                        >\n                          Now\n                        </button>\n                      </div>\n                    </div>\n                  </div>\n                </div>\n\n                {/* Chart */}\n                {selectedAttribute && chartData.datasets.length > 0 ? (\n                  <div className=\"chart-wrapper\" style={{ height: '400px' }}>\n                    <Line data={chartData} options={chartOptionsWithCoverage} plugins={[coveragePlugin]} />\n                  </div>\n                ) : (\n                  <div className=\"alert alert-info\">\n                    Please select an attribute and ensure data is available for the selected date range.\n                  </div>\n                )}\n              </div>\n            )}\n          </div>\n        )}\n      </div>\n    </div>\n  );\n};\n\nexport default Strategy;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,IAAI,EAAEC,SAAS,QAAQ,kBAAkB;AAClD,OAAOC,KAAK,MAAM,OAAO;AACzB,OAAOC,gBAAgB,MAAM,oBAAoB;AACjD,SACEC,KAAK,IAAIC,OAAO,EAChBC,aAAa,EACbC,WAAW,EACXC,YAAY,EACZC,WAAW,EACXC,KAAK,EACLC,OAAO,EACPC,MAAM,EACNC,QAAQ,QACH,UAAU;AACjB,SAASC,IAAI,QAAQ,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEvCX,OAAO,CAACY,QAAQ,CACdX,aAAa,EACbC,WAAW,EACXC,YAAY,EACZC,WAAW,EACXC,KAAK,EACLC,OAAO,EACPC,MACF,CAAC;AAED,MAAMM,QAAQ,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACrB,MAAM;IAAEC;EAAa,CAAC,GAAGnB,SAAS,CAAC,CAAC;EACpC,MAAM,CAACoB,eAAe,EAAEC,kBAAkB,CAAC,GAAGxB,QAAQ,CAACsB,YAAY,IAAI,qBAAqB,CAAC;EAC7F,MAAM,CAACG,YAAY,EAAEC,eAAe,CAAC,GAAG1B,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAAC2B,OAAO,EAAEC,UAAU,CAAC,GAAG5B,QAAQ,CAAC,IAAI,CAAC;;EAE5C;EACA,MAAM,CAAC6B,iBAAiB,EAAEC,oBAAoB,CAAC,GAAG9B,QAAQ,CAAC,EAAE,CAAC;EAC9D,MAAM,CAAC+B,UAAU,EAAEC,aAAa,CAAC,GAAGhC,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACiC,SAAS,EAAEC,YAAY,CAAC,GAAGlC,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAACmC,QAAQ,EAAEC,WAAW,CAAC,GAAGpC,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACqC,OAAO,EAAEC,UAAU,CAAC,GAAGtC,QAAQ,CAAC,EAAE,CAAC;EAE1CC,SAAS,CAAC,MAAM;IACdsC,iBAAiB,CAAChB,eAAe,CAAC;EACpC,CAAC,EAAE,CAACA,eAAe,CAAC,CAAC;EAErB,MAAMgB,iBAAiB,GAAG,MAAOC,QAAQ,IAAK;IAC5C,IAAI;MACFZ,UAAU,CAAC,IAAI,CAAC;MAChB,MAAMa,QAAQ,GAAG,MAAMrC,KAAK,CAACsC,GAAG,CAAC,iBAAiBF,QAAQ,GAAG,CAAC;MAC9Dd,eAAe,CAACe,QAAQ,CAACE,IAAI,CAAC;;MAE9B;MACA,IAAIF,QAAQ,CAACE,IAAI,IAAIC,MAAM,CAACC,IAAI,CAACJ,QAAQ,CAACE,IAAI,CAAC,CAACG,MAAM,GAAG,CAAC,EAAE;QAC1D,MAAMC,UAAU,GAAGH,MAAM,CAACC,IAAI,CAACJ,QAAQ,CAACE,IAAI,CAAC,CAAC,CAAC,CAAC;QAChD,MAAMK,aAAa,GAAGJ,MAAM,CAACC,IAAI,CAACJ,QAAQ,CAACE,IAAI,CAACI,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC;QAC/D,MAAME,UAAU,GAAGL,MAAM,CAACC,IAAI,CAACJ,QAAQ,CAACE,IAAI,CAACI,UAAU,CAAC,CAACC,aAAa,CAAC,CAAC;QAExE,IAAIC,UAAU,CAACH,MAAM,GAAG,CAAC,IAAI,CAACjB,iBAAiB,EAAE;UAC/CC,oBAAoB,CAACmB,UAAU,CAAC,CAAC,CAAC,CAAC;QACrC;;QAEA;QACA,MAAMC,MAAM,GAAGN,MAAM,CAACC,IAAI,CAACJ,QAAQ,CAACE,IAAI,CAAC,CAACQ,IAAI,CAAC,CAAC;QAChD,IAAID,MAAM,CAACJ,MAAM,GAAG,CAAC,EAAE;UACrB,MAAM,CAACM,cAAc,EAAEC,aAAa,CAAC,GAAGC,cAAc,CAACJ,MAAM,CAAC,CAAC,CAAC,CAAC;UACjE,MAAM,CAACK,YAAY,EAAEC,WAAW,CAAC,GAAGF,cAAc,CAACJ,MAAM,CAACA,MAAM,CAACJ,MAAM,GAAG,CAAC,CAAC,CAAC;UAE7E,IAAI,CAACf,UAAU,EAAE;YACfC,aAAa,CAACqB,aAAa,CAAC;YAC5BnB,YAAY,CAACkB,cAAc,CAAC;UAC9B;UACA,IAAI,CAACjB,QAAQ,EAAE;YACbC,WAAW,CAACoB,WAAW,CAAC;YACxBlB,UAAU,CAACiB,YAAY,CAAC;UAC1B;QACF;MACF;IACF,CAAC,CAAC,OAAOE,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;IACvD,CAAC,SAAS;MACR7B,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAM0B,cAAc,GAAIK,YAAY,IAAK;IACvC,MAAM,CAACC,IAAI,EAAEC,KAAK,CAAC,GAAGF,YAAY,CAACG,KAAK,CAAC,KAAK,CAAC;IAC/C,OAAO,CAACF,IAAI,EAAEC,KAAK,CAAC;EACtB,CAAC;;EAED;EACA;EACA;EACA;;EAEA;EACA,MAAME,sBAAsB,GAAGA,CAAA,KAAM;IACnC,IAAI,CAACtC,YAAY,IAAImB,MAAM,CAACC,IAAI,CAACpB,YAAY,CAAC,CAACqB,MAAM,KAAK,CAAC,EAAE,OAAO,EAAE;IAEtE,MAAMC,UAAU,GAAGH,MAAM,CAACC,IAAI,CAACpB,YAAY,CAAC,CAAC,CAAC,CAAC;IAC/C,MAAMuB,aAAa,GAAGJ,MAAM,CAACC,IAAI,CAACpB,YAAY,CAACsB,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC;IAC9D,OAAOH,MAAM,CAACC,IAAI,CAACpB,YAAY,CAACsB,UAAU,CAAC,CAACC,aAAa,CAAC,CAAC;EAC7D,CAAC;;EAED;EACA,MAAMgB,uBAAuB,GAAGA,CAAA,KAAM;IACpC,IAAI,CAACvC,YAAY,EAAE,OAAO;MAAEyB,MAAM,EAAE,EAAE;MAAEe,KAAK,EAAE;IAAG,CAAC;IAEnD,MAAMC,WAAW,GAAGtB,MAAM,CAACC,IAAI,CAACpB,YAAY,CAAC,CAAC0C,GAAG,CAACb,cAAc,CAAC;IACjE,MAAMJ,MAAM,GAAG,CAAC,GAAG,IAAIkB,GAAG,CAACF,WAAW,CAACC,GAAG,CAAC,CAAC,CAACP,IAAI,EAAEC,KAAK,CAAC,KAAKA,KAAK,CAAC,CAAC,CAAC,CAACV,IAAI,CAAC,CAAC;IAC7E,MAAMc,KAAK,GAAG,CAAC,GAAG,IAAIG,GAAG,CAACF,WAAW,CAACC,GAAG,CAAC,CAAC,CAACP,IAAI,EAAEC,KAAK,CAAC,KAAKD,IAAI,CAAC,CAAC,CAAC,CAACT,IAAI,CAAC,CAAC;IAE3E,OAAO;MAAED,MAAM;MAAEe;IAAM,CAAC;EAC1B,CAAC;;EAED;EACA,MAAMI,QAAQ,GAAGA,CAAA,KAAM;IACrB,MAAMC,GAAG,GAAG,IAAIC,IAAI,CAAC,CAAC;IACtB,MAAMC,YAAY,GAAG,CAACF,GAAG,CAACG,QAAQ,CAAC,CAAC,GAAG,CAAC,EAAEC,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;IACrE,MAAMC,WAAW,GAAGN,GAAG,CAACO,WAAW,CAAC,CAAC,CAACH,QAAQ,CAAC,CAAC;IAEhDtC,WAAW,CAACoC,YAAY,CAAC;IACzBlC,UAAU,CAACsC,WAAW,CAAC;EACzB,CAAC;;EAED;EACA,MAAME,UAAU,GAAGA,CAAA,KAAM;IACvB,IAAI,CAACrD,YAAY,IAAImB,MAAM,CAACC,IAAI,CAACpB,YAAY,CAAC,CAACqB,MAAM,KAAK,CAAC,EAAE;IAE7D,MAAMI,MAAM,GAAGN,MAAM,CAACC,IAAI,CAACpB,YAAY,CAAC,CAAC0B,IAAI,CAAC,CAAC;IAC/C,IAAID,MAAM,CAACJ,MAAM,GAAG,CAAC,EAAE;MACrB,MAAM,CAACM,cAAc,EAAEC,aAAa,CAAC,GAAGC,cAAc,CAACJ,MAAM,CAAC,CAAC,CAAC,CAAC;MACjElB,aAAa,CAACqB,aAAa,CAAC;MAC5BnB,YAAY,CAACkB,cAAc,CAAC;IAC9B;EACF,CAAC;;EAED;EACA,MAAM2B,YAAY,GAAGA,CAAA,KAAM;IACzB,IAAI,CAACtD,YAAY,IAAI,CAACI,iBAAiB,IAAI,CAACE,UAAU,IAAI,CAACE,SAAS,IAAI,CAACE,QAAQ,IAAI,CAACE,OAAO,EAAE;MAC7F,OAAO;QAAE2C,MAAM,EAAE,EAAE;QAAEC,QAAQ,EAAE;MAAG,CAAC;IACrC;;IAEA;IACA,MAAMC,YAAY,GAAG,CAAC,CAAC;IACvBtC,MAAM,CAACC,IAAI,CAACpB,YAAY,CAAC,CAAC0D,OAAO,CAACC,SAAS,IAAI;MAC7C,MAAM,CAACxB,IAAI,EAAEC,KAAK,CAAC,GAAGP,cAAc,CAAC8B,SAAS,CAAC;MAC/C,MAAMC,OAAO,GAAGC,QAAQ,CAAC1B,IAAI,CAAC;MAC9B,MAAM2B,QAAQ,GAAGD,QAAQ,CAACzB,KAAK,CAAC;MAChC,MAAM2B,YAAY,GAAGF,QAAQ,CAACrD,SAAS,CAAC;MACxC,MAAMoB,aAAa,GAAGiC,QAAQ,CAACvD,UAAU,CAAC;MAC1C,MAAM0D,UAAU,GAAGH,QAAQ,CAACjD,OAAO,CAAC;MACpC,MAAMmB,WAAW,GAAG8B,QAAQ,CAACnD,QAAQ,CAAC;MAEtC,MAAMuD,SAAS,GAAG,CAACL,OAAO,GAAGG,YAAY,IAAKH,OAAO,KAAKG,YAAY,IAAID,QAAQ,IAAIlC,aAAc,MAClFgC,OAAO,GAAGI,UAAU,IAAKJ,OAAO,KAAKI,UAAU,IAAIF,QAAQ,IAAI/B,WAAY,CAAC;MAE9F,IAAIkC,SAAS,EAAE;QACb;QACA,MAAMC,SAAS,GAAGlE,YAAY,CAAC2D,SAAS,CAAC;QACzC,IAAI7D,eAAe,KAAK,qBAAqB,EAAE;UAC7C2D,YAAY,CAACE,SAAS,CAAC,GAAGO,SAAS;QACrC,CAAC,MAAM,IAAIA,SAAS,CAACpE,eAAe,CAAC,EAAE;UACrC2D,YAAY,CAACE,SAAS,CAAC,GAAG;YAAE,CAAC7D,eAAe,GAAGoE,SAAS,CAACpE,eAAe;UAAE,CAAC;QAC7E;MACF;IACF,CAAC,CAAC;;IAEF;IACA,MAAMqE,YAAY,GAAGhD,MAAM,CAACC,IAAI,CAACqC,YAAY,CAAC,CAAC/B,IAAI,CAAC,CAAC0C,CAAC,EAAEC,CAAC,KAAK;MAC5D,MAAM,CAACC,KAAK,EAAEC,MAAM,CAAC,GAAG1C,cAAc,CAACuC,CAAC,CAAC;MACzC,MAAM,CAACI,KAAK,EAAEC,MAAM,CAAC,GAAG5C,cAAc,CAACwC,CAAC,CAAC;MACzC,OAAO,IAAIvB,IAAI,CAACwB,KAAK,EAAEC,MAAM,GAAG,CAAC,CAAC,GAAG,IAAIzB,IAAI,CAAC0B,KAAK,EAAEC,MAAM,GAAG,CAAC,CAAC;IAClE,CAAC,CAAC;;IAEF;IACA,MAAMlB,MAAM,GAAGY,YAAY,CAACzB,GAAG,CAACiB,SAAS,IAAI;MAC3C,MAAM,CAACxB,IAAI,EAAEC,KAAK,CAAC,GAAGP,cAAc,CAAC8B,SAAS,CAAC;MAC/C,MAAMe,IAAI,GAAG,IAAI5B,IAAI,CAACX,IAAI,EAAEC,KAAK,GAAG,CAAC,CAAC;MACtC,OAAOsC,IAAI,CAACC,kBAAkB,CAAC,OAAO,EAAE;QAAEvC,KAAK,EAAE,OAAO;QAAED,IAAI,EAAE;MAAU,CAAC,CAAC;IAC9E,CAAC,CAAC;;IAEF;IACA,MAAMyC,QAAQ,GAAGT,YAAY,CAACzB,GAAG,CAACiB,SAAS,IAAI;MAC7C,IAAIkB,aAAa,GAAG,CAAC;MACrB,IAAIC,aAAa,GAAG,CAAC;MAErB3D,MAAM,CAAC4D,MAAM,CAACtB,YAAY,CAACE,SAAS,CAAC,CAAC,CAACD,OAAO,CAAC3C,QAAQ,IAAI;QACzD,IAAIA,QAAQ,CAACX,iBAAiB,CAAC,EAAE;UAC/B,MAAM4E,UAAU,GAAG7D,MAAM,CAAC4D,MAAM,CAAChE,QAAQ,CAACX,iBAAiB,CAAC,CAAC,CAAC6E,MAAM,CAAC,CAACC,GAAG,EAAEC,KAAK,KAAKD,GAAG,GAAGC,KAAK,EAAE,CAAC,CAAC;UACpGN,aAAa,IAAIG,UAAU;UAC3BF,aAAa,EAAE;QACjB;MACF,CAAC,CAAC;MAEF,OAAOA,aAAa,GAAG,CAAC,GAAGD,aAAa,GAAGC,aAAa,GAAG,CAAC;IAC9D,CAAC,CAAC;;IAEF;IACA,MAAMM,gBAAgB,GAAG,IAAIzC,GAAG,CAAC,CAAC;IAClCwB,YAAY,CAACT,OAAO,CAACC,SAAS,IAAI;MAChCxC,MAAM,CAAC4D,MAAM,CAACtB,YAAY,CAACE,SAAS,CAAC,CAAC,CAACD,OAAO,CAAC3C,QAAQ,IAAI;QACzD,IAAIA,QAAQ,CAACX,iBAAiB,CAAC,EAAE;UAC/Be,MAAM,CAACC,IAAI,CAACL,QAAQ,CAACX,iBAAiB,CAAC,CAAC,CAACsD,OAAO,CAAC2B,MAAM,IAAI;YACzDD,gBAAgB,CAACE,GAAG,CAACD,MAAM,CAAC;UAC9B,CAAC,CAAC;QACJ;MACF,CAAC,CAAC;IACJ,CAAC,CAAC;;IAEF;IACA,MAAME,MAAM,GAAG,CACb,mBAAmB,EACnB,mBAAmB,EACnB,mBAAmB,EACnB,mBAAmB,EACnB,oBAAoB,EACpB,mBAAmB,EACnB,oBAAoB,EACpB,mBAAmB,EACnB,kBAAkB,EAClB,mBAAmB,EACnB,mBAAmB,EACnB,mBAAmB,EACnB,mBAAmB,EACnB,mBAAmB,EACnB,kBAAkB,EAClB,mBAAmB,EACnB,mBAAmB,EACnB,kBAAkB,EAClB,kBAAkB,EAClB,mBAAmB,CACpB;;IAED;IACA,MAAM/B,QAAQ,GAAGgC,KAAK,CAACC,IAAI,CAACL,gBAAgB,CAAC,CAAC1C,GAAG,CAAC,CAACgD,WAAW,EAAEC,KAAK,KAAK;MACxE,MAAMzE,IAAI,GAAGiD,YAAY,CAACzB,GAAG,CAACiB,SAAS,IAAI;QACzC;QACA,IAAIiC,KAAK,GAAG,CAAC;QACb,IAAIC,KAAK,GAAG,CAAC;QACb1E,MAAM,CAAC4D,MAAM,CAACtB,YAAY,CAACE,SAAS,CAAC,CAAC,CAACD,OAAO,CAAC3C,QAAQ,IAAI;UACzD,IAAIA,QAAQ,CAACX,iBAAiB,CAAC,IAAIW,QAAQ,CAACX,iBAAiB,CAAC,CAACsF,WAAW,CAAC,KAAKI,SAAS,EAAE;YACzFF,KAAK,IAAI7E,QAAQ,CAACX,iBAAiB,CAAC,CAACsF,WAAW,CAAC;YACjDG,KAAK,EAAE;UACT;QACF,CAAC,CAAC;QACF,OAAOA,KAAK,GAAG,CAAC,GAAGD,KAAK,GAAGC,KAAK,GAAG,CAAC,CAAC,CAAC;MACxC,CAAC,CAAC;MAEF,OAAO;QACLE,KAAK,EAAEL,WAAW;QAClBxE,IAAI,EAAEA,IAAI;QACV8E,WAAW,EAAET,MAAM,CAACI,KAAK,GAAGJ,MAAM,CAAClE,MAAM,CAAC;QAC1C4E,eAAe,EAAEV,MAAM,CAACI,KAAK,GAAGJ,MAAM,CAAClE,MAAM,CAAC;QAAE;QAChD6E,OAAO,EAAE;MACX,CAAC;IACH,CAAC,CAAC;IAEF,OAAO;MAAE3C,MAAM;MAAEC,QAAQ;MAAEoB;IAAS,CAAC;EACvC,CAAC;;EAED;EACA,MAAMuB,cAAc,GAAG;IACrBC,EAAE,EAAE,iBAAiB;IACrBC,SAAS,EAAGC,KAAK,IAAK;MACpB,MAAM;QAAEC,GAAG;QAAEC,SAAS;QAAEC;MAAO,CAAC,GAAGH,KAAK;;MAExC;MACA,MAAM1B,QAAQ,GAAG0B,KAAK,CAACI,MAAM,CAACC,OAAO,CAACC,YAAY;MAElD,IAAI,CAAChC,QAAQ,IAAIA,QAAQ,CAACvD,MAAM,KAAK,CAAC,EAAE;MAExCkF,GAAG,CAACM,IAAI,CAAC,CAAC;MACVN,GAAG,CAACO,IAAI,GAAG,YAAY;MACvBP,GAAG,CAACQ,SAAS,GAAG,QAAQ;MAExBnC,QAAQ,CAAClB,OAAO,CAAC,CAACsD,aAAa,EAAErB,KAAK,KAAK;QACzC;QACA,IAAIsB,KAAK,GAAG,SAAS,CAAC,CAAC;QACvB,IAAID,aAAa,GAAG,EAAE,EAAE;UACtBC,KAAK,GAAG,SAAS,CAAC,CAAC;QACrB,CAAC,MAAM,IAAID,aAAa,GAAG,EAAE,EAAE;UAC7BC,KAAK,GAAG,SAAS,CAAC,CAAC;QACrB;QAEAV,GAAG,CAACW,SAAS,GAAGD,KAAK;;QAErB;QACA,MAAME,MAAM,GAAGV,MAAM,CAACW,CAAC;QACvB,MAAMC,IAAI,GAAGF,MAAM,CAACG,eAAe,CAAC3B,KAAK,CAAC;QAC1C,MAAM4B,IAAI,GAAGf,SAAS,CAACgB,GAAG,GAAG,EAAE;;QAE/B;QACAjB,GAAG,CAACkB,QAAQ,CAAC,GAAGT,aAAa,CAACU,OAAO,CAAC,CAAC,CAAC,GAAG,EAAEL,IAAI,EAAEE,IAAI,CAAC;MAC1D,CAAC,CAAC;MAEFhB,GAAG,CAACoB,OAAO,CAAC,CAAC;IACf;EACF,CAAC;EAED,MAAMC,YAAY,GAAG;IACnBC,UAAU,EAAE,IAAI;IAChBC,OAAO,EAAE;MACPC,MAAM,EAAE;QACNC,QAAQ,EAAE,QAAQ;QAClBzE,MAAM,EAAE;UACN0E,aAAa,EAAE,IAAI;UACnBC,UAAU,EAAE,QAAQ;UACpBC,QAAQ,EAAE,EAAE;UACZC,SAAS,EAAE;QACb;MACF,CAAC;MACDC,KAAK,EAAE;QACLC,OAAO,EAAE,IAAI;QACbC,IAAI,EAAE,GAAGnI,iBAAiB,yBAAyB;QACnD0G,IAAI,EAAE;UACJ0B,IAAI,EAAE;QACR,CAAC;QACDC,OAAO,EAAE;UACPC,MAAM,EAAE;QACV;MACF,CAAC;MACDC,eAAe,EAAE;IACnB,CAAC;IACDC,MAAM,EAAE;MACNH,OAAO,EAAE;QACPjB,GAAG,EAAE,CAAC,CAAE;MACV;IACF,CAAC;IACDf,MAAM,EAAE;MACNoC,CAAC,EAAE;QACDC,WAAW,EAAE,IAAI;QACjBC,KAAK,EAAE;UACLC,QAAQ,EAAE,SAAAA,CAAS7D,KAAK,EAAE;YACxB,OAAOA,KAAK,GAAG,GAAG;UACpB;QACF,CAAC;QACDkD,KAAK,EAAE;UACLC,OAAO,EAAE,IAAI;UACbC,IAAI,EAAE;QACR;MACF,CAAC;MACDnB,CAAC,EAAE;QACDiB,KAAK,EAAE;UACLC,OAAO,EAAE,IAAI;UACbC,IAAI,EAAE;QACR;MACF;IACF,CAAC;IACDjJ,QAAQ,EAAE;MACR2J,KAAK,EAAE;QACLC,MAAM,EAAE;MACV;IACF;EACF,CAAC;EAED,MAAMC,mBAAmB,GAAG7G,sBAAsB,CAAC,CAAC;EACpD,MAAM;IAAEb,MAAM;IAAEe;EAAM,CAAC,GAAGD,uBAAuB,CAAC,CAAC;EACnD,MAAM6G,SAAS,GAAG9F,YAAY,CAAC,CAAC;;EAEhC;EACA,MAAM+F,wBAAwB,GAAG;IAC/B,GAAGzB,YAAY;IACfhB,YAAY,EAAEwC,SAAS,CAACxE;EAC1B,CAAC;EAED,oBACEnF,OAAA;IAAA6J,QAAA,eAEE7J,OAAA;MAAK8J,SAAS,EAAC,cAAc;MAAAD,QAAA,gBAC3B7J,OAAA;QAAK8J,SAAS,EAAC,sBAAsB;QAAAD,QAAA,eACnC7J,OAAA,CAAChB,IAAI;UAAC+K,EAAE,EAAC,GAAG;UAACD,SAAS,EAAC,oBAAoB;UAAAD,QAAA,gBACzC7J,OAAA;YAAG8J,SAAS,EAAC;UAAkB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,SACtC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACNnK,OAAA,CAACb,gBAAgB;QACfkB,eAAe,EAAEA,eAAgB;QACjC+J,gBAAgB,EAAE9J;MAAmB;QAAA0J,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACtC,CAAC,eACFnK,OAAA;QAAG8J,SAAS,EAAC,eAAe;QAAAD,QAAA,EAAC;MAAiB;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC,eAClDnK,OAAA;QAAAgK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,EACR1J,OAAO,gBACNT,OAAA;QAAK8J,SAAS,EAAC,aAAa;QAAAD,QAAA,eAC1B7J,OAAA;UAAK8J,SAAS,EAAC,gBAAgB;UAACO,IAAI,EAAC,QAAQ;UAAAR,QAAA,eAC3C7J,OAAA;YAAM8J,SAAS,EAAC,iBAAiB;YAAAD,QAAA,EAAC;UAAU;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChD;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,gBAENnK,OAAA;QAAK8J,SAAS,EAAC,kBAAkB;QAAAD,QAAA,EAC9BtJ,YAAY,iBACXP,OAAA;UAAK8J,SAAS,EAAC,iBAAiB;UAAAD,QAAA,gBAE9B7J,OAAA;YAAK8J,SAAS,EAAC,gBAAgB;YAAAD,QAAA,gBAE7B7J,OAAA;cAAK8J,SAAS,EAAC,kCAAkC;cAAAD,QAAA,gBAC/C7J,OAAA;gBAAO8J,SAAS,EAAC,eAAe;gBAAAD,QAAA,EAAC;cAAS;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAClDnK,OAAA;gBACE8J,SAAS,EAAC,aAAa;gBACvBpE,KAAK,EAAE/E,iBAAkB;gBACzB2J,QAAQ,EAAGC,CAAC,IAAK3J,oBAAoB,CAAC2J,CAAC,CAACC,MAAM,CAAC9E,KAAK,CAAE;gBAAAmE,QAAA,gBAEtD7J,OAAA;kBAAQ0F,KAAK,EAAC,EAAE;kBAAAmE,QAAA,EAAC;gBAAgB;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,EACzCT,mBAAmB,CAACzG,GAAG,CAACwH,IAAI,iBAC3BzK,OAAA;kBAAmB0F,KAAK,EAAE+E,IAAK;kBAAAZ,QAAA,EAAEY;gBAAI,GAAxBA,IAAI;kBAAAT,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAA6B,CAC/C,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,eAGNnK,OAAA;cAAK8J,SAAS,EAAC,eAAe;cAAAD,QAAA,gBAE5B7J,OAAA;gBAAK8J,SAAS,EAAC,YAAY;gBAAAD,QAAA,gBACzB7J,OAAA;kBAAO8J,SAAS,EAAC,eAAe;kBAAAD,QAAA,EAAC;gBAAU;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACnDnK,OAAA;kBAAK8J,SAAS,EAAC,sBAAsB;kBAAAD,QAAA,gBACnC7J,OAAA;oBACE0F,KAAK,EAAE7E,UAAW;oBAClByJ,QAAQ,EAAGC,CAAC,IAAKzJ,aAAa,CAACyJ,CAAC,CAACC,MAAM,CAAC9E,KAAK,CAAE;oBAAAmE,QAAA,gBAE/C7J,OAAA;sBAAQ0F,KAAK,EAAC,EAAE;sBAAAmE,QAAA,EAAC;oBAAK;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,EAC9BnI,MAAM,CAACiB,GAAG,CAACN,KAAK,iBACf3C,OAAA;sBAAoB0F,KAAK,EAAE/C,KAAM;sBAAAkH,QAAA,EAC9B,IAAIxG,IAAI,CAAC,IAAI,EAAEV,KAAK,GAAG,CAAC,CAAC,CAACuC,kBAAkB,CAAC,OAAO,EAAE;wBAAEvC,KAAK,EAAE;sBAAQ,CAAC;oBAAC,GAD/DA,KAAK;sBAAAqH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAEV,CACT,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACI,CAAC,eACTnK,OAAA;oBACE0F,KAAK,EAAE3E,SAAU;oBACjBuJ,QAAQ,EAAGC,CAAC,IAAKvJ,YAAY,CAACuJ,CAAC,CAACC,MAAM,CAAC9E,KAAK,CAAE;oBAAAmE,QAAA,gBAE9C7J,OAAA;sBAAQ0F,KAAK,EAAC,EAAE;sBAAAmE,QAAA,EAAC;oBAAI;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,EAC7BpH,KAAK,CAACE,GAAG,CAACP,IAAI,iBACb1C,OAAA;sBAAmB0F,KAAK,EAAEhD,IAAK;sBAAAmH,QAAA,EAAEnH;oBAAI,GAAxBA,IAAI;sBAAAsH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAA6B,CAC/C,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACI,CAAC,eACTnK,OAAA;oBACE8J,SAAS,EAAC,cAAc;oBACxBY,OAAO,EAAE9G,UAAW;oBAAAiG,QAAA,EACrB;kBAED;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAGNnK,OAAA;gBAAK8J,SAAS,EAAC,YAAY;gBAAAD,QAAA,gBACzB7J,OAAA;kBAAO8J,SAAS,EAAC,eAAe;kBAAAD,QAAA,EAAC;gBAAQ;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACjDnK,OAAA;kBAAK8J,SAAS,EAAC,oBAAoB;kBAAAD,QAAA,gBACjC7J,OAAA;oBACE0F,KAAK,EAAEzE,QAAS;oBAChBqJ,QAAQ,EAAGC,CAAC,IAAKrJ,WAAW,CAACqJ,CAAC,CAACC,MAAM,CAAC9E,KAAK,CAAE;oBAAAmE,QAAA,gBAE7C7J,OAAA;sBAAQ0F,KAAK,EAAC,EAAE;sBAAAmE,QAAA,EAAC;oBAAK;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,EAC9BnI,MAAM,CAACiB,GAAG,CAACN,KAAK,iBACf3C,OAAA;sBAAoB0F,KAAK,EAAE/C,KAAM;sBAAAkH,QAAA,EAC9B,IAAIxG,IAAI,CAAC,IAAI,EAAEV,KAAK,GAAG,CAAC,CAAC,CAACuC,kBAAkB,CAAC,OAAO,EAAE;wBAAEvC,KAAK,EAAE;sBAAQ,CAAC;oBAAC,GAD/DA,KAAK;sBAAAqH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAEV,CACT,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACI,CAAC,eACTnK,OAAA;oBACE0F,KAAK,EAAEvE,OAAQ;oBACfmJ,QAAQ,EAAGC,CAAC,IAAKnJ,UAAU,CAACmJ,CAAC,CAACC,MAAM,CAAC9E,KAAK,CAAE;oBAAAmE,QAAA,gBAE5C7J,OAAA;sBAAQ0F,KAAK,EAAC,EAAE;sBAAAmE,QAAA,EAAC;oBAAI;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,EAC7BpH,KAAK,CAACE,GAAG,CAACP,IAAI,iBACb1C,OAAA;sBAAmB0F,KAAK,EAAEhD,IAAK;sBAAAmH,QAAA,EAAEnH;oBAAI,GAAxBA,IAAI;sBAAAsH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAA6B,CAC/C,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACI,CAAC,eACTnK,OAAA;oBACE8J,SAAS,EAAC,YAAY;oBACtBY,OAAO,EAAEvH,QAAS;oBAAA0G,QAAA,EACnB;kBAED;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,EAGLxJ,iBAAiB,IAAIgJ,SAAS,CAAC5F,QAAQ,CAACnC,MAAM,GAAG,CAAC,gBACjD5B,OAAA;YAAK8J,SAAS,EAAC,eAAe;YAACa,KAAK,EAAE;cAAEC,MAAM,EAAE;YAAQ,CAAE;YAAAf,QAAA,eACxD7J,OAAA,CAACF,IAAI;cAAC2B,IAAI,EAAEkI,SAAU;cAACzC,OAAO,EAAE0C,wBAAyB;cAACvB,OAAO,EAAE,CAAC3B,cAAc;YAAE;cAAAsD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpF,CAAC,gBAENnK,OAAA;YAAK8J,SAAS,EAAC,kBAAkB;YAAAD,QAAA,EAAC;UAElC;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MACN;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAChK,EAAA,CAndID,QAAQ;EAAA,QACajB,SAAS;AAAA;AAAA4L,EAAA,GAD9B3K,QAAQ;AAqdd,eAAeA,QAAQ;AAAC,IAAA2K,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}