[{"/Users/<USER>/Desktop/Coding/BVS/CrystalBall/insights-app/frontend/src/index.js": "1", "/Users/<USER>/Desktop/Coding/BVS/CrystalBall/insights-app/frontend/src/reportWebVitals.js": "2", "/Users/<USER>/Desktop/Coding/BVS/CrystalBall/insights-app/frontend/src/App.js": "3", "/Users/<USER>/Desktop/Coding/BVS/CrystalBall/insights-app/frontend/src/components/Strategy.js": "4", "/Users/<USER>/Desktop/Coding/BVS/CrystalBall/insights-app/frontend/src/components/Home.js": "5", "/Users/<USER>/Desktop/Coding/BVS/CrystalBall/insights-app/frontend/src/components/StrategyDropdown.js": "6", "/Users/<USER>/Desktop/Coding/BVS/CrystalBall/insights-app/frontend/src/components/Fond.js": "7"}, {"size": 232, "mtime": 1753353443889, "results": "8", "hashOfConfig": "9"}, {"size": 362, "mtime": 1753351857690, "results": "10", "hashOfConfig": "9"}, {"size": 696, "mtime": 1753353443858, "results": "11", "hashOfConfig": "9"}, {"size": 20051, "mtime": 1754075460835, "results": "12", "hashOfConfig": "9"}, {"size": 828, "mtime": 1753366999527, "results": "13", "hashOfConfig": "9"}, {"size": 1712, "mtime": 1753905255870, "results": "14", "hashOfConfig": "9"}, {"size": 1284, "mtime": 1753353443859, "results": "15", "hashOfConfig": "9"}, {"filePath": "16", "messages": "17", "suppressedMessages": "18", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "wdhko1", {"filePath": "19", "messages": "20", "suppressedMessages": "21", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "22", "messages": "23", "suppressedMessages": "24", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "25", "messages": "26", "suppressedMessages": "27", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "28", "messages": "29", "suppressedMessages": "30", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "31", "messages": "32", "suppressedMessages": "33", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "34", "messages": "35", "suppressedMessages": "36", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "/Users/<USER>/Desktop/Coding/BVS/CrystalBall/insights-app/frontend/src/index.js", [], [], "/Users/<USER>/Desktop/Coding/BVS/CrystalBall/insights-app/frontend/src/reportWebVitals.js", [], [], "/Users/<USER>/Desktop/Coding/BVS/CrystalBall/insights-app/frontend/src/App.js", [], [], "/Users/<USER>/Desktop/Coding/BVS/CrystalBall/insights-app/frontend/src/components/Strategy.js", ["37", "38"], [], "/Users/<USER>/Desktop/Coding/BVS/CrystalBall/insights-app/frontend/src/components/Home.js", [], [], "/Users/<USER>/Desktop/Coding/BVS/CrystalBall/insights-app/frontend/src/components/StrategyDropdown.js", [], [], "/Users/<USER>/Desktop/Coding/BVS/CrystalBall/insights-app/frontend/src/components/Fond.js", [], [], {"ruleId": "39", "severity": 1, "message": "40", "line": 14, "column": 3, "nodeType": "41", "messageId": "42", "endLine": 14, "endColumn": 11}, {"ruleId": "43", "severity": 1, "message": "44", "line": 47, "column": 6, "nodeType": "45", "endLine": 47, "endColumn": 23, "suggestions": "46"}, "no-unused-vars", "'elements' is defined but never used.", "Identifier", "unusedVar", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'fetchStrategyData'. Either include it or remove the dependency array.", "ArrayExpression", ["47"], {"desc": "48", "fix": "49"}, "Update the dependencies array to be: [currentStrategy, fetchStrategyData]", {"range": "50", "text": "51"}, [1243, 1260], "[currentStrategy, fetchStrategyData]"]