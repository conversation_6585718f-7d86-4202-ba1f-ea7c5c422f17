{"version": 3, "file": "index.js", "sources": ["../src/utils.ts", "../src/chart.tsx", "../src/typedCharts.tsx"], "sourcesContent": ["import type { MouseEvent } from 'react';\nimport type {\n  ChartType,\n  ChartData,\n  DefaultDataPoint,\n  ChartDataset,\n  ChartOptions,\n  Chart,\n} from 'chart.js';\n\nimport type { ForwardedRef } from './types.js';\n\nconst defaultDatasetIdKey = 'label';\n\nexport function reforwardRef<T>(ref: ForwardedRef<T>, value: T) {\n  if (typeof ref === 'function') {\n    ref(value);\n  } else if (ref) {\n    ref.current = value;\n  }\n}\n\nexport function setOptions<\n  TType extends ChartType = ChartType,\n  TData = DefaultDataPoint<TType>,\n  TLabel = unknown,\n>(chart: Chart<TType, TData, TLabel>, nextOptions: ChartOptions<TType>) {\n  const options = chart.options;\n\n  if (options && nextOptions) {\n    Object.assign(options, nextOptions);\n  }\n}\n\nexport function setLabels<\n  TType extends ChartType = ChartType,\n  TData = DefaultDataPoint<TType>,\n  TLabel = unknown,\n>(\n  currentData: ChartData<TType, TData, TLabel>,\n  nextLabels: TLabel[] | undefined\n) {\n  currentData.labels = nextLabels;\n}\n\nexport function setDatasets<\n  TType extends ChartType = ChartType,\n  TData = DefaultDataPoint<TType>,\n  TLabel = unknown,\n>(\n  currentData: ChartData<TType, TData, TLabel>,\n  nextDatasets: ChartDataset<TType, TData>[],\n  datasetIdKey = defaultDatasetIdKey\n) {\n  const addedDatasets: ChartDataset<TType, TData>[] = [];\n\n  currentData.datasets = nextDatasets.map(\n    (nextDataset: Record<string, unknown>) => {\n      // given the new set, find it's current match\n      const currentDataset = currentData.datasets.find(\n        (dataset: Record<string, unknown>) =>\n          dataset[datasetIdKey] === nextDataset[datasetIdKey]\n      );\n\n      // There is no original to update, so simply add new one\n      if (\n        !currentDataset ||\n        !nextDataset.data ||\n        addedDatasets.includes(currentDataset)\n      ) {\n        return { ...nextDataset } as ChartDataset<TType, TData>;\n      }\n\n      addedDatasets.push(currentDataset);\n\n      Object.assign(currentDataset, nextDataset);\n\n      return currentDataset;\n    }\n  );\n}\n\nexport function cloneData<\n  TType extends ChartType = ChartType,\n  TData = DefaultDataPoint<TType>,\n  TLabel = unknown,\n>(data: ChartData<TType, TData, TLabel>, datasetIdKey = defaultDatasetIdKey) {\n  const nextData: ChartData<TType, TData, TLabel> = {\n    labels: [],\n    datasets: [],\n  };\n\n  setLabels(nextData, data.labels);\n  setDatasets(nextData, data.datasets, datasetIdKey);\n\n  return nextData;\n}\n\n/**\n * Get dataset from mouse click event\n * @param chart - Chart.js instance\n * @param event - Mouse click event\n * @returns Dataset\n */\nexport function getDatasetAtEvent(\n  chart: Chart,\n  event: MouseEvent<HTMLCanvasElement>\n) {\n  return chart.getElementsAtEventForMode(\n    event.nativeEvent,\n    'dataset',\n    { intersect: true },\n    false\n  );\n}\n\n/**\n * Get single dataset element from mouse click event\n * @param chart - Chart.js instance\n * @param event - Mouse click event\n * @returns Dataset\n */\nexport function getElementAtEvent(\n  chart: Chart,\n  event: MouseEvent<HTMLCanvasElement>\n) {\n  return chart.getElementsAtEventForMode(\n    event.nativeEvent,\n    'nearest',\n    { intersect: true },\n    false\n  );\n}\n\n/**\n * Get all dataset elements from mouse click event\n * @param chart - Chart.js instance\n * @param event - Mouse click event\n * @returns Dataset\n */\nexport function getElementsAtEvent(\n  chart: Chart,\n  event: MouseEvent<HTMLCanvasElement>\n) {\n  return chart.getElementsAtEventForMode(\n    event.nativeEvent,\n    'index',\n    { intersect: true },\n    false\n  );\n}\n", "import React, { useEffect, useRef, forwardRef } from 'react';\nimport { Chart as ChartJS } from 'chart.js';\nimport type { ChartType, DefaultDataPoint } from 'chart.js';\n\nimport type { ForwardedRef, ChartProps, BaseChartComponent } from './types.js';\nimport {\n  reforwardRef,\n  cloneData,\n  setOptions,\n  setLabels,\n  setDatasets,\n} from './utils.js';\n\nfunction ChartComponent<\n  TType extends ChartType = ChartType,\n  TData = DefaultDataPoint<TType>,\n  TLabel = unknown,\n>(\n  props: ChartProps<TType, TData, TLabel>,\n  ref: ForwardedRef<ChartJS<TType, TData, TLabel>>\n) {\n  const {\n    height = 150,\n    width = 300,\n    redraw = false,\n    datasetIdKey,\n    type,\n    data,\n    options,\n    plugins = [],\n    fallbackContent,\n    updateMode,\n    ...canvasProps\n  } = props;\n  const canvasRef = useRef<HTMLCanvasElement>(null);\n  const chartRef = useRef<ChartJS<TType, TData, TLabel> | null>(null);\n\n  const renderChart = () => {\n    if (!canvasRef.current) return;\n\n    chartRef.current = new ChartJS(canvasRef.current, {\n      type,\n      data: cloneData(data, datasetIdKey),\n      options: options && { ...options },\n      plugins,\n    });\n\n    reforwardRef(ref, chartRef.current);\n  };\n\n  const destroyChart = () => {\n    reforwardRef(ref, null);\n\n    if (chartRef.current) {\n      chartRef.current.destroy();\n      chartRef.current = null;\n    }\n  };\n\n  useEffect(() => {\n    if (!redraw && chartRef.current && options) {\n      setOptions(chartRef.current, options);\n    }\n  }, [redraw, options]);\n\n  useEffect(() => {\n    if (!redraw && chartRef.current) {\n      setLabels(chartRef.current.config.data, data.labels);\n    }\n  }, [redraw, data.labels]);\n\n  useEffect(() => {\n    if (!redraw && chartRef.current && data.datasets) {\n      setDatasets(chartRef.current.config.data, data.datasets, datasetIdKey);\n    }\n  }, [redraw, data.datasets]);\n\n  useEffect(() => {\n    if (!chartRef.current) return;\n\n    if (redraw) {\n      destroyChart();\n      setTimeout(renderChart);\n    } else {\n      chartRef.current.update(updateMode);\n    }\n  }, [redraw, options, data.labels, data.datasets, updateMode]);\n\n  useEffect(() => {\n    if (!chartRef.current) return;\n\n    destroyChart();\n    setTimeout(renderChart);\n  }, [type]);\n\n  useEffect(() => {\n    renderChart();\n\n    return () => destroyChart();\n  }, []);\n\n  return (\n    <canvas\n      ref={canvasRef}\n      role='img'\n      height={height}\n      width={width}\n      {...canvasProps}\n    >\n      {fallbackContent}\n    </canvas>\n  );\n}\n\nexport const Chart = forwardRef(ChartComponent) as BaseChartComponent;\n", "import React, { forwardRef } from 'react';\nimport {\n  Chart as <PERSON><PERSON><PERSON>,\n  <PERSON><PERSON><PERSON>roller,\n  <PERSON><PERSON><PERSON><PERSON><PERSON>,\n  Radar<PERSON><PERSON>roller,\n  <PERSON><PERSON>ut<PERSON><PERSON>roller,\n  PolarAreaController,\n  <PERSON><PERSON><PERSON><PERSON><PERSON>roller,\n  <PERSON><PERSON><PERSON>roller,\n  ScatterController,\n} from 'chart.js';\nimport type { ChartType, ChartComponentLike } from 'chart.js';\n\nimport type {\n  ChartProps,\n  ChartJSOrUndefined,\n  TypedChartComponent,\n} from './types.js';\nimport { Chart } from './chart.js';\n\nfunction createTypedChart<T extends ChartType>(\n  type: T,\n  registerables: ChartComponentLike\n) {\n  ChartJS.register(registerables);\n\n  return forwardRef<ChartJSOrUndefined<T>, Omit<ChartProps<T>, 'type'>>(\n    (props, ref) => <Chart {...props} ref={ref} type={type} />\n  ) as TypedChartComponent<T>;\n}\n\nexport const Line = /* #__PURE__ */ createTypedChart('line', LineController);\n\nexport const Bar = /* #__PURE__ */ createTypedChart('bar', BarController);\n\nexport const Radar = /* #__PURE__ */ createTypedChart('radar', RadarController);\n\nexport const Doughnut = /* #__PURE__ */ createTypedChart(\n  'doughnut',\n  DoughnutController\n);\n\nexport const PolarArea = /* #__PURE__ */ createTypedChart(\n  'polarArea',\n  PolarAreaController\n);\n\nexport const Bubble = /* #__PURE__ */ createTypedChart(\n  'bubble',\n  BubbleController\n);\n\nexport const Pie = /* #__PURE__ */ createTypedChart('pie', PieController);\n\nexport const Scatter = /* #__PURE__ */ createTypedChart(\n  'scatter',\n  ScatterController\n);\n"], "names": ["defaultDatasetIdKey", "reforwardRef", "ref", "value", "current", "setOptions", "chart", "nextOptions", "options", "Object", "assign", "<PERSON><PERSON><PERSON><PERSON>", "currentData", "<PERSON><PERSON><PERSON><PERSON>", "labels", "setDatasets", "nextDatasets", "datasetIdKey", "addedDatasets", "datasets", "map", "nextDataset", "currentDataset", "find", "dataset", "data", "includes", "push", "cloneData", "nextData", "getDatasetAtEvent", "event", "getElementsAtEventForMode", "nativeEvent", "intersect", "getElementAtEvent", "getElementsAtEvent", "ChartComponent", "props", "height", "width", "redraw", "type", "plugins", "fallback<PERSON><PERSON><PERSON>", "updateMode", "canvasProps", "canvasRef", "useRef", "chartRef", "<PERSON><PERSON><PERSON>", "ChartJS", "destroy<PERSON>hart", "destroy", "useEffect", "config", "setTimeout", "update", "canvas", "role", "Chart", "forwardRef", "createTypedChart", "registerables", "register", "Line", "LineController", "Bar", "BarController", "Radar", "RadarController", "Doughnut", "DoughnutController", "PolarArea", "PolarAreaController", "Bubble", "BubbleController", "Pie", "PieController", "<PERSON><PERSON><PERSON>", "ScatterController"], "mappings": ";;;AAYA,MAAMA,mBAAsB,GAAA,OAAA;AAErB,SAASC,YAAAA,CAAgBC,GAAoB,EAAEC,KAAQ,EAAA;IAC5D,IAAI,OAAOD,QAAQ,UAAY,EAAA;QAC7BA,GAAIC,CAAAA,KAAAA,CAAAA;AACN,KAAA,MAAO,IAAID,GAAK,EAAA;AACdA,QAAAA,GAAAA,CAAIE,OAAO,GAAGD,KAAAA;AAChB;AACF;AAEO,SAASE,UAAAA,CAIdC,KAAkC,EAAEC,WAAgC,EAAA;IACpE,MAAMC,OAAAA,GAAUF,MAAME,OAAO;AAE7B,IAAA,IAAIA,WAAWD,WAAa,EAAA;QAC1BE,MAAOC,CAAAA,MAAM,CAACF,OAASD,EAAAA,WAAAA,CAAAA;AACzB;AACF;AAEO,SAASI,SAAAA,CAKdC,WAA4C,EAC5CC,UAAgC,EAAA;AAEhCD,IAAAA,WAAAA,CAAYE,MAAM,GAAGD,UAAAA;AACvB;AAEO,SAASE,WAAAA,CAKdH,WAA4C,EAC5CI,YAA0C,EAAA;AAC1CC,IAAAA,IAAAA,YAAAA,GAAAA,SAAejB,CAAAA,MAAAA,GAAAA,CAAAA,IAAAA,SAAAA,CAAAA,CAAAA,CAAAA,KAAAA,KAAAA,CAAAA,GAAAA,SAAAA,CAAAA,CAAAA,CAAAA,GAAAA,mBAAAA;AAEf,IAAA,MAAMkB,gBAA8C,EAAE;AAEtDN,IAAAA,WAAAA,CAAYO,QAAQ,GAAGH,YAAaI,CAAAA,GAAG,CACrC,CAACC,WAAAA,GAAAA;;AAEC,QAAA,MAAMC,cAAiBV,GAAAA,WAAAA,CAAYO,QAAQ,CAACI,IAAI,CAC9C,CAACC,OACCA,GAAAA,OAAO,CAACP,YAAAA,CAAa,KAAKI,WAAW,CAACJ,YAAa,CAAA,CAAA;;QAIvD,IACE,CAACK,kBACD,CAACD,WAAAA,CAAYI,IAAI,IACjBP,aAAAA,CAAcQ,QAAQ,CAACJ,cACvB,CAAA,EAAA;YACA,OAAO;AAAE,gBAAA,GAAGD;AAAY,aAAA;AAC1B;AAEAH,QAAAA,aAAAA,CAAcS,IAAI,CAACL,cAAAA,CAAAA;QAEnBb,MAAOC,CAAAA,MAAM,CAACY,cAAgBD,EAAAA,WAAAA,CAAAA;QAE9B,OAAOC,cAAAA;AACT,KAAA,CAAA;AAEJ;AAEO,SAASM,UAIdH,IAAqC,EAAA;AAAER,IAAAA,IAAAA,YAAAA,GAAAA,SAAejB,CAAAA,MAAAA,GAAAA,CAAAA,IAAAA,SAAAA,CAAAA,CAAAA,CAAAA,KAAAA,KAAAA,CAAAA,GAAAA,SAAAA,CAAAA,CAAAA,CAAAA,GAAAA,mBAAAA;AACtD,IAAA,MAAM6B,QAA4C,GAAA;AAChDf,QAAAA,MAAAA,EAAQ,EAAE;AACVK,QAAAA,QAAAA,EAAU;AACZ,KAAA;IAEAR,SAAUkB,CAAAA,QAAAA,EAAUJ,KAAKX,MAAM,CAAA;IAC/BC,WAAYc,CAAAA,QAAAA,EAAUJ,IAAKN,CAAAA,QAAQ,EAAEF,YAAAA,CAAAA;IAErC,OAAOY,QAAAA;AACT;AAEA;;;;;AAKC,IACM,SAASC,iBACdxB,CAAAA,KAAY,EACZyB,KAAoC,EAAA;AAEpC,IAAA,OAAOzB,MAAM0B,yBAAyB,CACpCD,KAAME,CAAAA,WAAW,EACjB,SACA,EAAA;QAAEC,SAAW,EAAA;KACb,EAAA,KAAA,CAAA;AAEJ;AAEA;;;;;AAKC,IACM,SAASC,iBACd7B,CAAAA,KAAY,EACZyB,KAAoC,EAAA;AAEpC,IAAA,OAAOzB,MAAM0B,yBAAyB,CACpCD,KAAME,CAAAA,WAAW,EACjB,SACA,EAAA;QAAEC,SAAW,EAAA;KACb,EAAA,KAAA,CAAA;AAEJ;AAEA;;;;;AAKC,IACM,SAASE,kBACd9B,CAAAA,KAAY,EACZyB,KAAoC,EAAA;AAEpC,IAAA,OAAOzB,MAAM0B,yBAAyB,CACpCD,KAAME,CAAAA,WAAW,EACjB,OACA,EAAA;QAAEC,SAAW,EAAA;KACb,EAAA,KAAA,CAAA;AAEJ;;ACzIA,SAASG,cAAAA,CAKPC,KAAuC,EACvCpC,GAAgD,EAAA;IAEhD,MAAM,EACJqC,MAAS,GAAA,GAAG,EACZC,KAAAA,GAAQ,GAAG,EACXC,MAAS,GAAA,KAAK,EACdxB,YAAY,EACZyB,IAAI,EACJjB,IAAI,EACJjB,OAAO,EACPmC,OAAAA,GAAU,EAAE,EACZC,eAAe,EACfC,UAAU,EACV,GAAGC,WAAAA,EACJ,GAAGR,KAAAA;AACJ,IAAA,MAAMS,YAAYC,MAA0B,CAAA,IAAA,CAAA;AAC5C,IAAA,MAAMC,WAAWD,MAA6C,CAAA,IAAA,CAAA;AAE9D,IAAA,MAAME,WAAc,GAAA,IAAA;QAClB,IAAI,CAACH,SAAU3C,CAAAA,OAAO,EAAE;AAExB6C,QAAAA,QAAAA,CAAS7C,OAAO,GAAG,IAAI+C,OAAQJ,CAAAA,SAAAA,CAAU3C,OAAO,EAAE;AAChDsC,YAAAA,IAAAA;AACAjB,YAAAA,IAAAA,EAAMG,UAAUH,IAAMR,EAAAA,YAAAA,CAAAA;AACtBT,YAAAA,OAAAA,EAASA,OAAW,IAAA;AAAE,gBAAA,GAAGA;AAAQ,aAAA;AACjCmC,YAAAA;AACF,SAAA,CAAA;QAEA1C,YAAaC,CAAAA,GAAAA,EAAK+C,SAAS7C,OAAO,CAAA;AACpC,KAAA;AAEA,IAAA,MAAMgD,YAAe,GAAA,IAAA;AACnBnD,QAAAA,YAAAA,CAAaC,GAAK,EAAA,IAAA,CAAA;QAElB,IAAI+C,QAAAA,CAAS7C,OAAO,EAAE;YACpB6C,QAAS7C,CAAAA,OAAO,CAACiD,OAAO,EAAA;AACxBJ,YAAAA,QAAAA,CAAS7C,OAAO,GAAG,IAAA;AACrB;AACF,KAAA;IAEAkD,SAAU,CAAA,IAAA;AACR,QAAA,IAAI,CAACb,MAAAA,IAAUQ,QAAS7C,CAAAA,OAAO,IAAII,OAAS,EAAA;YAC1CH,UAAW4C,CAAAA,QAAAA,CAAS7C,OAAO,EAAEI,OAAAA,CAAAA;AAC/B;KACC,EAAA;AAACiC,QAAAA,MAAAA;AAAQjC,QAAAA;AAAQ,KAAA,CAAA;IAEpB8C,SAAU,CAAA,IAAA;AACR,QAAA,IAAI,CAACb,MAAAA,IAAUQ,QAAS7C,CAAAA,OAAO,EAAE;YAC/BO,SAAUsC,CAAAA,QAAAA,CAAS7C,OAAO,CAACmD,MAAM,CAAC9B,IAAI,EAAEA,KAAKX,MAAM,CAAA;AACrD;KACC,EAAA;AAAC2B,QAAAA,MAAAA;AAAQhB,QAAAA,IAAAA,CAAKX;AAAO,KAAA,CAAA;IAExBwC,SAAU,CAAA,IAAA;AACR,QAAA,IAAI,CAACb,MAAUQ,IAAAA,QAAAA,CAAS7C,OAAO,IAAIqB,IAAAA,CAAKN,QAAQ,EAAE;YAChDJ,WAAYkC,CAAAA,QAAAA,CAAS7C,OAAO,CAACmD,MAAM,CAAC9B,IAAI,EAAEA,IAAKN,CAAAA,QAAQ,EAAEF,YAAAA,CAAAA;AAC3D;KACC,EAAA;AAACwB,QAAAA,MAAAA;AAAQhB,QAAAA,IAAAA,CAAKN;AAAS,KAAA,CAAA;IAE1BmC,SAAU,CAAA,IAAA;QACR,IAAI,CAACL,QAAS7C,CAAAA,OAAO,EAAE;AAEvB,QAAA,IAAIqC,MAAQ,EAAA;AACVW,YAAAA,YAAAA,EAAAA;YACAI,UAAWN,CAAAA,WAAAA,CAAAA;SACN,MAAA;YACLD,QAAS7C,CAAAA,OAAO,CAACqD,MAAM,CAACZ,UAAAA,CAAAA;AAC1B;KACC,EAAA;AAACJ,QAAAA,MAAAA;AAAQjC,QAAAA,OAAAA;AAASiB,QAAAA,IAAAA,CAAKX,MAAM;AAAEW,QAAAA,IAAAA,CAAKN,QAAQ;AAAE0B,QAAAA;AAAW,KAAA,CAAA;IAE5DS,SAAU,CAAA,IAAA;QACR,IAAI,CAACL,QAAS7C,CAAAA,OAAO,EAAE;AAEvBgD,QAAAA,YAAAA,EAAAA;QACAI,UAAWN,CAAAA,WAAAA,CAAAA;KACV,EAAA;AAACR,QAAAA;AAAK,KAAA,CAAA;IAETY,SAAU,CAAA,IAAA;AACRJ,QAAAA,WAAAA,EAAAA;AAEA,QAAA,OAAO,IAAME,YAAAA,EAAAA;AACf,KAAA,EAAG,EAAE,CAAA;AAEL,IAAA,qBACE,KAACM,CAAAA,aAAAA,CAAAA,QAAAA,EAAAA;QACCxD,GAAK6C,EAAAA,SAAAA;QACLY,IAAK,EAAA,KAAA;QACLpB,MAAQA,EAAAA,MAAAA;QACRC,KAAOA,EAAAA,KAAAA;AACN,QAAA,GAAGM;AAEHF,KAAAA,EAAAA,eAAAA,CAAAA;AAGP;AAEO,MAAMgB,KAAQC,iBAAAA,UAAAA,CAAWxB,cAAsC;;AC7FtE,SAASyB,gBAAAA,CACPpB,IAAO,EACPqB,aAAiC,EAAA;AAEjCZ,IAAAA,OAAAA,CAAQa,QAAQ,CAACD,aAAAA,CAAAA;AAEjB,IAAA,qBAAOF,UACL,CAAA,CAACvB,KAAOpC,EAAAA,GAAAA,iBAAQ,KAAC0D,CAAAA,aAAAA,CAAAA,KAAAA,EAAAA;AAAO,YAAA,GAAGtB,KAAK;YAAEpC,GAAKA,EAAAA,GAAAA;YAAKwC,IAAMA,EAAAA;;AAEtD;MAEauB,IAAO,mBAAgBH,gBAAAA,CAAiB,QAAQI,cAAgB;MAEhEC,GAAM,mBAAgBL,gBAAAA,CAAiB,OAAOM,aAAe;MAE7DC,KAAQ,mBAAgBP,gBAAAA,CAAiB,SAASQ,eAAiB;MAEnEC,QAAW,mBAAgBT,gBAAAA,CACtC,YACAU,kBACA;MAEWC,SAAY,mBAAgBX,gBAAAA,CACvC,aACAY,mBACA;MAEWC,MAAS,mBAAgBb,gBAAAA,CACpC,UACAc,gBACA;MAEWC,GAAM,mBAAgBf,gBAAAA,CAAiB,OAAOgB,aAAe;MAE7DC,OAAU,mBAAgBjB,gBAAAA,CACrC,WACAkB,iBACA;;;;"}