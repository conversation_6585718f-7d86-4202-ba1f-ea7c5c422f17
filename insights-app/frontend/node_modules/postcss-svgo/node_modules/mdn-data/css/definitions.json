{"groupList": {"enum": ["Basic Selectors", "Combinators", "Compositing and Blending", "CSS Angles", "CSS Animations", "CSS Backgrounds and Borders", "CSS Box Model", "CSS Box Alignment", "CSS Break", "CSS Charsets", "CSS Color", "CSS Columns", "CSS Conditional Rules", "CSS Containment", "CSS Counter Styles", "CSS Device Adaptation", "CSS Display", "CSS Flexible Box Layout", "CSS Flexible Lengths", "CSS Fonts", "CSS Fragmentation", "CSS Frequencies", "CSS Generated Content", "CSS Grid Layout", "CSS Houdini", "CSS Images", "CSS Inline", "CSS Lengths", "CSS Lists and Counters", "CSS Logical Properties", "CSS Masking", "CSS Miscellaneous", "CSS Motion Path", "CSS Namespaces", "CSS Overflow", "CSS Pages", "CSS Positioning", "CSS Regions", "CSS Resolutions", "CSS Ruby", "CSS Scroll Anchoring", "CSS Scrollbars", "CSS Scroll Snap", "CSS Shadow Parts", "CSS Shapes", "CSS Speech", "CSS Table", "CSS Text", "CSS Text Decoration", "CSS Times", "CSS Transforms", "CSS Transitions", "CSS Types", "CSS Units", "CSS Basic User Interface", "CSS Variables", "CSS Will Change", "CSS Writing Modes", "CSSOM View", "Filter Effects", "Grouping Selectors", "MathML", "Media Queries", "Microsoft Extensions", "Mozilla Extensions", "Pointer Events", "<PERSON><PERSON><PERSON>", "Pseudo-classes", "Pseudo-elements", "Selectors", "WebKit Extensions"]}}