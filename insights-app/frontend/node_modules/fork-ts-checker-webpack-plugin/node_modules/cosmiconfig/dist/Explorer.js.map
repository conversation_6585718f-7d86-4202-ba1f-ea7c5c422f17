{"version": 3, "sources": ["../src/Explorer.ts"], "names": ["Explorer", "ExplorerBase", "constructor", "options", "search", "searchFrom", "process", "cwd", "startDirectory", "result", "searchFromDirectory", "dir", "absoluteDir", "path", "resolve", "run", "searchDirectory", "nextDir", "nextDirectoryToSearch", "transformResult", "config", "transform", "searchCache", "searchPlaces", "place", "placeResult", "loadSearchPlace", "shouldSearchStopWithResult", "filepath", "join", "fileContents", "createCosmiconfigResult", "loadFileContent", "content", "trim", "undefined", "loader", "getLoaderEntryForFile", "loaderResult", "fileContent", "loadedContentToCosmiconfigResult", "load", "validate<PERSON>ile<PERSON><PERSON>", "absoluteFilePath", "runLoad", "throwNotFound", "loadCache"], "mappings": ";;;;;;;AAAA;;AACA;;AACA;;AACA;;AACA;;;;;;AAGA,MAAMA,QAAN,SAAuBC,0BAAvB,CAAqD;AAC5CC,EAAAA,WAAP,CAAmBC,OAAnB,EAA6C;AAC3C,UAAMA,OAAN;AACD;;AAED,QAAaC,MAAb,CACEC,UAAkB,GAAGC,OAAO,CAACC,GAAR,EADvB,EAE8B;AAC5B,UAAMC,cAAc,GAAG,MAAM,gCAAaH,UAAb,CAA7B;AACA,UAAMI,MAAM,GAAG,MAAM,KAAKC,mBAAL,CAAyBF,cAAzB,CAArB;AAEA,WAAOC,MAAP;AACD;;AAED,QAAcC,mBAAd,CAAkCC,GAAlC,EAA2E;AACzE,UAAMC,WAAW,GAAGC,cAAKC,OAAL,CAAaR,OAAO,CAACC,GAAR,EAAb,EAA4BI,GAA5B,CAApB;;AAEA,UAAMI,GAAG,GAAG,YAAwC;AAClD,YAAMN,MAAM,GAAG,MAAM,KAAKO,eAAL,CAAqBJ,WAArB,CAArB;AACA,YAAMK,OAAO,GAAG,KAAKC,qBAAL,CAA2BN,WAA3B,EAAwCH,MAAxC,CAAhB;;AAEA,UAAIQ,OAAJ,EAAa;AACX,eAAO,KAAKP,mBAAL,CAAyBO,OAAzB,CAAP;AACD;;AAED,YAAME,eAAe,GAAG,MAAM,KAAKC,MAAL,CAAYC,SAAZ,CAAsBZ,MAAtB,CAA9B;AAEA,aAAOU,eAAP;AACD,KAXD;;AAaA,QAAI,KAAKG,WAAT,EAAsB;AACpB,aAAO,gCAAa,KAAKA,WAAlB,EAA+BV,WAA/B,EAA4CG,GAA5C,CAAP;AACD;;AAED,WAAOA,GAAG,EAAV;AACD;;AAED,QAAcC,eAAd,CAA8BL,GAA9B,EAAuE;AAAA;AAAA;;AAAA;;AAAA;AACrE,0CAA0B,KAAKS,MAAL,CAAYG,YAAtC,oLAAoD;AAAA,cAAnCC,KAAmC;AAClD,cAAMC,WAAW,GAAG,MAAM,KAAKC,eAAL,CAAqBf,GAArB,EAA0Ba,KAA1B,CAA1B;;AAEA,YAAI,KAAKG,0BAAL,CAAgCF,WAAhC,MAAiD,IAArD,EAA2D;AACzD,iBAAOA,WAAP;AACD;AACF,OAPoE,CASrE;;AATqE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;AAUrE,WAAO,IAAP;AACD;;AAED,QAAcC,eAAd,CACEf,GADF,EAEEa,KAFF,EAG8B;AAC5B,UAAMI,QAAQ,GAAGf,cAAKgB,IAAL,CAAUlB,GAAV,EAAea,KAAf,CAAjB;;AACA,UAAMM,YAAY,GAAG,MAAM,wBAASF,QAAT,CAA3B;AAEA,UAAMnB,MAAM,GAAG,MAAM,KAAKsB,uBAAL,CAA6BH,QAA7B,EAAuCE,YAAvC,CAArB;AAEA,WAAOrB,MAAP;AACD;;AAED,QAAcuB,eAAd,CACEJ,QADF,EAEEK,OAFF,EAG8B;AAC5B,QAAIA,OAAO,KAAK,IAAhB,EAAsB;AACpB,aAAO,IAAP;AACD;;AACD,QAAIA,OAAO,CAACC,IAAR,OAAmB,EAAvB,EAA2B;AACzB,aAAOC,SAAP;AACD;;AACD,UAAMC,MAAM,GAAG,KAAKC,qBAAL,CAA2BT,QAA3B,CAAf;AACA,UAAMU,YAAY,GAAG,MAAMF,MAAM,CAACR,QAAD,EAAWK,OAAX,CAAjC;AACA,WAAOK,YAAP;AACD;;AAED,QAAcP,uBAAd,CACEH,QADF,EAEEK,OAFF,EAG8B;AAC5B,UAAMM,WAAW,GAAG,MAAM,KAAKP,eAAL,CAAqBJ,QAArB,EAA+BK,OAA/B,CAA1B;AACA,UAAMxB,MAAM,GAAG,KAAK+B,gCAAL,CAAsCZ,QAAtC,EAAgDW,WAAhD,CAAf;AAEA,WAAO9B,MAAP;AACD;;AAED,QAAagC,IAAb,CAAkBb,QAAlB,EAAgE;AAC9D,SAAKc,gBAAL,CAAsBd,QAAtB;;AACA,UAAMe,gBAAgB,GAAG9B,cAAKC,OAAL,CAAaR,OAAO,CAACC,GAAR,EAAb,EAA4BqB,QAA5B,CAAzB;;AAEA,UAAMgB,OAAO,GAAG,YAAwC;AACtD,YAAMd,YAAY,GAAG,MAAM,wBAASa,gBAAT,EAA2B;AACpDE,QAAAA,aAAa,EAAE;AADqC,OAA3B,CAA3B;AAIA,YAAMpC,MAAM,GAAG,MAAM,KAAKsB,uBAAL,CACnBY,gBADmB,EAEnBb,YAFmB,CAArB;AAKA,YAAMX,eAAe,GAAG,MAAM,KAAKC,MAAL,CAAYC,SAAZ,CAAsBZ,MAAtB,CAA9B;AAEA,aAAOU,eAAP;AACD,KAbD;;AAeA,QAAI,KAAK2B,SAAT,EAAoB;AAClB,aAAO,gCAAa,KAAKA,SAAlB,EAA6BH,gBAA7B,EAA+CC,OAA/C,CAAP;AACD;;AAED,WAAOA,OAAO,EAAd;AACD;;AA/GkD", "sourcesContent": ["import path from 'path';\nimport { ExplorerBase } from './ExplorerBase';\nimport { readFile } from './readFile';\nimport { cacheWrapper } from './cacheWrapper';\nimport { getDirectory } from './getDirectory';\nimport { CosmiconfigResult, ExplorerOptions, LoadedFileContent } from './types';\n\nclass Explorer extends ExplorerBase<ExplorerOptions> {\n  public constructor(options: ExplorerOptions) {\n    super(options);\n  }\n\n  public async search(\n    searchFrom: string = process.cwd(),\n  ): Promise<CosmiconfigResult> {\n    const startDirectory = await getDirectory(searchFrom);\n    const result = await this.searchFromDirectory(startDirectory);\n\n    return result;\n  }\n\n  private async searchFromDirectory(dir: string): Promise<CosmiconfigResult> {\n    const absoluteDir = path.resolve(process.cwd(), dir);\n\n    const run = async (): Promise<CosmiconfigResult> => {\n      const result = await this.searchDirectory(absoluteDir);\n      const nextDir = this.nextDirectoryToSearch(absoluteDir, result);\n\n      if (nextDir) {\n        return this.searchFromDirectory(nextDir);\n      }\n\n      const transformResult = await this.config.transform(result);\n\n      return transformResult;\n    };\n\n    if (this.searchCache) {\n      return cacheWrapper(this.searchCache, absoluteDir, run);\n    }\n\n    return run();\n  }\n\n  private async searchDirectory(dir: string): Promise<CosmiconfigResult> {\n    for await (const place of this.config.searchPlaces) {\n      const placeResult = await this.loadSearchPlace(dir, place);\n\n      if (this.shouldSearchStopWithResult(placeResult) === true) {\n        return placeResult;\n      }\n    }\n\n    // config not found\n    return null;\n  }\n\n  private async loadSearchPlace(\n    dir: string,\n    place: string,\n  ): Promise<CosmiconfigResult> {\n    const filepath = path.join(dir, place);\n    const fileContents = await readFile(filepath);\n\n    const result = await this.createCosmiconfigResult(filepath, fileContents);\n\n    return result;\n  }\n\n  private async loadFileContent(\n    filepath: string,\n    content: string | null,\n  ): Promise<LoadedFileContent> {\n    if (content === null) {\n      return null;\n    }\n    if (content.trim() === '') {\n      return undefined;\n    }\n    const loader = this.getLoaderEntryForFile(filepath);\n    const loaderResult = await loader(filepath, content);\n    return loaderResult;\n  }\n\n  private async createCosmiconfigResult(\n    filepath: string,\n    content: string | null,\n  ): Promise<CosmiconfigResult> {\n    const fileContent = await this.loadFileContent(filepath, content);\n    const result = this.loadedContentToCosmiconfigResult(filepath, fileContent);\n\n    return result;\n  }\n\n  public async load(filepath: string): Promise<CosmiconfigResult> {\n    this.validateFilePath(filepath);\n    const absoluteFilePath = path.resolve(process.cwd(), filepath);\n\n    const runLoad = async (): Promise<CosmiconfigResult> => {\n      const fileContents = await readFile(absoluteFilePath, {\n        throwNotFound: true,\n      });\n\n      const result = await this.createCosmiconfigResult(\n        absoluteFilePath,\n        fileContents,\n      );\n\n      const transformResult = await this.config.transform(result);\n\n      return transformResult;\n    };\n\n    if (this.loadCache) {\n      return cacheWrapper(this.loadCache, absoluteFilePath, runLoad);\n    }\n\n    return runLoad();\n  }\n}\n\nexport { Explorer };\n"], "file": "Explorer.js"}